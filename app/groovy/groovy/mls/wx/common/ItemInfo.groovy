package groovy.mls.wx.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seo.domain.SeoDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-商品信息
 */

@Translator(id = "itemInfo")
class ItemInfo implements IThreeDependTranslator<ItemBaseDO, ExtraDO, SeoDO, ItemInfoVO> {

    static class ItemInfoVO {
        String desc;
        String title;
        String itemId;
        Boolean isFaved;
        Integer cFav;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer state;
        /**
         * 开售时间，待开售商品
         */
        Long saleStartTime;
        /**
         * 商品类型，0为普通商品，1为预售商品
         */
        Integer saleType;

        Map<String, String> seo;

        /**
         * 暂时加上，之后去掉
         */
        String lowPrice;
        String highPrice;
        String lowNowPrice;
        String highNowPrice;

        ItemInfoVO(ItemBaseDO item, ExtraDO extra, SeoDO seo) {
            this.desc = item?.desc;
            this.title = item?.title;
            this.itemId = item?.iid;
            this.isFaved = item?.isFaved;
            this.cFav = item?.cFav;
            this.state = item?.state;
            this.saleType = item?.saleType;
            this.saleStartTime = extra?.onSaleTime;
            this.seo = seo;

            this.lowPrice = item?.lowPrice;
            this.highPrice = item?.highPrice;
            this.lowNowPrice = item?.lowNowPrice;
            this.highNowPrice = item?.highNowPrice;
        }
    }

    @Override
    ItemInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, SeoDO seo) {
        if (!itemBase) {
            return null;
        }
        ItemInfoVO itemInfo = new ItemInfoVO(itemBase, extra, seo);
        return itemInfo;
    }
}