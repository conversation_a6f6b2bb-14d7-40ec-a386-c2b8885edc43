package groovy.mls.wx.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "skuInfo")
public class SkuInfo implements IOneDependTranslator<SkuDO, SkuVO> {


    static class SkuVO {
        String title;
        List<SkuData> skus
        List<PropInfo> props;
        String styleKey;
        String sizeKey;
        String priceRange;
        String defaultPrice;
        Integer totalStock;
        Boolean canInstallment;
        String mainPriceStr;
        String subPriceStr;
        Integer limitTotalStock;
        Integer limitNum;
        String limitDesc;
    }

    @Override
    SkuVO translate(SkuDO input1) {
        if (!input1) {
            return null;
        }
        return new SkuVO(
                skus: input1?.skus,
                props: input1?.props,
                styleKey: input1?.styleKey,
                sizeKey: input1?.sizeKey,
                priceRange: input1?.priceRange,
                defaultPrice: input1?.defaultPrice,
                totalStock: input1?.totalStock,
                canInstallment: input1?.canInstallment,
                mainPriceStr: input1?.mainPriceStr,
                subPriceStr: input1?.subPriceStr,
                limitTotalStock: input1?.limitTotalStock,
                limitNum: input1?.limitNum,
                limitDesc: input1?.limitDesc
        );
    }
}