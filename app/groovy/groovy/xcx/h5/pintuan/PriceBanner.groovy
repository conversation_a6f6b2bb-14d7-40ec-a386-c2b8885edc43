package groovy.xcx.h5.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "priceBanner", defaultValue = DefaultType.NULL)
class PriceBanner implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}

//import com.mogujie.detail.core.annotation.Translator
//
///**
// * Create by changsheng on 2018/9/11 22:52
// * Package groovy.xcx.h5.pintuan
// */
//import com.mogujie.detail.core.translator.ITwoDependTranslator
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import groovy.xcx.h5.base.ItemPriceVO
//import groovy.xcx.h5.base.PriceBannerVO
//import groovy.xcx.h5.base.constant.CountdownMaitId
//import groovy.xcx.h5.base.constant.CountdownType
//
//@Translator(id = "priceBanner")
//class PriceBanner implements ITwoDependTranslator<ItemBaseDO, PinTuanDO, PriceBannerVO> {
//
//
//    @Override
//    PriceBannerVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan) {
//        ItemPriceVO itemPrice = new PintuanPrice().translate(itemBase, pinTuan)
//
//        Boolean isExpire = pinTuan?.isExpire()
//        Long now = System.currentTimeSeconds()
//        Long startTime = pinTuan?.startTime
//        Long endTime = pinTuan?.endTime
//        PriceBannerVO priceBanner
//
//        if (!pinTuan) {
//            priceBanner = new PriceBannerVO()
//        } else if (now < startTime) {
//            priceBanner = new PriceBannerVO()
//            priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, startTime, endTime, CountdownMaitId.CHANNEL_PINTUAN)
//        } else if (now > startTime && !isExpire) {
//            priceBanner = new PriceBannerVO()
//            priceBanner.initPriceBanner(CountdownType.IN, itemPrice, startTime, endTime, CountdownMaitId.CHANNEL_PINTUAN)
//        } else {
//            priceBanner = new PriceBannerVO()
//        }
//
//        return priceBanner
//    }
//}