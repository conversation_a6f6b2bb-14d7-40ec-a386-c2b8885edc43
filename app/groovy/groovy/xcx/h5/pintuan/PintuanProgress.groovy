 package groovy.xcx.h5.pintuan

 import com.mogujie.detail.core.annotation.Translator
 import com.mogujie.detail.core.constant.DefaultType
 import com.mogujie.detail.core.translator.IOneDependTranslator
 import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


 @Translator(id = "pintuanProgress", defaultValue = DefaultType.NULL)
 class PintuanProgress implements IOneDependTranslator<ItemBaseDO, Object> {

     @Override
     Object translate(ItemBaseDO input1) {
         return null
     }
 }

// /**
//  * Created by changsheng on 08/01/2018.
//  */
// import com.mogujie.detail.core.annotation.Translator
// import com.mogujie.detail.core.translator.IOneDependTranslator
// import com.mogujie.detail.module.pintuan.domain.PinTuanDO
// import com.mogujie.enzo.api.dto.ProgressDTO
// import groovy.xcx.h5.base.ProgressVO

// @Translator(id = "pintuanProgress")
// class PintuanProgress implements IOneDependTranslator<PinTuanDO, ProgressVO> {

//     @Override
//     ProgressVO translate(PinTuanDO pinTuan) {
//         ProgressDTO progressDTO = pinTuan?.progressDTO
//         if (!progressDTO || progressDTO?.progress == null) {
//             return null
//         }

//         return new ProgressVO(
//                 progress: Integer.parseInt(progressDTO?.progress),
//                 leftTitle: "只差${progressDTO?.leftNum}个团",
//                 rightTitle: "${progressDTO?.openAwardNum}个团满开奖",
//                 leftTextColor: "#999999",
//                 rightTextColor: "#9C89FF",
//                 background: "linear-gradient(to right, #9C89FF, #FF5682)"
//         )
//     }
// }