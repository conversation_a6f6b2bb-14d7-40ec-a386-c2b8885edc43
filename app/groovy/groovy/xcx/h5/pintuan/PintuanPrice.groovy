package groovy.xcx.h5.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "pintuanPrice", defaultValue = DefaultType.NULL)
class PintuanPrice implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}
//import com.mogujie.detail.core.annotation.Translator
//import com.mogujie.detail.core.translator.ITwoDependTranslator
//import com.mogujie.detail.core.util.NumUtil
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import com.mogujie.detail.module.sku.domain.SkuData
//import groovy.xcx.h5.base.ItemPriceVO
//import groovy.xcx.h5.base.PriceTagVO
//import groovy.xcx.h5.base.Util
//
///**
// * Created by changsheng on 23/03/2017.
// */
//
//@Translator(id = "pintuanPrice")
//class PintuanPrice implements ITwoDependTranslator<ItemBaseDO, PinTuanDO, ItemPriceVO> {
//
//    @Override
//    ItemPriceVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan) {
//
//        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
//
//        if (!itemBase) {
//            return null
//        } else if (pinTuan && pinTuan?.skuInfo) {
//            // 拼团
//
//            Integer tuanType = pinTuan?.tuanType
//
//            itemPrice.with {
//                nowPrice = pinTuan?.skuInfo?.lowNowPrice
//                oldPrice = getOldPriceForce(itemBase)
//                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
//
//                Double savedPrice
//                List<SkuData> skus = pinTuan?.skuInfo?.skus
//                // skus中现价的最低价
//                Integer lowNowPrice
//                // skus中原价的最高价
//                Integer lowOldPrice
//                if (skus && skus.size() > 0) {
//                    lowNowPrice = skus?.get(0)?.nowprice
//                    lowOldPrice = skus?.get(0)?.price
//
//                    skus?.each {
//                        Integer nowprice = it?.nowprice
//                        Integer price = it?.price
//                        if (nowprice && nowprice < lowNowPrice) {
//                            lowNowPrice = nowprice
//                        }
//                        if (price && price < lowOldPrice) {
//                            lowOldPrice = price
//                        }
//                    }
//                    savedPrice = lowNowPrice && lowOldPrice ? (lowOldPrice - lowNowPrice) : null
//                }
//
//                if (tuanType == 3 || tuanType == 8) {
//                    priceTags = [
//                            new PriceTagVO(
//                                    text: "抽奖团"
//                            )
//                    ]
//                } else {
//                    priceTags = [
//                            new PriceTagVO(
//                                    text: (pinTuan?.tuanNum as String) + "人拼团"
//                            )
//                    ]
//                    if (savedPrice && savedPrice > 0 && highNowPrice == null) {
//                        priceTags.add(
//                                new PriceTagVO(
//                                        text: "拼团立省" + currency + NumUtil.formatNum(savedPrice / 100D)
//                                )
//                        )
//                    }
//                }
//
//            }
//        } else {
//            // 没有拼团信息
//            itemPrice.with {
//                setNowPriceByRange(itemBase)
//                setOldPriceByRange(itemBase)
//
//                // 预热期展示预热价
//                Long now = System.currentTimeSeconds()
//                Long startTime = pinTuan?.startTime
//                if (now < startTime && pinTuan?.lowActivityPrice != null) {
//                    setNowPriceByRange(pinTuan?.lowActivityPrice, pinTuan?.highActivityPrice)
//                }
//            }
//        }
//
//        Util.setEsiDataForPrice(itemPrice)
//
//        return itemPrice
//    }
//}