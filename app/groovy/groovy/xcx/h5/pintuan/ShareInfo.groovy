package groovy.xcx.h5.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "shareInfo", defaultValue = DefaultType.NULL)
class ShareInfo implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}

//import com.mogujie.detail.core.annotation.Translator
//import com.mogujie.detail.core.translator.ITwoDependTranslator
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import groovy.xcx.h5.base.PintuanInfoVO
//import groovy.xcx.h5.base.ShareInfoVO
//
//@Translator(id = "shareInfo")
//class ShareInfo implements ITwoDependTranslator<ItemBaseDO, PinTuanDO, ShareInfoVO> {
//
//    @Override
//    ShareInfoVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan) {
//        if (itemBase == null) {
//            return null
//        }
//
//        PintuanInfoVO pintuanInfo = new PintuanInfoVO(pinTuan, itemBase)
//        Integer pintuanState = pintuanInfo?.state
//
//        // 排除掉新人专享的情况
//        Boolean isNeedShareIntegral = (pintuanState == 2 || pintuanState == 5) && !pintuanInfo?.isNew
//
//        return new ShareInfoVO(isNeedShareIntegral)
//    }
//}