package groovy.xcx.h5.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "bottomBar", defaultValue = DefaultType.NULL)
class BottomBar implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}

//import com.mogujie.detail.core.annotation.Translator
//import com.mogujie.detail.core.translator.IThreeDependTranslator
//import com.mogujie.detail.core.util.ImageUtil
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import com.mogujie.detail.module.shop.domain.ShopDO
//import groovy.xcx.h5.base.BottomBarButtonPopUpVO
//import groovy.xcx.h5.base.BottomBarButtonVO
//import groovy.xcx.h5.base.BottomBarVO
//import groovy.xcx.h5.base.PintuanInfoVO
//
//@Translator(id = "bottomBar")
//class BottomBar implements IThreeDependTranslator<ItemBaseDO, PinTuanDO, ShopDO, BottomBarVO> {
//
//    @Override
//    BottomBarVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan, ShopDO shop) {
//
//        PintuanInfoVO pintuanInfo = new PintuanInfoVO(pinTuan, itemBase)
//
//        List<BottomBarButtonVO> buttons = new ArrayList<>()
//        String buttonTipsText = ""
//
//        Integer tuanType = pintuanInfo?.tuanType
//        Integer tuanNum = pintuanInfo?.tuanNum
//        Integer pintuanState = pintuanInfo?.state
//        String currency = pintuanInfo?.currency
//        String normalPrice = pintuanInfo?.normalPrice
//        String pintuanPrice = pintuanInfo?.pintuanPrice
//
//        // 抽奖团，底部bar只有两个按钮
//        if (tuanType == 3 || tuanType == 8) {
//            // 最左边加个客服
//            buttons.add(new BottomBarButtonVO(
//                    type: "im",
//                    name: "im",
//                    text: "客服",
//            ))
//
//            if (pintuanState == 2) {
//                buttonTipsText = "拼团抢购即将开始，记得来哦"
//            }
//
//            if (pintuanState in [1, 3, 4]) {
//                // 查看抽奖结果
//                // 此时不展示收藏店铺的tips
//                buttons.add(new BottomBarButtonVO(
//                        type: "button",
//                        name: "shopFav",
//
//                ))
//                buttons.add(new BottomBarButtonVO(
//                        type: "button",
//                        name: "follow",
//                        prefix: "关注",
//                        text: "服务通知",
//                        nextText: "查看抽奖结果"
//                ))
//            } else {
//                // 抽奖中或未开始
//                buttons.add(new BottomBarButtonVO(
//                        type: "button",
//                        name: "shopFav",
//                        popup: new BottomBarButtonPopUpVO(
//                                image: ImageUtil.img("/mlcdn/c45406/170703_19iii8h9lg76baae17709ae0k2adg_260x72.png")
//                        )
//                ))
//                buttons.add(new BottomBarButtonVO(
//                        type: "button",
//                        name: "buy",
//                        text: "立即参团参与抽奖",
//                        nextText: "${tuanNum}人拼团",
//                        isDisabled: pintuanState != 5
//                ))
//            }
//        } else {
//            // 其他拼团类型
//            buttons.add(new BottomBarButtonVO(
//                    type: "icon",
//                    name: "shop",
//                    text: "店铺"
//            ))
//
//            buttons.add(new BottomBarButtonVO(
//                    type: "im",
//                    name: "im",
//                    text: "客服",
//            ))
//
//            Boolean isTuanDisabled
//            Boolean isNormalDisabled
//
//            switch (pintuanState) {
//                case 1:
//                    isTuanDisabled = true
//                    isNormalDisabled = true
//                    buttonTipsText = "商品已下架，快去挑挑其他商品哦～"
//                    break
//                case 2:
//                    isTuanDisabled = true
//                    isNormalDisabled = false
//                    buttonTipsText = "拼团抢购即将开始，记得来哦"
//                    break
//                case 3:
//                    isTuanDisabled = true
//                    isNormalDisabled = false
//                    buttonTipsText = "拼团抢购结束，您还可以单独买哦"
//                    break
//                case 4:
//                    isTuanDisabled = true
//                    isNormalDisabled = false
//                    buttonTipsText = "拼团商品已抢光，您还可以单独买哦"
//                    break
//                case 5:
//                    isTuanDisabled = false
//                    isNormalDisabled = false
//                    buttonTipsText = ""
//                    break
//                default:
//                    isTuanDisabled = true
//                    isNormalDisabled = false
//                    buttonTipsText = "拼团抢购结束，您还可以单独买哦"
//                    break
//            }
//
//            buttons.add(new BottomBarButtonVO(
//                    type: "button",
//                    name: "singleBuyRedirect",
//                    text: "${currency}${normalPrice}",
//                    nextText: "单独购买",
//                    isDisabled: isNormalDisabled,
//                    width: 260
//            ))
//
//            // 团长免单
//            String buyButtonText
//            if (tuanType == 5 && pinTuan?.couponFreeNum > 0) {
//                buyButtonText = "团长免费开团"
//            } else if (tuanType == 9) {
//                buyButtonText = "一键参团"
//            } else {
//                buyButtonText = "${currency}${pintuanPrice}"
//            }
//
//            buttons.add(new BottomBarButtonVO(
//                    type: "button",
//                    name: "buy",
//                    text: buyButtonText,
//                    nextText: "${tuanNum}人拼团",
//                    isDisabled: isTuanDisabled
//            ))
//        }
//
//        return new BottomBarVO(
//                buttons: buttons,
//                tipsText: buttonTipsText,
//                hideMiddleSku: pintuanState != 5,
//        )
//    }
//}