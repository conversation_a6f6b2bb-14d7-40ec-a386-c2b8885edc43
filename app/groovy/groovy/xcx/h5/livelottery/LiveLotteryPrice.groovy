package groovy.xcx.h5.livelottery

import com.mogujie.detail.core.adt.ChannelMeta
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

/**
 * Created by jinger on 2018/12/19.
 */
@Translator(id = "liveLotteryPrice", defaultValue = DefaultType.NULL)
class LiveLotteryPrice implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        ChannelMeta channelMetaInfo = channelInfo?.channelMetaInfo

        if (!itemBase) {
            return  null
        }

        itemPrice.with {
            setNowPriceByRange(itemBase)
            setOldPriceByRange(itemBase)
            if (Qualification.hasQualification(channelInfo)) {
                priceTags = [
                        new PriceTagVO(
                                text: channelMetaInfo?.priceDesc ? channelMetaInfo?.priceDesc : '福利价，可兑换1件'
                        )
                ]
            }
        }

        Util.setEsiDataForPrice(itemPrice)
        return itemPrice
    }
}