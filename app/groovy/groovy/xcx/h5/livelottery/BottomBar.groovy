package groovy.xcx.h5.livelottery

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveType
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO
import groovy.xcx.h5.base.ItemState
import groovy.xcx.h5.base.LinkInfoVO

/**
 * Created by jinger on 2018/12/19.
 */
@Translator(id = "bottomBar", defaultValue = DefaultType.NULL)
class BottomBar implements IThreeDependTranslator<ItemBaseDO, ChannelInfoDO, LiveDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo, LiveDO live) {
        List<BottomBarButtonVO> buttons = new ArrayList<>()

        Integer itemState = itemBase?.state
        Boolean isDisabled = itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_OUT_OF_STOCK

        if (live == null) {
            buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
            ))
        }


        // 直播供应链
        if (live?.liveType == LiveType.LIVE_SUPPLY_CHAIN) {
            buttons.add(new BottomBarButtonVO(
                    type: "icon",
                    name: "link",
                    iconName: "person",
                    text: "主播"
            ))
        }

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: "liveLotteryBuy",
                text: Qualification.hasQualification(channelInfo) ? getBuyText(itemState) : "暂无资格",
                isDisabled: !Qualification.hasQualification(channelInfo) || isDisabled
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }

    static private String getBuyText(Integer itemState) {
        switch (itemState) {
            case 0: return "立即抢购"
            case 1: return "已下架"
            case 2: return "卖光啦"
            default: return "立即抢购"
        }
    }
}