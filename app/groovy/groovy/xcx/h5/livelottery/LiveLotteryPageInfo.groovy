package groovy.xcx.h5.livelottery

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.PageInfoVO

/**
 * Created by jinger on 2018/12/19.
 */
@Translator(id = "liveLotteryPageInfo", defaultValue = DefaultType.NULL)
class LiveLotteryPageInfo implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, PageInfoVO> {

    @Override
    PageInfoVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        // 如果没有资格
        if (!Qualification.hasQualification(channelInfo)) {
            return new PageInfoVO(
                    redirectTips: "暂无兑奖资格",
                    redirectUrl: "/pages/detail/pages/normal/index?itemId=" + itemBase?.iid
            )
        }

        return new PageInfoVO()
    }
}