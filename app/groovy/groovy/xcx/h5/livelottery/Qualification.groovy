package groovy.xcx.h5.livelottery

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by jinger on 2018/12/20.
 */
@Translator(id = "hasQualification", defaultValue = DefaultType.NULL)
class Qualification implements IOneDependTranslator<ChannelInfoDO, Boolean> {

    @Override
    Boolean translate(ChannelInfoDO channelInfo) {
        return hasQualification(channelInfo)
    }

    static boolean hasQualification(ChannelInfoDO channelInfo) {
        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()
        if (nowTime > startTime && nowTime < endTime) {
            return channelInfo?.currentPriceIsChannelPrice
        }
        return false
    }
}