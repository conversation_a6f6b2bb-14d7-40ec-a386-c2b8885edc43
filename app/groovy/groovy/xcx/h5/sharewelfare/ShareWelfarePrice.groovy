package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.adt.ChannelMeta
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

@Translator(id = "shareWelfarePrice")
class ShareWelfarePrice implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {
        if (!itemBase) {
            return null
        }

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
        ChannelMeta channelMetaInfo = channelInfo?.channelMetaInfo
        Map<String, Object> extraTagMap = channelInfo?.extraTagMap
        Integer total = extraTagMap?.get("gsc") ? Integer.valueOf(extraTagMap?.get("gsc")?.toString()) : 0

        itemPrice?.setNowPriceByRange(itemBase)
        itemPrice?.setOldPriceByRange(itemBase)

        itemPrice?.priceTags = []

        // 分享福利价
        if (channelMetaInfo?.priceDesc) {
            itemPrice?.priceTags?.add(new PriceTagVO(
                    text: channelMetaInfo?.priceDesc
            ))
        }

        // 分享3个群
        if (total > 0) {
            itemPrice?.priceTags?.add(new PriceTagVO(
                    text: "分享${total}个群"
            ))
        }

        Util.setEsiDataForPrice(itemPrice)

        return itemPrice
    }
}