package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator

/**
 * Create by changsheng on 2018/9/12 00:48
 * Package groovy.xcx.h5.sharewelfare
 */
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.constant.CountdownMaitId

@Translator(id = "priceBanner")
class PriceBanner implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, PriceBannerVO> {


    @Override
    PriceBannerVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {
        ItemPriceVO itemPrice = new ShareWelfarePrice().translate(itemBase, channelInfo)

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        PriceBannerVO priceBanner

        if (!channelInfo) {
            priceBanner = new PriceBannerVO()
        } else {
            priceBanner = new PriceBannerVO()
            priceBanner.initPriceBanner(itemPrice, startTime, endTime, CountdownMaitId.CHANNEL_SHARE_WLFARE)
        }

        return priceBanner
    }
}