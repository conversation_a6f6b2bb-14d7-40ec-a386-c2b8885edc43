package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO

@Translator(id = "shareWelfareInfo")
class ShareWelfareInfo implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, ShareWelfareInfoVO> {

    static class ShareWelfareInfoVO {
        Integer remain

        Integer total

        String savedPrice
    }

    @Override
    ShareWelfareInfoVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        Map<String, Object> extraDynMap = channelInfo?.extraDynMap
        Map<String, Object> extraTagMap = channelInfo?.extraTagMap
        Integer total = extraTagMap?.get("gsc") ? Integer.valueOf(extraTagMap?.get("gsc")?.toString()) : 0
        Integer remainNum = extraDynMap?.get("remainNum") ? Integer.valueOf(extraDynMap?.get("remainNum")?.toString()) : total

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
        itemPrice.setNowPriceByRange(itemBase)
        itemPrice.setOldPriceByRange(itemBase)

//        String channelPrice = channelInfo?.channelPrice? NumUtil.formatNum(channelInfo?.channelPrice / 100D) : itemPrice?.nowPrice
//        String normalPrice = itemPrice?.nowPrice

        Long channelPrice = channelInfo?.channelPrice
        Long normalPrice = channelInfo?.lowNormalPrice

        String savedPrice = null
        if (normalPrice >= 0 && channelPrice >= 0) {
            savedPrice = NumUtil.formatNum((normalPrice - channelPrice) / 100D)
        }

        return new ShareWelfareInfoVO(
                remain: remainNum,
                total: total,
                savedPrice: savedPrice
        )
    }
}