package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import groovy.xcx.h5.base.TopCountdownVO
import groovy.xcx.h5.base.Util

import java.text.SimpleDateFormat

@Translator(id = "shareWelfareCountdown")
class ShareWelfareCountdown implements IOneDependTranslator<ChannelInfoDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ChannelInfoDO channelInfo) {

        if (!channelInfo) {
            return new TopCountdownVO()
        }

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(Util.getMaitIdByEnv(56776))
        Map<String, Object> imgData = maitData?.get(0)

        if (nowTime < startTime) {
            String format = "M月d日 H点m分开抢"

            Date startDate = new Date(startTime * 1000)
            Calendar calendar = Calendar.getInstance()
            calendar.setTime(startDate)
            Integer minute = calendar.get(Calendar.MINUTE)
            if (minute == 0) {
                format = "M月d日 H点开抢"
            }

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format)
            return new TopCountdownVO(
                    image: imgData?.get("shareWelfareImage"),
                    titleColor: imgData?.get("shareWelfareTitleColor"),
                    isCountdomShow: false,
                    timeText: simpleDateFormat.format(new Date(startTime * 1000)),
                    timeColor: imgData?.get("shareWelfareTitleColor")
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            return new TopCountdownVO(
                    image: imgData?.get("shareWelfareImage"),
                    titleColor: imgData?.get("shareWelfareTitleColor"),
                    text: "距结束仅剩",
                    countdown: endTime - nowTime
            )
        } else {
            return new TopCountdownVO()
        }
    }
}