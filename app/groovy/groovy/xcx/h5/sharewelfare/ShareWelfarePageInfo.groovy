package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemState
import groovy.xcx.h5.base.PageDialogVO
import groovy.xcx.h5.base.PageInfoVO
import groovy.xcx.h5.base.Util

@Translator(id = "shareWelfarePageInfo")
class ShareWelfarePageInfo implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, PageInfoVO> {


    @Override
    PageInfoVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        PageDialogVO dialog = null

        Long endTime = channelInfo?.endTime
        Long startTime = channelInfo?.startTime
        Long nowTime = System.currentTimeSeconds()

        Integer itemState = itemBase?.state

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(Util.getMaitIdByEnv(112522))

        Map<String, Object> outOfStockConfig = maitData.find {
            it?.get("itemStatus") == 2
        }

        Map<String, Object> outOfDateConfig = maitData.find {
            it?.get("itemStatus") == 1
        }

        if (endTime > 0 && startTime > 0 && itemState == ItemState.ITEM_OUT_OF_STOCK && nowTime > startTime && nowTime < endTime) {
            // 库存不足
            dialog = new PageDialogVO(
                    title: outOfStockConfig?.get("title"),
                    content: outOfStockConfig?.get("content") ? [outOfStockConfig?.get("content")?.toString()] : null,
                    bgImage: outOfStockConfig?.get("bgImage"),
                    btnText: outOfStockConfig?.get("btnText"),
                    link: outOfStockConfig?.get("link"),
                    status: 4
            )
        } else if (!channelInfo) {
            // 活动结束，展示3秒之后自动跳转
            dialog = new PageDialogVO(
                    title: outOfDateConfig?.get("title"),
                    content: outOfDateConfig?.get("content") ? [outOfDateConfig?.get("content")?.toString()] : null,
                    bgImage: outOfDateConfig?.get("bgImage"),
                    btnText: outOfDateConfig?.get("btnText"),
                    link: outOfDateConfig?.get("link"),
                    autoRedirect: true,
                    status: 5
            )
        }

        return new PageInfoVO(
                dialog: dialog
        )
    }
}