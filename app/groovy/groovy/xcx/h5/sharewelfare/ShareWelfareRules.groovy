package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.EntryBarVO
import groovy.xcx.h5.base.PageDialogVO
import groovy.xcx.h5.base.Util

@Translator(id = "shareWelfareRules")
class ShareWelfareRules implements IOneDependTranslator<ItemBaseDO, EntryBarVO> {

    @Override
    EntryBarVO translate(ItemBaseDO itemBase) {
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(Util.getMaitIdByEnv(112529))
        Map<String, Object> maitItemData = maitData?.get(0)

        List<Map<String, Object>> maitRuleData = MaitUtil.getMaitData(Util.getMaitIdByEnv(113124))

        List<String> content = []
        maitRuleData?.eachWithIndex { it, index ->
            content.add("${index + 1}. ${it?.get("text")?.toString()}".toString())
        }

        return new EntryBarVO(
                title: maitItemData?.get("iconTitle"),
                titleColor: maitItemData?.get("tagTextColor"),
                accessoryTitle: maitItemData?.get("accessoryTitle"),
                dialog: new PageDialogVO(
                        title: maitItemData?.get("ruleTitle"),
                        content: content,
                        bgImage: maitItemData?.get("ruleBgImage")
                )
        )
    }
}