package groovy.xcx.h5.sharewelfare

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
// import com.mogujie.enzo.api.enums.TuanStatus
import groovy.xcx.h5.base.*

@Translator(id = "shareWelfareBottomBar")
class ShareWelfareBottomBar implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, BottomBarVO> {


    enum TuanStatus {
        tuaning(0), //"拼团中"
        tuanSuccess(1), //"拼团成功"
        tuanFailed(2), //"拼团失败"
        waitPay(1000), //"拼团中等待团员付款"
        hasReceive(1001), // "已领取"
        notReceive(1002), // "未领取"
        exhaust(1003), // "已领完"
        sending(1004), // "奖品发放中"
        hasJoin(1005), // "已参与"
        assistSuccess(1006), // "助力完成,逛逛其他商品"
        expired(1007), // "领取过期"
        notShare(1008); // "未分享"

        private int value;

        private TuanStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }

        public void setValue(int value) {
            this.value = value;
        }

    }


    @Override
    BottomBarVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        Integer itemState = itemBase?.state
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
        itemPrice.setNowPriceByRange(itemBase)
        itemPrice.setOldPriceByRange(itemBase)

        String currency = itemBase?.currency ?: "¥"
        String channelPrice = itemPrice?.nowPrice
        String normalPrice = channelInfo?.lowNormalPrice ? NumUtil.formatNum(channelInfo?.lowNormalPrice / 100D) : itemPrice?.nowPrice

        Map<String, Object> extraDynMap = channelInfo?.extraDynMap
        Map<String, Object> extraTagMap = channelInfo?.extraTagMap
        Integer total = extraTagMap?.get("gsc") ? Integer.valueOf(extraTagMap?.get("gsc")?.toString()) : 0
        Integer remainNum = extraDynMap?.get("remainNum") ? Integer.valueOf(extraDynMap?.get("remainNum")?.toString()) : total
        TuanStatus tuanStatus = (TuanStatus) extraDynMap?.get("tuanStatus")
        Boolean hasBeenShared = extraDynMap?.get("hasBeenShared") ? Boolean.valueOf(extraDynMap?.get("hasBeenShared")?.toString()) : null
        Long tuanId = extraDynMap?.get("tuanId") ? Long.valueOf(extraDynMap?.get("tuanId")?.toString()) : null

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(Util.getMaitIdByEnv(112582))
        Map<String, Object> maitItemData = maitData?.get(0)

        String shareTipText = maitItemData?.get("shareTipText")
        String shareCompleteTipText = maitItemData?.get("shareCompleteTipText")
        String shareSendingText = maitItemData?.get("shareSendingText")

        String shareTipImage = maitItemData?.get("shareTipImage")
        String shareComplateTipImage = maitItemData?.get("shareComplateTipImage")
        String shareSendingImage = maitItemData?.get("shareSendingImage")

        String shareTipTextColor = maitItemData?.get("shareTipTextColor")
        String shareCompleteTipTextColor = maitItemData?.get("shareCompleteTipTextColor")
        String shareSendingTextColor = maitItemData?.get("shareSendingTextColor")

        Integer shareTipImageWidth = maitItemData?.get("shareTipImageWidth") ? Integer.valueOf(maitItemData?.get("shareTipImageWidth")?.toString()) : 0
        Integer shareComplateTipImageWidth = maitItemData?.get("shareComplateTipImageWidth") ? Integer.valueOf(maitItemData?.get("shareComplateTipImageWidth")?.toString()) : 0
        Integer shareSendingImageWidth = maitItemData?.get("shareSendingImageWidth") ? Integer.valueOf(maitItemData?.get("shareSendingImageWidth")?.toString()) : 0

        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
        ))

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        String buttonTipsText = ""
        String buttonName = ""
        String buttonText = ""
        String buttonNextText = ""
        Boolean isDisabled = false
        BottomBarButtonPopUpVO popup = null
        Long countdown = null

        if (itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_WAIT_FOR_SALE) {
            // 下架或者待开售
            buttonTipsText = "商品已下架，快去挑挑其他商品哦～"
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "singleBuyRedirect",
                    text: "${currency}${normalPrice}",
                    nextText: "单独购买",
                    isDisabled: true
            ))
            buttonName = "buy"
            buttonText = "${currency}${channelPrice}"
            buttonNextText = "分享${total}个群"
            isDisabled = true
        } else {
            // 非下架场景，单独购买始终可以点击
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "singleBuyRedirect",
                    text: "${currency}${normalPrice}",
                    nextText: "单独购买",
                    isDisabled: false
            ))

            if (nowTime < startTime) {
                if (startTime - nowTime <= 300) {
                    // 这个分支先不发
                    // 开始前5分钟刷新
                    buttonName = "refresh"
                    buttonText = "立即刷新"
                    countdown = startTime - nowTime
                    buttonTipsText = "活动即将开始"
                } else {
                    // 活动未开始
                    buttonTipsText = "活动即将开始，请设置提醒"
                    buttonName = "alert"
                    buttonText = "设置提醒"
                }
            } else if (nowTime > startTime && nowTime < endTime) {
                // 活动进行中

                buttonText = "${currency}${channelPrice}"
                buttonNextText = "分享${total}个群"

                if (itemState == ItemState.ITEM_OUT_OF_STOCK) {
                    // 库存不足
                    buttonName = "buy"
                    isDisabled = true
                } else if (tuanStatus == TuanStatus.notShare || tuanStatus == TuanStatus.tuaning) {
                    // 还没有资格
                    buttonName = hasBeenShared ? "share" : "shareDialog"
                    popup = hasBeenShared ? new BottomBarButtonPopUpVO(
                            text: shareTipText?.replace("{remain}", remainNum.toString()),
                            textColor: shareTipTextColor,
                            image: shareTipImage,
                            align: "right",
                            width: shareTipImageWidth
                    ) : null
                } else if (tuanStatus == TuanStatus.notReceive) {
                    // 已经有资格，并且未消耗
                    buttonName = "buy"
                    popup = hasBeenShared ? new BottomBarButtonPopUpVO(
                            text: shareCompleteTipText,
                            textColor: shareCompleteTipTextColor,
                            image: shareComplateTipImage,
                            align: "right",
                            width: shareComplateTipImageWidth
                    ) : null
                } else if (tuanStatus == TuanStatus.sending) {
                    // 获取资格中
                    buttonName = "sendingDialog"
                    popup = hasBeenShared ? new BottomBarButtonPopUpVO(
                            text: shareSendingText,
                            textColor: shareSendingTextColor,
                            image: shareSendingImage,
                            align: "right",
                            width: shareSendingImageWidth
                    ) : null
                } else {
                    buttonName = "shareDialog"
                }
            } else {
                // 活动结束
                buttonName = "buy"
                buttonText = "活动结束"
                isDisabled = true
            }
        }

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: buttonName,
                text: buttonText,
                nextText: buttonNextText,
                isDisabled: isDisabled,
                popup: popup,
                startTime: startTime
        ))

        return new BottomBarVO(
                buttons: buttons,
                tipsText: buttonTipsText,
                countdown: countdown
        )
    }
}