package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ShareInfoVO

@Translator(id = "shareInfo")
class ShareInfo implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, ShareInfoVO> {

    @Override
    ShareInfoVO translate(ItemBaseDO itemBase, FastbuyDO fastbuy) {
        if (itemBase == null) {
            return null
        }

        RushState rushState = new RushState(fastbuy, itemBase)

        Boolean isNeedShareIntegral = rushState.state in [0, 1, 4, 5]

        return new ShareInfoVO(isNeedShareIntegral)
    }
}