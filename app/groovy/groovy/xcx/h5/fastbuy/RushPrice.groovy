package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.LimitDiscountInfo
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.CountdownMaitId

/**
 * Created by changsheng on 30/06/2017.
 * H5私有模块-快抢详情页-价格
 */
@Translator(id = "rushPrice")
class RushPrice implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, ItemPriceVO> {


    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, FastbuyDO fastbuy) {
        ItemPriceVO rushPrice = new ItemPriceVO(itemBase)

        rushPrice.with {
            if (itemBase) {

                List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(fastbuy?.maitId ?: CountdownMaitId.CHANNEL_FASTBUY)
                Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)

                // 限量立减
                LimitDiscountInfo limitDiscountInfo = itemBase?.limitDiscountInfo
                if (limitDiscountInfo) {
                    // 限量立减的原价为大促价
                    if (itemBase?.activityPrice) {
                        oldPrice = NumUtil.formatNum(itemBase?.activityPrice / 100D)
                    }
                    Integer stock = limitDiscountInfo?.limitCount
                    priceTags = [
                            new PriceTagVO(
                                    text: countdownInfoImgData?.get("priceDesc")?.replace('${stock}', String.valueOf(stock))?: "前${stock}件限量抢"
                            )
                    ]
                } else {
                    priceTags = [
                            new PriceTagVO(
                                    text: countdownInfoImgData?.get("priceDesc")?: "快抢价"
                            )
                    ]
                }

                if (fastbuy?.isNewComerItem()) {
                    priceTags.add(
                            new PriceTagVO(
                                    text: "新人专享"
                            )
                    )
                }
            } else {
                return null
            }
        }

        Util.setEsiDataForPrice(rushPrice)

        return rushPrice
    }
}
