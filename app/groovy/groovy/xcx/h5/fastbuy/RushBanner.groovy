package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.CommonBannerVO

/**
 * Created by jinger on 2019/1/30.
 */
@Translator(id = "rushBanner", defaultValue = DefaultType.NULL)
class RushBanner implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, CommonBannerVO> {

    @Override
    CommonBannerVO translate(ItemBaseDO item,FastbuyDO fastbuy) {
        if (!fastbuy) {
            return  new CommonBannerVO()
        }

        // 新品快时尚隐藏价格趋势图
        if ("newFashionBuy" == fastbuy?.getExtra()?.get("bizType")) {
            return  new CommonBannerVO()
        }

        // 价格趋势图
        return new CommonBannerVO(
                "image": item.canShowStrikethroughPrice ? fastbuy?.extra?.get("kimg") : ""
        )
    }
}