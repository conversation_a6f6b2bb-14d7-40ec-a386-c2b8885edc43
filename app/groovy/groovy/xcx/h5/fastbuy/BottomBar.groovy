package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO

@Translator(id = "bottomBar")
class BottomBar implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase, FastbuyDO fastbuy) {

        Integer itemState = itemBase?.state
        Integer state = fastbuy?.state
        Long startTime = fastbuy?.startTime
        Long nowTime = System.currentTimeSeconds()
        Boolean isNewComerItem = fastbuy?.isNewComerUser()
        Boolean isNewComerUser = fastbuy?.isNewComerItem()
        Number isNoticed = fastbuy?.isFollowed() ? 1 : 0 //是否设置提醒，0-未设置提醒  1-已经设置提醒

        if (itemState == 1 || itemState == 3) {
            state = 3
        } else if (nowTime < startTime && state == 0 && (startTime - nowTime) <= 300) {
            state = 5
        }

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
        ))

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        String buttonText
        Boolean isDisabled = false
        String name

        // 新人专享快抢商品
        if (isNewComerItem && !isNewComerUser) {
            buttonText = "新用户专享"
            name = "buy"
            isDisabled = true
        } else {
            switch (state) {
                case 0:
                    buttonText = "即将开始，设置提醒"
                    name = "rushAlert"
                    break
                case 1:
                    buttonText = "立即抢购"
                    name = "buy"
                    break
                case 2:
                    buttonText = "已售罄"
                    name = "buy"
                    isDisabled = true
                    break
                case 3:
                    buttonText = "活动结束"
                    name = "buy"
                    isDisabled = true
                    break
                case 4:
                    buttonText = "还有机会，点击刷新"
                    name = "refresh"
                    break
                case 5:
                    buttonText = "立即刷新"
                    name = "refresh"
                    break
                default:
                    buttonText = "活动结束"
                    name = "buy"
                    isDisabled = true
                    break
            }
        }

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: name,
                text: buttonText,
                isNoticed: isNoticed,
                isDisabled: isDisabled,
                startTime: fastbuy?.startTime
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }
}