package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import groovy.xcx.h5.base.PriceBannerVO

/**
 * Create by changsheng on 2018/11/26 14:53
 * Package groovy.xcx.h5.common
 */
@Translator(id = "officialRecommend")
class OfficialRecommendInfo implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, OfficialRecommendInfoVO> {

    static class OfficialRecommendInfoVO {
        List<String> tags
        String title
        String desc
    }

    @Override
    OfficialRecommendInfoVO translate(FastbuyDO fastbuy, ItemBaseDO itemBase) {
        OfficialRecommend officialRecommend = itemBase?.officialRecommend

        PriceBannerVO priceBannerInfo = new PriceBanner().translate(fastbuy, itemBase)

        Long startTime = officialRecommend?.startTime
        Long endTime = officialRecommend?.endTime
        Long now = System.currentTimeSeconds()

        if (startTime <= now && now <= endTime) {
            // 氛围标 + 推荐标
            List<String> tags = []
            if (priceBannerInfo?.titleTagImage) {
                tags.add(priceBannerInfo?.titleTagImage)
            }
            List<Map<String, Object>> maitList =  MaitUtil.getMaitData(127253L)
            Map<String, Object> maitData = maitList?.get(0)
            String officialRecommendIcon = maitData?.get("officialRecommendIcon")
            if (officialRecommendIcon) {
                tags.add(officialRecommendIcon)
            }
            return new OfficialRecommendInfoVO(
                    title: officialRecommend?.title,
                    desc: officialRecommend?.desc,
                    tags: tags,
            )
        } else {
            return new OfficialRecommendInfoVO()
        }

    }
}