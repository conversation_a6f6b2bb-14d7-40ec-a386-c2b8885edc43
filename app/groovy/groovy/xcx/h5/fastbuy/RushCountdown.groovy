package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.TopCountdownVO

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢倒计时
 */

@Translator(id = "rushCountdown")
class RushCountdown implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(FastbuyDO fastbuy, ItemBaseDO itemBase) {

        Integer itemState = itemBase?.state
        if (!fastbuy || itemState == 1 || itemState == 3) {
            return new TopCountdownVO()
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(43182)
        Map<String, Object> imgData = maitData?.get(0)

        Long startTime = fastbuy?.startTime
        Long endTime = fastbuy?.endTime

        def backgroundImg = imgData?.get("fastBuyImage")
        def titleColor = imgData?.get("fastBuyTitleColor")

        def nowTime = System.currentTimeSeconds()
        if (nowTime < startTime) {
            // 活动未开始
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            // 活动进行中
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: endTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }
}
