package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.xcx.h5.base.ProgressVO

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-进度条
 */
@Translator(id = "rushProgress")
class RushProgress implements IOneDependTranslator<FastbuyDO, ProgressVO> {

    @Override
    ProgressVO translate(FastbuyDO fastbuy) {

        if (!fastbuy) {
            return null
        }
        String background = "linear-gradient(to right, #FFCA49, #FF9A49)"
        Integer progress = 0
        if (fastbuy?.allStock > 0) {
            progress = Math.ceil(fastbuy?.progressBar * 100)
        }
        String leftTitle = ""
        String rightTitle = ""
        String leftTextColor = "#999999"
        String rightTextColor = "#FF5777"
        switch (fastbuy?.state) {
            case RushState.ACTIVITY_NOT_STARTED:
            case RushState.ACTIVITY_WARM_UP:
                //未开始
                leftTitle = "库存${fastbuy?.allStock}件"
                break
            case RushState.ACTIVITY_STARTED:
                //活动中
                leftTitle = "已抢购${fastbuy?.allStock - fastbuy?.totalStock}件"
                rightTitle = "仅剩${fastbuy?.totalStock}件"
                break
            case RushState.OUT_OF_STOCK:
                leftTitle = "已售罄"
                break
            case RushState.ACTIVITY_ENDED:
                //抢购完或已结束
                leftTitle = "已售罄"
                break
            case RushState.WAIT_FOR_STOCK:
                //抢购完，还有人未付款
                leftTitle = "已抢购${fastbuy?.allStock}件"
                rightTitle = "${fastbuy?.leftUser}人未付款"
                break
            default:
                break
        }
        return new ProgressVO(
                leftTitle: leftTitle,
                rightTitle: rightTitle,
                progress: progress,
                leftTextColor: leftTextColor,
                rightTextColor: rightTextColor,
                background: background
        )
    }

}
