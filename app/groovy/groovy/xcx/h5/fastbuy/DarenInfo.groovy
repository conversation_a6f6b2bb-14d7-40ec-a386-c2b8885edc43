package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Create by jinger on 2019/08/21 15:09
 */
@Translator(id = "darenInfo", defaultValue = DefaultType.NULL)
class DarenInfo implements ITwoDependTranslator<ExtraDO, FastbuyDO, DarenInfoVO> {

    public static final long maitId = 138701L

    static class DarenInfoVO {
        String avatar
        String name
        String tag
        String icon
        String subTitle
        String background
        String link
        String tagColor
        String subTitleColor
    }

    @Override
    DarenInfoVO translate(ExtraDO extra, FastbuyDO fastbuy) {
        // 新品快时尚达人信息
        if (extra?.getDarenInfo() && "newFashionBuy" == fastbuy?.getExtra()?.get("bizType")) {

            com.mogujie.detail.module.extra.domain.DarenInfo darenInfo = extra?.getDarenInfo()
            List<Map<String, Object>> list = MaitUtil.getMaitData(maitId)

            if (list) {
                Map<String, Object> foundDaren = null
                for (Map<String, Object> map : list) {
                    long maitTag
                    try {
                        maitTag = Long.parseLong(String.valueOf(map?.get("userid")))
                    } catch(Throwable ignore) {
                        maitTag = darenInfo?.id - 1
                    }
                    if (maitTag == darenInfo?.id) {
                        foundDaren = map
                        break
                    }
                }
                if (foundDaren) {
                    String subtitle
                    if (foundDaren?.get("uesrAttestation")) {
                        subtitle = "${foundDaren?.get("uesrAttestation")} | 种草力${darenInfo?.power}"
                    } else {
                        subtitle = "种草力${darenInfo?.power}"
                    }
                    return new DarenInfoVO(
                            avatar: darenInfo?.avatar,
                            name: darenInfo?.name,
                            tag: foundDaren?.get("userTitle"),
                            tagColor: foundDaren?.get("titleColor"),
                            subTitle: subtitle,
                            subTitleColor: foundDaren?.get("attestationColor"),
                            icon: foundDaren?.get("attestationIcon"),
                            background: foundDaren?.get("background"),
                            link: foundDaren?.get("link")
                    )
                }
            }
        }
        return new DarenInfoVO()
    }
}