package groovy.xcx.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil

/**
 * Create by changsheng on 2018/8/25 11:28
 * Package groovy.xcx.h5.fastbuy
 */
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.base.constant.CountdownType

@Translator(id = "priceBanner")
class PriceBanner implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, PriceBannerVO> {

    @Override
    PriceBannerVO translate(FastbuyDO fastbuy, ItemBaseDO itemBase) {

        Integer itemState = itemBase?.state

        if (!fastbuy || itemState == 1 || itemState == 3) {
            return new PriceBannerVO()
        }

        ItemPriceVO rushPrice = new RushPrice().translate(itemBase, fastbuy)

        Long startTime = fastbuy?.startTime
        Long endTime = fastbuy?.endTime

        Long nowTime = System.currentTimeSeconds()
        if (nowTime < startTime) {

            Integer noticeNum = fastbuy?.noticeNum
            // 活动未开始
            PriceBannerVO priceBanner = new PriceBannerVO(
                    priceDesc: "${Util.tenThousandFormat(noticeNum, 'w')}人关注",
            )
            priceBanner.initPriceBanner(CountdownType.PRE, rushPrice, startTime, endTime, fastbuy?.maitId ?: CountdownMaitId.CHANNEL_FASTBUY)
            return priceBanner
        } else if (nowTime > startTime && nowTime < endTime) {
            // 活动进行中
            PriceBannerVO priceBanner = new PriceBannerVO(
                    priceDesc: "",
            )
            priceBanner.initPriceBanner(CountdownType.IN, rushPrice, startTime, endTime, fastbuy?.maitId ?: CountdownMaitId.CHANNEL_FASTBUY)

            List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(fastbuy?.maitId ?: CountdownMaitId.CHANNEL_FASTBUY)
            Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

            // 	隐藏进度条，1隐藏，0不隐藏
            Boolean isHiddenProgress = countdownInfoImgData?.get("isHiddenProgress")?.equals("1")

            if (fastbuy?.allStock > 0 && !isHiddenProgress) {
                priceBanner.progress = Math.ceil(fastbuy.progressBar * 100)
                priceBanner.progressText = getProgressText(fastbuy)
                priceBanner.progressColor = countdownInfoImgData?.get("progressValueColor") ?: "#D86EFF"
                priceBanner.progressBgColor = countdownInfoImgData?.get("progressBgColor") ?: "#8C2FF1"
            }
            return priceBanner
        } else {
            return new PriceBannerVO()
        }
    }

    static getProgressText(FastbuyDO fastbuy) {
        switch (fastbuy?.state) {
            case RushState.ACTIVITY_NOT_STARTED:
            case RushState.ACTIVITY_WARM_UP:
                //未开始
                return "库存${fastbuy?.allStock}件"
            case RushState.ACTIVITY_STARTED:
                //活动中
                return "仅剩${fastbuy?.totalStock}件"
            case RushState.OUT_OF_STOCK:
                return "已售罄"
            case RushState.ACTIVITY_ENDED:
                //抢购完或已结束
                return "已售罄"
            case RushState.WAIT_FOR_STOCK:
                //抢购完，还有人未付款
                return "${fastbuy?.leftUser}人未付款"
            default:
                return ""
        }
    }
}