package groovy.xcx.h5.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

/**
 * Created by changsheng on 14/03/2017.
 * H5私有模块-秒杀详情页-价格
 */
@Translator(id = "seckillPrice")
class SeckillPrice implements ITwoDependTranslator<SeckillDO, ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(SeckillDO seckill, ItemBaseDO itemBase) {
        ItemPriceVO seckillPrice = new ItemPriceVO(itemBase);
        if (seckill == null) {
            seckillPrice.setNowPriceByRange(itemBase)
            seckillPrice.setOldPriceByRange(itemBase)
        } else {
            PriceTagVO priceTag = new PriceTagVO(
                    text: "秒杀价",
                    bgColor: PriceTagVO.defaultBgColor,
                    textColor: PriceTagVO.defaultTextColor,
                    link: ""
            );
            seckillPrice.priceTags = [priceTag];
            // 秒杀价格单位是分，转换成元
            seckillPrice.nowPrice = NumUtil.formatNum(seckill?.price / 100D);

            seckillPrice.setOldPriceByRange(itemBase);

            seckillPrice.eventDesc = "每人限购一件"

            if (Tools.isVirtualCouponItem()) {
                seckillPrice.eventDesc += "，购买后自动发券，不支持退款"
            }

        }

        Util.setEsiDataForPrice(seckillPrice)

        return seckillPrice;
    }
}