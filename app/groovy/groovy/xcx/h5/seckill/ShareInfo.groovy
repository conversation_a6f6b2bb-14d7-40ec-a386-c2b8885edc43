package groovy.xcx.h5.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import groovy.xcx.h5.base.ShareInfoVO

@Translator(id = "shareInfo")
class ShareInfo implements ITwoDependTranslator<ItemBaseDO, SeckillDO, ShareInfoVO> {

    @Override
    ShareInfoVO translate(ItemBaseDO itemBase, SeckillDO seckill) {
        if (seckill == null || itemBase == null) {
            return null
        }

        Boolean isNeedShareIntegral = seckill?.status in [0, 1, 2]

        return new ShareInfoVO(isNeedShareIntegral)

    }
}