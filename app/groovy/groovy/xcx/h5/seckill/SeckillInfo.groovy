package groovy.xcx.h5.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-秒杀详情页-价格
 */
@Translator(id = "seckillInfo")
public class SeckillInfo implements ITwoDependTranslator<SeckillDO, ItemBaseDO, SeckillVO> {

    static class SeckillVO {
        Integer countdown
        Integer price
        /**
         * 秒杀状态
         * 0. 未开始
         * 1. 预热中
         * 2. 进行中
         * 3. 结束
         */
        Integer status
        Integer startTime
        Integer endTime
        Integer enrollStock
        Long outerId
        String secKillId
        String itemId
    }

    @Override
    SeckillVO translate(SeckillDO seckill, ItemBaseDO itemBase) {
        Integer status;
        if (!seckill) {
            // 已结束
            status = 4
        } else {
            status = seckill?.status
        }
        return new SeckillVO(
                countdown: seckill?.countdown,
                price: seckill?.price,
                status: status,
                startTime: seckill?.startTime,
                endTime: seckill?.endTime,
                enrollStock: seckill?.enrollStock,
                outerId: seckill?.outerId,
                secKillId: seckill?.secKillId,
                itemId: itemBase?.iid
        )
    }
}