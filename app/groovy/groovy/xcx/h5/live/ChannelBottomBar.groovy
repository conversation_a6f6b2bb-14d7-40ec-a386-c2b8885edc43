package groovy.xcx.h5.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO

@Translator(id = "channelBottomBar")
class ChannelBottomBar implements IThreeDependTranslator<ItemBaseDO, ChannelInfoDO, SkuDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo, SkuDO sku) {
        Integer itemState = itemBase?.state
        Integer itemStock = sku?.totalStock

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
        ))

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        String buttonText
        Boolean isDisabled
        String name = "buy"


        if (itemState == 1 || itemState == 3) {
            // 商品下架
            buttonText = "已下架"
            isDisabled = true
        } else if (nowTime < startTime) {
            // 活动未开始，这里有可能需要设置提醒
            buttonText = "活动未开始"
            isDisabled = true
        } else if (nowTime >= startTime && nowTime <= endTime) {
            if (itemStock > 0) {
                // 正常购买
                buttonText = "立即抢购"
                isDisabled = false
            } else {
                // 库存不足
                buttonText = "已抢完"
                isDisabled = true
            }
        } else {
            // 活动结束&不存在这个活动
            buttonText = "活动结束"
            isDisabled = true
        }

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: name,
                text: buttonText,
                isDisabled: isDisabled,
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }
}