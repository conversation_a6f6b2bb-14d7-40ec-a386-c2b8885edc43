package groovy.xcx.h5.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ProgressVO

@Translator(id = "channelProgress")
class ChannelProgress implements IThreeDependTranslator<ItemBaseDO, SkuDO, ChannelInfoDO, ProgressVO> {

    static class ChannelProgressVO {}

    @Override
    ProgressVO translate(ItemBaseDO itemBase, SkuDO sku, ChannelInfoDO channelInfo) {

        Integer itemState = itemBase?.state

        // 活动不存在、商品下架
        if (!channelInfo || itemState == 1 || itemState == 3) {
            return null
        }

        Integer itemStock = sku?.totalStock
        Long originTotalStock = channelInfo?.originTotalStock

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()

        String leftTitle
        String rightTitle = ""
        String leftTextColor = "#999999"
        String rightTextColor = "#FF5777"
        String background = "linear-gradient(to right, #FFCA49, #FF9A49)"
        Integer progress = Math.ceil((originTotalStock - itemStock) * 100 / originTotalStock)

        if (nowTime < startTime) {
            // 活动未开始
            leftTitle = "库存${originTotalStock}件"
        } else if (nowTime > startTime && nowTime < endTime) {
            if (itemStock > 0) {
                leftTitle = "已抢购${originTotalStock - itemStock}件"
                rightTitle = "仅剩${itemStock}件"
            } else {
                // 库存不足
                leftTitle = "已售罄"
            }
        } else {
            //已结束
            leftTitle = "已结束"
        }

        return new ProgressVO(
                leftTitle: leftTitle,
                rightTitle: rightTitle,
                progress: progress,
                leftTextColor: leftTextColor,
                rightTextColor: rightTextColor,
                background: background
        )
    }
}