package groovy.xcx.h5.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.PageInfoVO

@Translator(id = "channelPageInfo")
class ChannelPageInfo implements ITwoDependTranslator<ChannelInfoDO, ItemBaseDO, PageInfoVO> {


    @Override
    PageInfoVO translate(ChannelInfoDO channelInfo, ItemBaseDO itemBase) {
        Long nowTime = System.currentTimeSeconds()
        if (!channelInfo || nowTime > channelInfo?.endTime) {
            return new PageInfoVO(
                    redirectUrl: "/pages/detail/pages/normal/index?itemId=${itemBase?.iid}",
                    redirectTips: "本商品直播特卖已经结束，正在为您跳转至新购买地址~"
            )
        } else {
            return new PageInfoVO()
        }
    }
}