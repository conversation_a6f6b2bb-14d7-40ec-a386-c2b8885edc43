package groovy.xcx.h5.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import groovy.xcx.h5.base.TopCountdownVO

@Translator(id = "channelCountdown")
class ChannelCountdown implements IOneDependTranslator<ChannelInfoDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ChannelInfoDO channelInfo) {

        Boolean currentPriceIsChannelPrice = channelInfo?.currentPriceIsChannelPrice

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776)
        Map<String, Object> imgData = maitData?.get(0)

        String backgroundImg = imgData?.get("liveImage")

        if (currentPriceIsChannelPrice) {
            return new TopCountdownVO(
                    image: backgroundImg,
                    isCountdomShow: false
            )
        } else {
            return new TopCountdownVO()
        }
    }
}