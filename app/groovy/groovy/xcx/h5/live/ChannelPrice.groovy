package groovy.xcx.h5.live

import com.mogujie.detail.core.adt.ChannelMeta
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

@Translator(id = "channelPrice")
class ChannelPrice implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
        ChannelMeta channelMetaInfo = channelInfo?.channelMetaInfo
        Boolean currentPriceIsChannelPrice = channelInfo?.currentPriceIsChannelPrice

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                // 只有主播改价之后，才需要展示priceTag
                if (currentPriceIsChannelPrice) {
                    priceTags = [
                            new PriceTagVO(
                                    text: channelMetaInfo?.priceDesc
                            )
                    ]
                }
            }

            Util.setEsiDataForPrice(itemPrice)
            return itemPrice
        }
    }
}