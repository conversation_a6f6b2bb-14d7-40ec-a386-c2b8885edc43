package groovy.xcx.h5.live

import com.mogujie.detail.core.annotation.Translator

/**
 * Create by changsheng on 2018/8/27 10:17
 * Package groovy.xcx.h5.live
 */
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.base.constant.CountdownType

@Translator(id = "channelPriceBanner")
class ChannelPriceBanner implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, PriceBannerVO> {

    @Override
    PriceBannerVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {
        if (!itemBase || !channelInfo) {
            return new PriceBannerVO()
        }
        ItemPriceVO itemPrice = new ChannelPrice().translate(itemBase, channelInfo)
        Boolean currentPriceIsChannelPrice = channelInfo?.currentPriceIsChannelPrice

        if (currentPriceIsChannelPrice) {
            PriceBannerVO priceBanner = new PriceBannerVO(
                    countdown: 0,
                    type: CountdownType.IN,
            )
            priceBanner.setPriceInfo(itemPrice)
            priceBanner.setCountdownImage(CountdownMaitId.CHANNEL_LIVE)

            return priceBanner

        } else {
            return new PriceBannerVO()
        }
    }
}