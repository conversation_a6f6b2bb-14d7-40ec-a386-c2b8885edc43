package app.groovy.groovy.xcx.h5.common


import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO

@Translator(id = "galleryNormal", defaultValue = DefaultType.NULL)
class GalleryNormal implements IThreeDependTranslator<ItemBaseDO, LiveSimpleDO, LiveDO, Object> {

    static class GalleryNormalVO {
        GalleryExplainInfo explainInfo
    }

    static class GalleryExplainInfo {
        String coverImage
        String jumpUrl
        String firstFrame
        String videoUrl
        String videoH265Url
    }

    @Override
    Object translate(ItemBaseDO input1, LiveSimpleDO liveSimpleDO, LiveDO liveDO) {
        if (!input1?.topImages) {
            // 业务上可以返回NULL
            return null
        }

        def ret = new GalleryNormalVO()

        // 视频讲解浮窗，和店铺自播、进房浮标互斥
        if (liveSimpleDO?.explainWindowInfo && !liveSimpleDO?.shopLiveInfo && (!liveDO?.liveItemInfos || liveDO?.liveItemInfos?.size() == 0)) {
            ret.explainInfo = new GalleryExplainInfo(
                    coverImage: input1.topImages.first(),
                    jumpUrl: liveSimpleDO.explainWindowInfo.appLink,
                    firstFrame: liveSimpleDO.explainWindowInfo.firstFrame,
                    videoUrl: liveSimpleDO.explainWindowInfo.fileUrl,
                    videoH265Url: liveSimpleDO.explainWindowInfo.videoH265Url
            )
        } else {
            ret.explainInfo = new GalleryExplainInfo(jumpUrl: "") // 客户端据 jumpUrl 判断是否要展示，置为空字符串避免缓存问题
        }

        if (ret.explainInfo.jumpUrl) {
            String acm = DetailContextHolder.get().getParam("acm")
            if (acm) {
                if (ret.explainInfo.jumpUrl.contains("?")) {
                    ret.explainInfo.jumpUrl += "&acm=" + acm
                } else {
                    ret.explainInfo.jumpUrl += "?acm=" + acm
                }
            }
        }

        return ret
    }
}

