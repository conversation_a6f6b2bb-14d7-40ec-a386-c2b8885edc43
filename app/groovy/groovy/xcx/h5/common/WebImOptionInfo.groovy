package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.xcx.h5.base.Util

/**
 * Create by changsheng on 2018/11/28 15:11
 * Package groovy.xcx.h5.common
 */
@Translator(id = "webImOptionInfo")
class WebImOptionInfo implements IFourDependTranslator<ItemBaseDO, ShopDO, PinTuanDO, PresaleDO, WebImOptionInfoVO> {

    static class WebImOptionInfoVO {
        WebImOptionVO option
        Boolean showOnBottom
    }

    static class WebImOptionVO  {
        String shopId
        String userId
        String goodsId
        String img
        String title
    }

    @Override
    WebImOptionInfoVO translate(ItemBaseDO itemBase, ShopDO shop, PinTuanDO pinTuan, PresaleDO presale) {
        WebImOptionVO option =  new WebImOptionVO(
                shopId: shop?.shopId,
                userId: shop?.userId,
                goodsId: itemBase?.iid,
                img: itemBase?.topImages?.size() > 0 ? itemBase.topImages.get(0) : '',
                title: itemBase?.title,
        )

        RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()
        Boolean isPintuan = Util.isPintuanItem(itemBase, pinTuan, presale)

        return new WebImOptionInfoVO(
                option: option,
                // 除了非渠道拼团
                showOnBottom: !(isPintuan && routeInfo.bizType == BizType.NORMAL),
        )
    }
}