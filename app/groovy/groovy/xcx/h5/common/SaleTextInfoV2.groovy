package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.TextUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.detail.core.adt.DetailContextHolder
import groovy.xcx.h5.base.Util
import org.apache.http.util.TextUtils
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum

import java.text.SimpleDateFormat
import groovy.xcx.h5.base.SaleTextVO

/**
 * Created by jinger on 2018/12/29.
 *
 *  1190 新版本：待开售 > 延迟发货 > 优惠券 > 2107资源位 > 营销文案
 *  老版本：延迟发货 > 营销文案
 */
@Translator(id = "saleTextInfoV2", defaultValue = DefaultType.NULL)
class SaleTextInfoV2 implements IFourDependTranslator<ItemBaseDO, SkuDO, ExtraDO, ActivityDO, SaleTextVO> {


    @Override
    SaleTextVO translate(ItemBaseDO itemBase, SkuDO sku, ExtraDO extraDO, ActivityDO activityDO) {


        if (!itemBase || !sku) {
            return  null
        }

        // 待开售
        if (itemBase?.state == 3 && extraDO?.onSaleTime > 0) {
            Date date = new Date(extraDO?.onSaleTime * 1000)
            SimpleDateFormat formatter = new SimpleDateFormat("MM-dd HH:mm")
            String dateString = formatter.format(date)
            return  new SaleTextVO(
                    text: "${dateString}开售，请提前设置提醒",
                    name: "waitForSale"
            )
        }

        // 延迟发货
        SaleTextVO saleTextInfo = getSaleText(sku)
        if (saleTextInfo?.text) {
            return saleTextInfo
        }

        // 营销文案兜底
        SaleTextVO saleText = new SaleTextVO(
                text: itemBase?.slogan,
                name: "slogan"
        )

        // 优惠信息
        if (Tools.isVirtualCouponItem()) {
            saleText.text =  "购买后自动发券，不支持退款"
            saleText.name = "coupon"
        }

        // 2107资源位配的tags
        // 大促tags和退券文案并存时：同时展示，退券文案在前
        if (activityDO?.eventTags) {
            String text = ""
            for (int index = 0; index < activityDO?.eventTags?.size(); index++) {
                if (index > 0) text += "，"
                text += activityDO?.eventTags?.get(index)?.tagText
            }
            if (!TextUtils.isEmpty(text)) {
                saleText.text = saleText.name == "coupon" ? "${saleText.text}，${text}" : text
                saleText.name = "dacu"
            }
        }

        return saleText
    }

    /**
     * 延迟发货文案
     * 获取延迟发货的时间，【购买后N日内发货】/【购买后Nmin-Nmax日内发货】
     * @param sku
     * @return
     */
    static SaleTextVO getSaleText(SkuDO sku) {

        boolean hasNewDelayship = false
        boolean inSpringFestival = Tools.inSpringFestival()
        boolean springFestivalShutdown = Tools.isSpringFestivalShutdownItem()   // 春节不打烊的标的商品

        // 定时发货
        SaleTextVO sendOutOnTimeInfo = getSendOutOnTime(sku)
        boolean isSendOutOnTime = !TextUtils.isEmpty(sendOutOnTimeInfo?.text)    // 是否是定时发货

        List <SkuData> skus = sku?.skus

        if (skus && skus?.size > 0) {
            skus.each {
                if (it?.delayHours) {
                    hasNewDelayship = true
                }
            }
        }

        /**
         * 1. 定时发货，区间展示最晚时间
         */
        if (isSendOutOnTime) {
            if (inSpringFestival && springFestivalShutdown) {
                return new SaleTextVO(
                        text: "春节快递休息，发货时间请咨询商家客服",
                        name: "springFestivalShutdown"
                )
            }
            return sendOutOnTimeInfo
        }


        /**
         * 2.春节打烊处理
         */
        if (inSpringFestival && !Tools.contain30DayDeliveryService()) {
            if (springFestivalShutdown) {
                // 打烊
                return new SaleTextVO(
                        text: "春节快递休息，发货时间请咨询商家客服",
                        name: "springFestivalShutdown"
                )
            } else {
                // 不打烊
                def services = [ServiceDetailEnum.FH_410,
                                ServiceDetailEnum.FH_420,
                                ServiceDetailEnum.FH_430,
                                ServiceDetailEnum.FH_448,
                                ServiceDetailEnum.FH_435,
                                ServiceDetailEnum.FH_440]
                boolean showBenefit = services.any { service -> Util.containsService(DetailContextHolder.get(), service) }

                // 非定时发货：24/36/72/48/5d/7d服务体系，春节不打烊
                // 定时发货不打烊：正常展示发货文案
                if (showBenefit && !hasNewDelayship) {
                    return new SaleTextVO(
                            text: "春节期间不打烊，照常发货",
                            name: "springFestivalNotShutdown"
                    )
                }
            }
        }


        /**
         * 3. 兜底
         */
        return new SaleTextVO()
    }

    // 定时发货
    static SaleTextVO getSendOutOnTime(SkuDO sku) {
        Integer minDelayHours = 0
        Integer maxDelayHours = 0
        boolean hasNewDelayship = false

        List <SkuData> skus = sku?.skus
        Boolean tooDelay = false
        String delayReason = ''

        if (skus && skus?.size > 0) {
            skus.each {
                if (it?.delayHours) {
                    hasNewDelayship = true
                    // 延迟超过或等于 7 天，红色显示，默认灰色小号字体
                    if (!tooDelay && Math.round(it?.delayHours / 24) >= 7) {
                        tooDelay = true
                    }
                    minDelayHours = it?.delayHours < minDelayHours || !minDelayHours ? it?.delayHours : minDelayHours
                    maxDelayHours = it?.delayHours > maxDelayHours || !maxDelayHours ? it?.delayHours : maxDelayHours
                }
                // 全款预售，多条理由取第一条
                if (it?.delayReason && !delayReason) {
                    delayReason = "${it?.delayReason}，"
                }
            }

            // 超过或等于 72 小时显示 日，否则显示小时
            String maxStr = maxDelayHours > 72 ? (Math.round(maxDelayHours / 24) + "日") : (maxDelayHours + "小时")
//            String minStr = minDelayHours > 72 ? Math.round(minDelayHours / 24) : (minDelayHours + (maxDelayHours > 72 ? "小时" : ""))
            String name = tooDelay ? 'tooDelaySendOut' : 'delaySendOut'

//            if (minDelayHours != maxDelayHours ) {
//                return new SaleTextVO(
//                        text: '购买后' + minStr + '-' + maxStr + '内发货',
//                        name: name
//                )
//            }

            if (maxDelayHours && !hasNewDelayship) {
                return new SaleTextVO(
                        name: name,
                        text: "${delayReason}购买后${maxStr}内发货"
                )
            }

        }
        return new SaleTextVO()
    }
}