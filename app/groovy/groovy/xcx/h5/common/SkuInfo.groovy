package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.core.util.MaitUtil
import groovy.xcx.h5.base.SkuInfoVO
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.service.item.domain.basic.ItemTagDO
import com.mogujie.contentcenter.utils.JsonUtil


/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "skuInfo")
public class SkuInfo implements IFourDependTranslator<ItemBaseDO, SkuDO, LiveSimpleDO, FastbuyDO, SkuInfoVO> {

    @Override
    SkuInfoVO translate(ItemBaseDO itemBase, SkuDO sku, LiveSimpleDO liveSimpleDO, FastbuyDO buyDo) {
        SkuInfoVO skuInfo = new SkuInfoVO(sku, itemBase, liveSimpleDO)

        BizType bizType = DetailContextHolder.get().getRouteInfo().bizType

        if (bizType == BizType.FASTBUY) {
            skuInfo.maxNumber = buyDo ? buyDo.limitNum : 1
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(145273)

        skuInfo.insuranceMait =  maitData;


        skuInfo.jsonExtra = DetailContextHolder.get().getItemDO().getJsonExtra()
        // 只需要pp标
        List<ItemTagDO> tags = DetailContextHolder.get().getItemDO().getItemTags()
        if (tags != null) {
            skuInfo.promotionItemTags = JsonUtil.toJson(tags.findAll {"pp" == it.getTagKey() || "1897" == it.getTagValue()})
        }
        skuInfo.jsonExtra = skuInfo.jsonExtra ?: ""
        skuInfo.promotionItemTags = skuInfo.promotionItemTags ?: ""

        DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()
        skuInfo.promotionExtraParams = ["cids": itemBase.cids, "shopTags": itemDO?.shopInfo?.tags]

        return skuInfo
    }
}