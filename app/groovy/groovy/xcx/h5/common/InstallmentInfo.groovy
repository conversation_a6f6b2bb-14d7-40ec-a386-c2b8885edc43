package groovy.xcx.h5.common

/**
 * Created by chang<PERSON><PERSON> on 17/11/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.EntryBarVO

@Translator(id = "installmentInfo", defaultValue = DefaultType.EMPTY_STRING)
class InstallmentInfo implements IOneDependTranslator<SkuDO, EntryBarVO> {

    @Override
    EntryBarVO translate(SkuDO sku) {
        if (sku?.freePhases) {
            def maitData = MaitUtil.getMaitData(50418)
            def maitInfo = maitData?.get(0)
            String freePhasesText = maitInfo?.get("freePhasesText")

            EntryBarVO installmentInfo = new EntryBarVO(
                    iconTitle: maitInfo?.get("iconTitle"),
                    title: freePhasesText?.replace("{phases}", sku?.freePhases?.toString()),
                    accessoryTitle: maitInfo?.get("accessoryTitle"),
                    linkUrl: maitInfo?.get("xcxLinkUrl"),
                    tagBgColor: maitInfo?.get("iconBackgroundColor") ?: "#ffeeee;",
                    tagTextColor: maitInfo?.get("iconTextColor") ?: "#fb4747;"
            )
            return installmentInfo
        } else {
            return null
        }
    }
}