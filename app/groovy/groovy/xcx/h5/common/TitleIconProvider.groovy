package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO

/**
 * 标题图标提供者类
 * 用于为其他类提供标题图标和价格历史信息
 */
@Translator(id = "titleIconProvider")
class TitleIconProvider implements ISixDependTranslator<ItemBaseDO, ActivityDO, GroupbuyingDO, PinTuanDO, NormalCountdownDO, PresaleDO, TitleIconResult> {

    /**
     * 标题图标结果类
     */
    static class TitleIconResult {
        String titleIcon
        String priceHistory
        
        TitleIconResult(String titleIcon, String priceHistory) {
            this.titleIcon = titleIcon
            this.priceHistory = priceHistory
        }
    }

    @Override
    TitleIconResult translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, GroupbuyingDO groupbuyingDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presaleDO) {
        if (!itemBaseDO) {
            return new TitleIconResult("", "")
        }
        
        // 调用工具类获取标题图标和价格历史
        String titleIcon
        String priceHistory
        (titleIcon, priceHistory) = TitleIconAndPriceHistoryUtil.getTitleIcon(itemBaseDO, activityDO, groupbuyingDO, pinTuanDO, normalCountdownDO, presaleDO)
        
        return new TitleIconResult(titleIcon ?: "", priceHistory ?: "")
    }
}
