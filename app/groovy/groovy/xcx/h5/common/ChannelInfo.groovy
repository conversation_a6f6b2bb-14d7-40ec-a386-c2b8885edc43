package groovy.xcx.h5.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.ChannelMeta
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO

@Translator(id = "channelInfo")
class ChannelInfo implements IOneDependTranslator<ChannelInfoDO, ChannelInfoVO> {

    static class ChannelInfoVO {
        String channel
        Short activityType
        String activityId
        Long activityIdInt
        BizExtraVO bizExtra
    }

    static class BizExtraVO {
        Long bizId
    }

    @Override
    ChannelInfoVO translate(ChannelInfoDO channelInfo) {
        if (!channelInfo) {
            return new ChannelInfoVO()
        }

        ChannelMeta channelMetaInfo = channelInfo?.channelMetaInfo
        Map<String, Object> extraDynMap = channelInfo?.extraDynMap

        String activityId = DetailContextHolder.get().getParam("activityId")
        Long activityIdInt = IdConvertor.urlToId(activityId)
        Long tuanId = extraDynMap?.get("tuanId") ? Long.valueOf(extraDynMap?.get("tuanId")?.toString()) : null

        return new ChannelInfoVO(
                channel: channelMetaInfo?.orderChannelId,
                activityType: channelMetaInfo?.outType,
                activityId: activityId,
                activityIdInt: activityIdInt,
                // 这里要注意缓存的问题，以后如果要新加字段，
                bizExtra: new BizExtraVO(
                        bizId: tuanId
                )
        )
    }
}