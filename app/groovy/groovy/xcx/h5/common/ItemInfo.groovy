package groovy.xcx.h5.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.live.domain.LiveSliceDO
import com.mogujie.detail.module.seo.domain.SeoDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.xcx.h5.base.CoverVideoVO
import groovy.xcx.h5.base.Util
import org.apache.http.util.TextUtils
import groovy.xcx.h5.base.constant.SourceType
/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-商品信息
 */

@Translator(id = "itemInfo")
class ItemInfo implements IFiveDependTranslator<ItemBaseDO, ExtraDO, SeoDO, LiveSliceDO, LiveSimpleDO, ItemInfoVO> {

    static class PickformeVO {
        String name;
        String icon;
        String link;
        String h5Link;
        String acm;
    }

    static class ItemInfoVO {
        String desc;
        String title;
        String itemId;
        Boolean isFaved;
        Integer cFav;
        String cids;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer state;
        /**
         * 开售时间，待开售商品
         */
        Long saleStartTime;
        /**
         * 商品类型，0为普通商品，1为预售商品
         */
        Integer saleType;

        Map<String, String> seo;

        List<String> numTags

        CoverVideoVO video

        Integer priceChannel

        Integer virtualItemType

        String slogan

        Long activityIdInt

        /**
         * 标题tag
         */
        List<String> titleTags
        List<String> titleTagsV2

        /**
         * 轮播图tag
         */
        List<String> picTags

        Boolean hideTitle

        String saleText

        /**
         * 暂时加上，之后去掉
         */
        String lowPrice;
        String highPrice;
        String lowNowPrice;
        String highNowPrice;

        /**
         * title 右侧的帮我选
         */
        PickformeVO pickforme;

        ItemInfoVO(ItemBaseDO item, ExtraDO extra, SeoDO seo, LiveSliceDO liveSlice, LiveSimpleDO liveSimple) {
            this.desc = item?.desc;
            this.title = item?.title;
            this.cids = item?.cids;
            this.itemId = item?.iid;
            this.isFaved = item?.isFaved;
            this.cFav = item?.cFav;
            this.state = item?.state;
            this.saleType = item?.saleType;
            this.saleStartTime = extra?.onSaleTime;
            this.seo = seo;
            this.numTags = item?.numTags
            this.priceChannel = item?.priceChannel ?: 0
            this.virtualItemType = item?.virtualItemType?.getCode()
            this.slogan = item?.slogan
            this.saleText = item?.slogan
            this.titleTags = []
            this.titleTagsV2 = []
            this.picTags = []


            if (item?.numTags?.contains("1538")) {
                Map<String, Object> maitData156421 = MaitUtil.getMaitData(156421)?.get(0)
                String icon = maitData156421?.get("tagImg")
                if (!TextUtils.isEmpty(icon)) {
                    this.titleTagsV2.add(0, icon)
                }
            }

            String activityId = DetailContextHolder.get().getParam("activityId")
            String fastbuyId = DetailContextHolder.get().getParam("fastbuyId")

            // 非法字符输入
            try {
                if (activityId) {
                    this.activityIdInt = IdConvertor.urlToId(activityId)
                } else if (fastbuyId) {
                    this.activityIdInt = IdConvertor.urlToId(fastbuyId)
                }
            } catch (Throwable ignore) {
            }

            RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()

            this.video = new CoverVideoVO(item, extra, liveSlice, routeInfo, liveSimple)

            // 自营标
            if (Tools.isSelfEmployedItem()) {
                List<Map<String, Object>> maitList =  MaitUtil.getMaitData(127253L)
                Map<String, Object> maitData = maitList?.get(0)

                // 老的标兼容(老自营标在标题前边)
                if (maitData?.get("selfIcon")) {
                    this.titleTags.add(maitData?.get("selfIcon")?.toString())
                }
                if (maitData?.get("topSelfIcon")) {
                    this.picTags.add(maitData?.get("topSelfIcon")?.toString())
                }
            }

            // 直播秒杀中标
            SourceType sourceType = Util?.getSourceType(DetailContextHolder.get())
            if (extra?.isLiveSeckill() && sourceType != SourceType.LIVE_WALL) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("liveSeckillIcon")
                if (!TextUtils.isEmpty(icon)) {
                    this.titleTagsV2.add(0, icon)
                }
            }

            // 官方推荐信息，不展示标题模块，只支持非渠道和快抢
            OfficialRecommend officialRecommend = item?.officialRecommend
//            RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()
            boolean showOfficialRecommend = (routeInfo.bizType == BizType.NORMAL || routeInfo.bizType == BizType.FASTBUY)
            if ((officialRecommend?.desc || officialRecommend?.title) && showOfficialRecommend) {
                this.hideTitle = true
            }

            this.lowNowPrice = item?.lowNowPrice;
            this.highNowPrice = item?.highNowPrice;

            // 判断是否能展示原价
            this.lowPrice = item.canShowStrikethroughPrice? item?.lowPrice : this.lowNowPrice;
            this.highPrice = item.canShowStrikethroughPrice? item?.highPrice : this.highNowPrice;

            // 帮我选（默认只有女装类目透出），渠道不透出
            if (routeInfo.bizType == BizType.NORMAL) {
                List<Map<String, Object>> maitList = MaitUtil.getMaitData(143035L)
                Map<String, Object> maitData = maitList?.get(0)
                String pickformeCids = maitData?.get("cids") ?: "683"

                if (Util.isInCids(pickformeCids, item)) {
                    String link = "https://h5.mogu.com/pickforme/launch/index.html?itemId=${item.iid}"
                           link = Util.addAcmToUrl(link, maitData?.get("acm"))
                    String xcxLink = URLEncoder.encode(link, "UTF-8")
                    this.pickforme = new PickformeVO(
                            icon: 'https://s10.mogucdn.com/mlcdn/c45406/191218_762ibk3def84blec68cbe4le770df_54x54.png',
                            name: '帮我选',
                            link: "/pages/web/index?login=true&share=true&src=${xcxLink}",
                            h5Link: link,
                            acm: maitData?.get("acm")
                    )
                }
            }
        }
    }

    @Override
    ItemInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, SeoDO seo, LiveSliceDO liveSlice, LiveSimpleDO liveSimple) {
        if (!itemBase) {
            return null
        }
        ItemInfoVO itemInfo = new ItemInfoVO(itemBase, extra, seo, liveSlice, liveSimple);
        return itemInfo
    }
}