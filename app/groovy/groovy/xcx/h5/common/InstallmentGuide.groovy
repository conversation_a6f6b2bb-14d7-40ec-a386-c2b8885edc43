package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ForetasteAuth
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.EntryBarVO

@Translator(id = "installmentGuide")
class InstallmentGuide implements IOneDependTranslator<ItemBaseDO, EntryBarVO> {

    @Override
    EntryBarVO translate(ItemBaseDO itemBase) {
        ForetasteAuth foretasteAuth = itemBase?.foretasteAuth
        // 用户未开通且可开通的场景，需要展示开通引导
        if (foretasteAuth?.state == 2) {
            List<Map<String, Object>> maitDataList = MaitUtil.getMaitData(108100)
            Map<String, Object> maitData = maitDataList?.get(0)

            String acm = maitData?.get("acm")
            String linkUrl = maitData?.get("linkUrl")

            if (!linkUrl) {
                return new EntryBarVO()
            }

            if (linkUrl.contains('?')) {
                linkUrl = "${linkUrl}&acm=${acm}".toString()
            } else {
                linkUrl = "${linkUrl}?acm=${acm}".toString()
            }

            return new EntryBarVO(
                    icon: maitData?.get("icon"),
                    title: maitData?.get("text"),
                    titleV2: "先享后付",
                    message: maitData?.get("text"),
                    linkUrl: linkUrl,
                    iconWidth: Integer.parseInt(maitData?.get("iconWidth")?.toString()),
                    iconHeight: Integer.parseInt(maitData?.get("iconHeight")?.toString()),
                    accessoryTitle: maitData?.get("accessoryTitle"),
                    accessoryTitleColor: maitData?.get("accessoryTitleColor"),
                    acm: acm,
            )
        } else {
            return new EntryBarVO()
        }
    }
}