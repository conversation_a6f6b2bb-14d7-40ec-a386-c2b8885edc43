package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Created by pananping on 2020/11/18.
 */
@Translator(id = "delayExpress", defaultValue = DefaultType.NULL)
class DelayExpress implements ITwoDependTranslator<ShopDO, SkuDO, Object> {

    @Override
    Object translate(ShopDO shopDO, SkuDO skuDO) {
        groovy.mgj.app.common.DelayExpress appTranslator = new groovy.mgj.app.common.DelayExpress()
        return appTranslator.translate(shopDO, skuDO)
    }
}