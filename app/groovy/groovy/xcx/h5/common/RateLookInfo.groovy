package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.rate.domain.RateDO

/**
 * Create by jinger on 2019/09/16 09:50
 */
@Translator(id = "rateLookInfo", defaultValue = DefaultType.NULL)
class RateLookInfo implements IOneDependTranslator<RateDO, RateLookInfoVO> {

    static class RateLookInfoVO {
        List<LookItemVO> list
        Integer total
        String moreLink     // 晒单更多链接
        String title        // 晒单 23 | 精选晒单 3
    }

    static class LookItemVO {
        private String contentId;
        private String image;
        private String link;
    }

    @Override
    RateLookInfoVO translate(RateDO rate) {

        if (DetailContextHolder.get().isDyn()) {
            return null;
        }

        // 只展示3个 & 打开开关
        if (rate?.contentInfos?.size() > 3 && rate?.switchContent) {
            List<LookItemVO> rateLookList = rate?.contentInfos?.collect {
                LookItemVO lookItem = new LookItemVO(
                        contentId: it?.contentId,
                        image: it?.cover,
                        link: it?.link
                )
                return lookItem
            }
            String title = rate?.contentTotal ? "精选晒单(${rate?.contentTotal})" : ""

            return new RateLookInfoVO(
                    title: title ?: "精选晒单",
                    list: rateLookList?.subList(0, 4),
                    total: rate?.contentTotal,
                    moreLink: rate?.contentMoreLink
            )
        }
        return new RateLookInfoVO()
    }
}