package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/03/2017.
 * H5公共模块-红人信息
 */

@Translator(id = "celebrityInfo")
class CelebrityInfo implements IOneDependTranslator<ExtraDO, CelebrityInfoVO> {

    static class CelebrityInfoVO {
        String avatarImg;
        String profileUrl;
        String userName;
        String uId;
        String certTagImg;
        String certTagName;
    }

    @Override
    CelebrityInfoVO translate(ExtraDO extra) {
        if (!extra?.celebrityInfo) {
            return null;
        }
        return new CelebrityInfoVO(
                avatarImg: extra?.celebrityInfo?.avatarImg,
                profileUrl: extra?.celebrityInfo?.profileUrl,
                userName: extra?.celebrityInfo?.userName,
                uId: extra?.celebrityInfo?.uId,
                certTagImg: extra?.celebrityInfo?.certTagImg,
                certTagName: extra?.celebrityInfo?.certTagName
        );
    }
}