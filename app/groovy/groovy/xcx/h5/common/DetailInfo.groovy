package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO
import com.mogujie.service.tangram.domain.entity.DetailModule

/**
 * Created by ch<PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-图文详情
 */

@Translator(id = "detailInfo")
class DetailInfo implements IOneDependTranslator<DetailDO, DetailInfoVO> {

    static class DetailInfoVO {
        String desc;
        List<DetailModule> detailImage
        ShopDecorateVO shopDecorate
        Boolean splitDetailImage
    }

    static class ShopDecorateVO {
        private String img

        private String link
    }

    @Override
    DetailInfoVO translate(DetailDO detail) {
        if (!detail) {
            return null;
        }
        return new DetailInfoVO(
                desc: detail?.desc,
                detailImage: detail?.detailImage,
                splitDetailImage: detail?.splitDetailImage,
                shopDecorate: new ShopDecorateVO(
                        img: detail?.shopDecorate?.img,
                        link: detail?.shopDecorate?.xcxLink
                )
        );
    }
}