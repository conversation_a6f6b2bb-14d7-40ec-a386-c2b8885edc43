package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by changsheng on 21/12/2017.
 */
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.xcx.h5.base.Util

@Translator(id = "sizeHelper")
class SizeHelper implements IThreeDependTranslator<SkuDO, SizeHelperDO, ItemBaseDO, SizeHelperInfoVO> {

    static class SizeHelperInfoVO {
        String title
        String entrance
        String size
        String sizeDesc
    }

    @Override
    SizeHelperInfoVO translate(SkuDO sku, SizeHelperDO sizeHelper, ItemBaseDO itemBase) {

        Boolean isNewSizeHelper = Util.isNewSizeHelper(itemBase)

        if (!sizeHelper || isNewSizeHelper) {
            return new SizeHelperInfoVO()
        }

        String title
        String size
        String entrance
        String sizeDesc

        //默认展示查看尺码表
        entrance = "查看尺码表"
        title = "尺码推荐: 暂无"
        // 已登录,但是未填写尺码参数,应该显示完善尺码入口
        if (DetailContextHolder.get().getLoginUserId() != null && !sizeHelper.userInfoFilled) {
            entrance = "完善尺码"
            title = "尺码推荐: 暂无，请先完善尺码"
        } else if (sizeHelper.matchedSizeType != null) {
            // 已登录, 有填写尺码参数
            for (int i = 0; i < sku?.skus?.size(); i++) {
                SkuData skuData = sku.skus[i]
                // 推荐的号型与当前商品sku匹配
                if (skuData?.sizeType == sizeHelper.matchedSizeType) {
                    size = skuData.size
                    title = "尺码推荐："
                    sizeDesc = "(根据个人尺码测算得出)"
                    break
                }
            }
        }

        return new SizeHelperInfoVO(
                title: title,
                entrance: entrance,
                size: size,
                sizeDesc: sizeDesc
        )
    }
}