package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "pintuanInfo", defaultValue = DefaultType.NULL)
class PintuanInfo implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}
//import com.mogujie.detail.core.annotation.Translator
//import com.mogujie.detail.core.translator.ITwoDependTranslator
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import groovy.xcx.h5.base.PintuanInfoVO
//
///**
// * Created by changsheng on 22/03/2017.
// * H5私有模块-拼团详情页-拼团信息
// */
//
//@Translator(id = "pintuanInfo")
//class PintuanInfo implements ITwoDependTranslator<PinTuanDO, ItemBaseDO, PintuanInfoVO> {
//
//
//    @Override
//    PintuanInfoVO translate(PinTuanDO pinTuan, ItemBaseDO itemBase) {
//        return new PintuanInfoVO(pinTuan, itemBase)
//    }
//}