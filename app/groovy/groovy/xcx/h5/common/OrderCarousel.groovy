package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/01/07.
 */
@Translator(id = "orderCarousel", defaultValue = DefaultType.NULL)
class OrderCarousel implements IOneDependTranslator<ExtraDO, Object> {

    @Override
    Object translate(ExtraDO extraDO) {
        groovy.mgj.app.common.OrderCarousel appTranslator = new groovy.mgj.app.common.OrderCarousel()
        return appTranslator.translate(extraDO)
    }
}