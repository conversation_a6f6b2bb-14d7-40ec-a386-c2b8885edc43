package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Create by jinger on 2019/07/31 20:30
 */
@Translator(id = "skuBarInfo", defaultValue = DefaultType.NULL)
class SkuBarInfo implements IOneDependTranslator<SkuDO, SkuBarInfoVO> {

    static class SkuItemVO {
        String color
        String label
        String image
        String stockId
    }

    static class SkuBarInfoVO {
        String labelV2
        String label
        String type
        List<SkuItemVO> list
    }

    @Override
    SkuBarInfoVO translate(SkuDO sku) {

        if (sku?.skus?.size() > 0) {

            // 颜色展示优先
            // 当SKU仅为色盘颜色&颜色数量>1时，展示颜色选择块
            // 当SKU为自定义数据&个数>1时，展示SKU图选择块

            String type = 'SKU_COLOR'
            Set<Integer> styleIdSet = new HashSet<>()
            List<SkuItemVO> list = new ArrayList<>()

            sku?.skus?.each {

                // 过滤重复的颜色或样式，会存在一个颜色对应多个sku的情况
                if (styleIdSet.contains(it?.styleId)) return

                if (it?.color == null || it?.color?.isEmpty()) {
                    type = 'SKU_IMAGE'
                }

                styleIdSet.add(it?.styleId)

                list.add(new SkuItemVO(
                        color: formatColor(it?.color),
                        label: it?.style,
                        image: it?.img,
                        stockId: it?.stockId
                ))
            }

            if (styleIdSet.size() > 1) {
                return new SkuBarInfoVO(
                        label: type == 'SKU_COLOR' ? '颜色' : '颜色规格',
                        labelV2: type == 'SKU_COLOR' ? '颜色' : '颜色\n规格',
                        type: type,
                        list: list
                )
            }

        }

        return  new SkuBarInfoVO()

    }

    // 注意 6位 8位 以及 # !!!
    static String formatColor(String color) {

        if (color == null || color?.isEmpty()) return ""

        return "#${color.substring(color.length() - 6, color.length())}"
    }
}