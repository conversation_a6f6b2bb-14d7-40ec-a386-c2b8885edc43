package groovy.xcx.h5.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.buyershow.domain.SizeInfo
import com.mogujie.detail.module.buyershow.domain.Source

/**
 * Created by chang<PERSON><PERSON> on 21/03/2017.
 * H5公共模块-买家秀
 */

@Translator(id = "buyerShowInfo")
class BuyerShowInfo implements IOneDependTranslator<BuyerShowDO, BuyerShowInfoVO> {

    static class BuyerShowInfoVO {
        Integer count;
        List<BuyerShowItemVO> items;

    }

    static class BuyerShowItemVO {
        com.mogujie.detail.module.buyershow.domain.UserInfo userInfo;
        SizeInfo sizeInfo;
        String content;
        List<String> imgs;
        Source source;
        String contentId;
        String rateId;
    }

    @Override
    BuyerShowInfoVO translate(BuyerShowDO buyerShow) {
        if (!buyerShow) {
            return null;
        }
        return new BuyerShowInfoVO(
                count: buyerShow?.count,
                items: buyerShow?.items?.collect {
                    return new BuyerShowItemVO(
                            userInfo: it?.userInfo,
                            sizeInfo: it?.sizeInfo,
                            content: it?.content,
                            imgs: it?.imgs,
                            source: it?.source,
                            contentId: it?.contentId ? IdConvertor.idToUrl(it?.contentId) : null,
                            rateId: it?.rateId ? IdConvertor.idToUrl(it?.rateId) : null
                    )
                }
        );
    }
}