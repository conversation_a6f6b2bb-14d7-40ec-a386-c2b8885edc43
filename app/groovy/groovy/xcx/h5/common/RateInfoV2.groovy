package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.rate.domain.DetailRate
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.rate.util.Constants
import com.mogujie.service.rate.domain.tag.RateTag
import com.mogujie.service.rate.util.RateUtils
import java.text.DecimalFormat

/**
 * Created by changsheng on 21/03/2017.
 * H5公共模块-商品评价
 */

@Translator(id = "rateInfoV2")
class RateInfoV2 implements IThreeDependTranslator<RateDO, ExtraDO, LiveSimpleDO, RateInfoVO> {

    static class RateInfoVO {
        List<DetailRateVO> list;
        Integer cRate;
        Integer imgTotal;
        List<RateTag> rateTags;
        Long sales;
        Boolean needDSR  // 是否需要评分模块
        String cScore // 评分
    }

    static class DetailRateVO {
        RateUserInfoVO user;
        List<String> images;
        String rateId;
        String content;
        Long created;
        Integer isAnonymous;
        String style;
        Boolean isProbation;
        String probation;
        Integer isEmpty;
        DetailRate append;
        String explain;
        String level;
        Boolean canExplain;
        List<String> extraInfo;
        boolean isBuyerShow;
        Map<String, String> sizeInfo;
        Long contentId;
        List userTags;
    }

    static class RateUserInfoVO {
        String uid;
        String uname;
        String avatar;
        String profileUrl;
        String tagIndex;
    }

    public static String hideUname(String uname) {
        if ((uname == null || uname.trim().isEmpty())) {
            return "***";
        } else {
            return uname.substring(0, 1) + "***" + uname.substring(uname.size() - 1);
        }
    }

    @Override
    RateInfoVO translate(RateDO rate, ExtraDO extra, LiveSimpleDO liveSimpleDO) {
        if (!rate) {
            return null;
        }
        List<DetailRateVO> rateList = rate?.list?.collect {
            DetailRateVO detailRate = new DetailRateVO(
                    user: new RateUserInfoVO(
                            uid: null,
                            uname: it?.user?.uname,
                            avatar: it?.user?.avatar,
                            profileUrl: it?.user?.profileUrl,
                            tagIndex: it?.user?.tagIndex
                    ),
                    userTags: it?.userTags,
                    images: rate?.switchContent ? (liveSimpleDO?.pickedExplainInfo != null ? it?.images : null) : it?.images, // 如果展示晒单，则评价不展示图片
                    rateId: it?.rateId,
                    content: it?.content,
                    created: it?.created,
                    style: it?.style,
                    isAnonymous: it?.isAnonymous,
                    probation: it?.probation,
                    isEmpty: it?.isEmpty,
                    append: it?.append,
                    isProbation: it?.isProbation,
                    explain: it?.explain,
                    level: it?.level,
                    canExplain: it?.canExplain,
                    extraInfo: it?.extraInfo,
                    isBuyerShow: it?.isBuyerShow,
                    sizeInfo: it?.sizeInfo,
                    contentId: it?.contentId
            );
            return detailRate;
        }

        List<RateTag> rateTags = rate?.rateTags ? (List<RateTag>)rate.rateTags.clone() : rate?.rateTags

        if (rate?.rateTags && rate.rateTags.size() > 0 && rate.imgTotal && rate.imgTotal > 0) {
            RateTag imgTag = new RateTag();
            imgTag.setNum(rate.imgTotal);
            imgTag.setEmotion("positive");
            imgTag.setProperty("图片");
            imgTag.setValue("有图片");
            rateTags.add(0, imgTag);
        }

        // 是否需要显示评分
        String needDSR_CONFIG  = MetabaseUtil.get("rateShowDSR")
        boolean needDSR = needDSR_CONFIG ? Boolean.parseBoolean(needDSR_CONFIG) : false

        // 评分
        String cScore = ""
        if (rate?.itemDsr) {
            int avgDSR = RateUtils.getDsrAvg(Arrays.asList(rate?.itemDsr?.desc, rate?.itemDsr?.quality))
            DecimalFormat decimalFormat = new DecimalFormat(".00")
            cScore = decimalFormat.format((double) (avgDSR / 100))
        }

        return new RateInfoVO(
                list: rateList,
                cRate: rate?.cRate,
                imgTotal: rate?.imgTotal,
                rateTags: rateTags,
                sales: extra?.sales,
                needDSR: needDSR,
                cScore: cScore
        )
    }
}