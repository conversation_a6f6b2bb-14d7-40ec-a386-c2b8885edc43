package app.groovy.groovy.xcx.h5.common

import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.spi.dslutils.Tools

class TitleIconAndPriceHistoryUtil {

    /**
     * 获取氛围标
     */
    static def getTitleIcon(
            ItemBaseDO itemBaseDO,
            ActivityDO activityDO,
            GroupbuyingDO groupbuyingDO,
            PinTuanDO pinTuanDO,
            NormalCountdownDO normalCountdownDO,
            PresaleDO presaleDO
    ) {
        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XSBK)
        CountdownInfo xinpin = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XINP)
        CountdownInfo shango = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.SHANGO)

        // 通过配置新增的kv标氛围
        CountdownInfo extraPreActivity, extraInActivity
        (extraPreActivity, extraInActivity) = NormalCountdownManager.getActivity(normalCountdownDO)

        String titleIcon = ""
        String priceHistory = ""

        // 氛围标 跟priceBannerV2以及AtmosphereBannerV2里标题前面的icon是配套的，判断条件保持一致
        // 预售
        if (presaleDO) {
            titleIcon = ""
        }
        // 待开售
        else if (itemBaseDO?.state == 3) {
            titleIcon = ""
        }
        // 主播推荐
        else if (groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend()) {
            Long maitID = 144975L
            Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
            titleIcon = maitData?.get("titleIcon")
        }
        // 当前是大促(包括品牌日等)正式期并且本商品是活动商品
        else if (activityDO && activityDO?.activityState == 2) {
            if(activityDO.activityTitleImage) {
                titleIcon = activityDO.activityTitleImage
            }
        }
        // 限时爆款。限时爆款没有预热，就是正式期
        else if(xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY){
            Map<String, Object> maitData = MaitUtil.getMaitData(xsbk?.maitId1110)?.get(0)
            titleIcon = maitData?.get("titleIcon")
        }
        // 团购正式期
        else if (groupbuyingDO && groupbuyingDO?.status == TuanStatus.IN && groupbuyingDO?.endTime) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
            titleIcon = maitData?.get("titleIcon")
        }
        // 招商非渠道拼团。没有预热期，就是正式期
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
            titleIcon = maitData?.get("titleIcon")
        }
        // 新品正式期
        else if (xinpin && xinpin.startTime <= System.currentTimeSeconds() && xinpin.endTime > System.currentTimeSeconds() && xinpin.state == CountdownState.IN_ACTIVITY) {
            Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
            titleIcon = maitData?.get("titleIcon")
            priceHistory = xinpin?.priceHistoryImg
        }
        // 闪购正式期
        else if (shango && shango.startTime <= System.currentTimeSeconds() && shango.endTime > System.currentTimeSeconds() && shango.state == CountdownState.IN_ACTIVITY) {
            Long maitID = shango?.maitId1110
            Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
            titleIcon = maitData?.get("titleIcon")
            priceHistory = shango?.priceHistoryImg
        }
        // 其他kv标配置的氛围正式期
        else if (extraInActivity) {
            Map<String, Object> maitData = MaitUtil.getMaitData(extraInActivity?.maitId1110)?.get(0)
            titleIcon = maitData?.get("titleIcon")
            priceHistory = extraInActivity?.priceHistoryImg
        }
        // 不是正式期，是预热或者普通
        else {
            long lastPreActivityStartTime = Long.MAX_VALUE
            boolean isPre = false

            // 当前是大促(包括品牌日等)预热期并且本商品是活动商品，并且比其他预热的开始时间要早
            if (activityDO && activityDO?.warmUpPrice?.price && activityDO?.activityState == 1
                    && activityDO.startTime < lastPreActivityStartTime) {
                if (activityDO.activityTitleImage) {
                    titleIcon = activityDO.activityTitleImage
                }

                lastPreActivityStartTime = activityDO.startTime
                isPre = true
            }
            // 团购预热期，并且比其他预热的开始时间要早
            if (groupbuyingDO?.status == TuanStatus.PRE && groupbuyingDO?.startTime
                    && groupbuyingDO.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
                titleIcon = maitData?.get("titleIcon")

                lastPreActivityStartTime = groupbuyingDO.startTime
                isPre = true
            }
            // 新品预热期，并且比其他预热的开始时间要早
            if (xinpin && xinpin.startTime > System.currentTimeSeconds() && xinpin.state == CountdownState.WARM_UP
                    && xinpin.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
                titleIcon = maitData?.get("titleIcon")
                priceHistory = xinpin?.priceHistoryImg

                lastPreActivityStartTime = xinpin.startTime
                isPre = true
            }
            // 闪购预热期，并且比其他预热的开始时间要早
            if (shango && shango.startTime > System.currentTimeSeconds() && shango.state == CountdownState.WARM_UP
                    && shango.startTime < lastPreActivityStartTime) {
                Long maitID = shango?.maitId1110
                Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
                titleIcon = maitData?.get("titleIcon")
                priceHistory = shango?.priceHistoryImg

                lastPreActivityStartTime = shango.startTime
                isPre = true
            }
            // 其他kv标配置的氛围预热期，并且比其他预热的开始时间要早
            if (extraPreActivity
                    && extraInActivity.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(extraPreActivity?.maitId1110)?.get(0)
                priceHistory = extraPreActivity?.priceHistoryImg
                titleIcon = maitData?.get("titleIcon")

                lastPreActivityStartTime = extraInActivity.startTime
                isPre = true
            }

            // 普通
            if (!isPre) {
                titleIcon = ""
            }
        }

        priceHistory = (priceHistory ?: "")
        return new Tuple2(titleIcon, priceHistory)
    }

}
