package groovy.xcx.h5.common

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.Platform
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.CommonSwitchUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.DetailShopCategory
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.shop.domain.ShopDsr
import com.mogujie.detail.module.shop.domain.ShopService
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-店铺信息
 */

@Translator(id = "shopInfo")
class ShopInfo implements ITwoDependTranslator<ShopDO, ItemBaseDO, ShopInfoVO> {

//    private static final Logger logger = LoggerFactory.getLogger(CommonSwitchUtil.class);

    static class DescInfoVO {
        boolean show
        String desc
    }

    static class ShopLabelVO {
        Integer id
        String text
    }

    static class ShopInfoVO {
        List<ShopDsr> score;
        List<ShopService> services;
        List<DetailShopCategory> categories;
        String cFans;
        String cSells;
        Boolean isMarked;
        Integer cGoods;
        String userId;
        String shopLogo;
        String name;
        String shopId;
        String tag;
        Integer type;
        Map<String, Object> shopHeader;
        Integer level;
        String star;
        String levelDescV2
        String shopUrl;
        String saleDesc                    // 在架商品
        String saleDescV2                  // 在架商品 999
        List<ShopLabelVO> shopLabels       // 缓存的店铺标签
        List<ShopLabelVO> dynShopLabels    // 店铺标签
        String shopLabelPriority           // 店铺优先级

        // 设置实时数据，其余走缓存
        void setDynShopInfo(ShopDO shop) {
            // 实时店铺标签
            this.dynShopLabels = shop?.dynLabels?.collect {
                new ShopLabelVO(
                        text: it.name,
                        id: it.sort
                )
            }

            // 推荐指数（老版本不展示）
            DescInfoVO descInfo = JSON.parseObject(MetabaseUtil.get("shopStarDesc"), DescInfoVO.class)
            this.levelDescV2 = descInfo?.show ? descInfo?.desc : ""
        }

        // 全量请求：设置缓存数据
        void setShopInfo(ShopDO shop, ItemBaseDO itemBase) {
            RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo();
            Platform platform = routeInfo.platform;
            if (platform == Platform.WX) {
                this.shopUrl = shop?.shopId ? ("//weixin.meilishuo.com/wx/shop/" + shop?.shopId) : null;
            } else {
                String url = "//m.mogujie.com/v8/meili/shop?shopid=" + shop?.shopId
                this.shopUrl = shop?.shopId ? StrategyUpUtil.upUrl(url) : null;
            }
            this.score = shop?.score?.grep {
                it?.name != "价格合理"
            };
            this.services = shop?.services;
            this.categories = shop?.categories;
            this.isMarked = shop?.isMarked;
            this.userId = shop?.userId;
            this.shopLogo = shop?.shopLogo;
            this.name = shop?.name;
            this.shopId = shop?.shopId;
            this.tag = shop?.tag;
            this.type = shop?.type;
            this.shopHeader = shop?.shopHeader;
            this.level = shop?.level;
            this.star = shop?.star;

//            String clientIp = DetailContextHolder?.get()?.getClientIp()
//            Boolean isDyn = DetailContextHolder?.get()?.isDyn()

//            logger.info("xcx-ShopDO-label: ${itemBase?.iid}, labels: {}, ip: ${clientIp}, isDyn: ${isDyn}", shop?.labels)
//            logger.info("xcx-ShopDO-dynlabel: ${itemBase?.iid}, dynlabels: {}, ip: ${clientIp}, isDyn: ${isDyn}, userId: ${itemBase?.loginUserId}", shop?.dynLabels)

            // 店铺标签（缓存）
            this.shopLabels = shop?.labels?.collect {
                label ->
                    return new ShopLabelVO(
                            text: label?.name,
                            id: label?.sort
                    )
            }


            // 店铺标签优先级，在前端做排序
            this.shopLabelPriority = MetabaseUtil.get("shopLabelPriority")


            if (shop?.getCFans() > 100000) {
                this.cFans = NumUtil.formatNum(shop?.getCFans() / 10000D, 1) + "万"
            } else {
                this.cFans = shop?.getCFans()?.toString()
            }

            if (shop?.getCSells() > 100000) {
                this.cSells = NumUtil.formatNum(shop?.getCSells() / 10000D, 1) + "万"
            } else {
                this.cSells = shop?.getCSells()?.toString()
            }

            // 在售商品数（会存在缓存）
            String saleDescTemplate = MetabaseUtil.get("shopSaleDesc")
            String saleDesc = saleDescTemplate ? saleDescTemplate.replace("\${cGoods}", "") : "在架商品 "
            this.saleDesc = saleDesc

            // 在架商品数只有在大于0才展示（19年双十一降级）
            if (shop?.getCGoods() && shop?.getCGoods() > 0) {
                this.cGoods = shop?.getCGoods();
                this.saleDescV2 = "${saleDesc}${this.cGoods}"
            }

        }
    }

    @Override
    ShopInfoVO translate(ShopDO shop, ItemBaseDO itemBase) {
        if (!shop) {
            return null
        }

        ShopInfoVO shopInfo = new ShopInfoVO()
        shopInfo?.setDynShopInfo(shop)  // 实时数据

        if (!DetailContextHolder.get().isDyn()) {
            shopInfo?.setShopInfo(shop, itemBase)     // 缓存数据
        }

        return shopInfo;
    }
}