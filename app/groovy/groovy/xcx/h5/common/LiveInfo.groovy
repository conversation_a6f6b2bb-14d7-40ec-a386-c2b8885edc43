package groovy.xcx.h5.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder

/**
 * Created by changsheng on 11/12/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.live.domain.ExplainInfo
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.live.domain.LiveType
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.xcx.h5.base.LinkInfoVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.XCXType

import java.math.RoundingMode
import java.text.DecimalFormat

@Translator(id = "liveInfo", defaultValue = DefaultType.EMPTY_STRING)
class LiveInfo implements IThreeDependTranslator<LiveDO, ShopDO, LiveSimpleDO, Object> {

    static class LiveInfoVO {
        String providerName
        String licenseImage
        List<LiveAnchorInfoVO> liveAnchorInfos
        String liveListUrl
        Integer liveStatus
        String anchorId   // 主播id，用于领券接口
    }

    static class LiveAnchorInfoVO {
        String name
        String avatar
        String liveUrl
        String userDesc
        String titleDesc
        String title
        LinkInfoVO link
    }

    static private String tenThousandFormat(Integer num) {
        if (num < 10000 ) {
            return num
        }
        double tenThousandNum = (double)num / 10000
        DecimalFormat df = new DecimalFormat("0.0")
        df.setRoundingMode(RoundingMode.DOWN)
        return  df.format(tenThousandNum) + '万'
    }


    @Override
    Object translate(LiveDO live, ShopDO shopDO, LiveSimpleDO liveSimple) {

//        if (DetailContextHolder?.get()?.isDyn()) {
//            return null
//        }

        // 直播商品进图墙
        if (liveSimple?.pickedExplainInfo) {

            ExplainInfo pickedExplainInfo = liveSimple?.pickedExplainInfo

            DecimalFormat df = new DecimalFormat("#.#")
            df.setRoundingMode(RoundingMode.HALF_DOWN)

            List<LiveAnchorInfoVO> liveAnchorInfos = []

            String fansDesc = "粉丝 ${tenThousandFormat(pickedExplainInfo?.fansNum)}"
            String heightDesc = pickedExplainInfo.actHeight ? "${df.format(pickedExplainInfo.actHeight)}cm" : ""
            String weightDesc = pickedExplainInfo?.actWeight ? "${df.format(pickedExplainInfo?.actWeight)}kg" : ""
            String userInfoDesc = [heightDesc, weightDesc]?.join(" ")

            List<String> descArr = []
            if (pickedExplainInfo?.fansNum) {
                descArr.push(fansDesc)
            }
            if (userInfoDesc) {
                descArr.push(userInfoDesc)
            }
            if (pickedExplainInfo?.city) {
                descArr.push(pickedExplainInfo?.city)
            }

            String anchorId = pickedExplainInfo?.actUserId

            LiveAnchorInfoVO liveAnchorInfo = new LiveAnchorInfoVO(
                    name: pickedExplainInfo?.actUserName,
                    avatar: pickedExplainInfo?.avatar,
                    userDesc: descArr?.join(" | "),
                    titleDesc: '查看主播',
                    link: new LinkInfoVO(
                            url: anchorId ? '/pages/lookPersonal/index?uid=' + anchorId : '',
                            h5Url: anchorId ? "https://h5.mogu.com/brand-content/personal-homepage.html?uid=" + anchorId : ''
                    )
            )

            liveAnchorInfos?.add(liveAnchorInfo)

            return new LiveInfoVO(
                    providerName: shopDO?.name,
                    liveAnchorInfos: liveAnchorInfos,
                    anchorId: anchorId
            )
        }

        // 直播供应链
        if (live?.liveType == LiveType.LIVE_SUPPLY_CHAIN) {

            String anchorId = ''

            DecimalFormat df = new DecimalFormat("#.#")
            df.setRoundingMode(RoundingMode.HALF_DOWN)

            return new LiveInfoVO(
                    providerName: live?.providerName,
                    licenseImage: live?.licenseImage,
                    anchorId: anchorId
            )

        }


        // 直播特卖
        if (live?.liveType == LiveType.OLD_LIVE_PROMOTION) {
            return new LiveInfoVO(
                    providerName: live?.providerName,
                    licenseImage: live?.licenseImage,
                    liveListUrl: "/pages/live/liveList/index"
            )
        }

        // 好物优选
        if (Util?.isHideEntryShop(shopDO) && shopDO?.name) {
            return new LiveInfoVO(
                    providerName: shopDO?.name
            )
        }

        // 小程序里兼容了liveInfo为{}的判断，没兼容的返回""
        Boolean isH5 = Util.getXcxType() == XCXType.H5
        Boolean isNZXCX = Util.getXcxType() == XCXType.XCX_NZ

        return isH5 || isNZXCX ? new LiveInfoVO() : null
    }
}