package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ForetasteAuth
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.xcx.h5.base.Util

@Translator(id = "useBeforeBuyInfo")
class UseBeforeBuyInfo implements IThreeDependTranslator<ItemBaseDO, PinTuanDO, PresaleDO, UseBeforeBuyInfoVO> {

    static final String USE_BEFORE_BUY_URL = StrategyUpUtil.upUrl("https://bfm.mogujie.com/m/buylater/apply")

    static class UseBeforeBuyInfoVO {
        // 标题
        String title
        // 主要文案
        String message
        // 开关是否开启
        Boolean enable
        // 开关是否可用
        Boolean disable
        // 角标图片
        String icon
        // 角标图片宽度
        Integer iconWidth
        // 先享后付按钮文案
        String buttonText
        // 先享后付按钮第二行提示文案
        String buttonTips
        // 先享后付重置默认按钮文案
        String defaultButtonText
        // 按钮背景样式
        String buttonStyle
        // 跳转中间页地址
        String url
    }

    @Override
    UseBeforeBuyInfoVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        ForetasteAuth foretasteAuth = itemBase?.foretasteAuth
        Boolean isPintuan = Util.isPintuanItem(itemBase, pinTuan, presale)
        // state == 0，预售 不展示先享后付
        // 非渠道拼团的单独购买，还是需要的
        // 用户可使用 && 商品使用 && 非预售 展示先享后付
        RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()
        if (!(foretasteAuth?.state in [1, 3] && ItemTag.FORETASTE in itemBase?.itemTags && ((routeInfo.bizType == BizType.NORMAL && presale == null) || routeInfo.bizType != BizType.NORMAL))) {
            return new UseBeforeBuyInfoVO()
        }

        // 取麦田资源位 角标
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(108100) //119850
        Map<String, Object> maitItem = maitData?.get(0)

        UseBeforeBuyInfoVO useBeforeBuyInfo = new UseBeforeBuyInfoVO(
                title: "先享后付",
                message: foretasteAuth?.message,
                buttonStyle: "background-image: linear-gradient(to bottom, #F7C831, #FFA732);",
                defaultButtonText: "立刻购买",
                buttonText: "先享后付",
        )

        // 1:用户开通可正常先付后买 2:用户未开通可正常先付后买，可以选择先享后付
        // 这次要改成全部关闭
        if (foretasteAuth?.state == 1 || foretasteAuth?.state == 2) {
            // useBeforeBuyInfo.enable = foretasteAuth?.selected
            useBeforeBuyInfo.enable = false
        } else {
            useBeforeBuyInfo.enable = false
        }

        // 未开通需要提示补充信息
        if (foretasteAuth?.state == 2) {
            useBeforeBuyInfo.buttonTips = "补充信息后即可享受"
        } else {
            useBeforeBuyInfo.buttonTips = ""
        }

        // 未开通，或者第一次使用，需要跳到中间页
        if (foretasteAuth?.state == 2 || foretasteAuth?.first) {
            useBeforeBuyInfo.url = maitItem?.get("buyLaterLink") ?: USE_BEFORE_BUY_URL
        } else {
            useBeforeBuyInfo.url = ""
        }

        // 先享后付不可用
        if (foretasteAuth?.state == 3) {
            useBeforeBuyInfo.disable = true
        } else {
            useBeforeBuyInfo.disable = false
        }

        if (maitItem != null && maitItem?.get("tagIcon") && maitItem?.get("tagIconWidth") > 0) {
            useBeforeBuyInfo.icon = maitItem?.get("tagIcon")
            useBeforeBuyInfo.iconWidth = Integer.parseInt(maitItem?.get("tagIconWidth")?.toString())
        } else {
            useBeforeBuyInfo.icon = ""
            useBeforeBuyInfo.iconWidth = 0
        }

        return useBeforeBuyInfo
    }
}