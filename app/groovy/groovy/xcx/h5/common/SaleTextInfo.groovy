package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.SaleTextVO

/**
 * Created by jinger on 2018/12/29.
 */
@Translator(id = "saleTextInfo", defaultValue = DefaultType.NULL)
class SaleTextInfo implements ITwoDependTranslator<ItemBaseDO, SkuDO, SaleTextVO> {

    @Override
    SaleTextVO translate(ItemBaseDO itemBase, SkuDO sku) {

        if (!itemBase || !sku) {
            return  null
        }

        SaleTextVO saleTextInfo = SaleTextInfoV2.getSaleText(sku)
        if (saleTextInfo?.text) {
            return saleTextInfo
        }

        // 营销文案兜底
        return new SaleTextVO(
                text: itemBase?.slogan,
                name: "slogan"
        )
    }
}