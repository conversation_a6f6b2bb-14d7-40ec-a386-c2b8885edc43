package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.itemParams.domain.ProductInfo
import com.mogujie.detail.module.itemParams.domain.Rule
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import groovy.xcx.h5.base.ItemParamsRuleVO
import groovy.xcx.h5.base.Util

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-商品参数
 */

@Translator(id = "itemParams")
class ItemParams implements IThreeDependTranslator<ItemParamsDO, Si<PERSON><PERSON>elperDO, ItemBaseDO, ItemParamsVO> {

    static class ItemParamsVO {
        ProductInfo info
        Rule rule
        /**
         * 推荐的行号，没有则为-1
         */
        Integer machedSizeLine

        /**
         * 表头单位数组
         */
        List<String> headUnits
    }

    @Override
    ItemParamsVO translate(ItemParamsDO itemParams, SizeHelperDO sizeHelper, ItemBaseDO itemBase) {
        if (!itemParams) {
            return null
        }

        Rule rule = itemParams?.rule

        Boolean isNewSizeHelper = Util.isNewSizeHelper(itemBase)

        ItemParamsRuleVO itemParamsRule = new ItemParamsRuleVO(rule, sizeHelper)

        return new ItemParamsVO(
                info: itemParams?.info,
                rule: isNewSizeHelper? null : itemParamsRule?.rule,
                machedSizeLine: itemParamsRule?.machedSizeLine,
                headUnits: itemParamsRule?.headUnits
        );
    }
}