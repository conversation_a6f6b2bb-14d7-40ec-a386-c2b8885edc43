package groovy.xcx.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 11/10/2021.
 * H5&xcx 特殊类目资质展示
 */

@Translator(id = "certInfo")
class CertInfo implements IOneDependTranslator<DetailDO, Object> {

    @Override
    Object translate(DetailDO detailDO) {
        if (!detailDO) {
            return null
        }

        return detailDO.getCertInfo()
    }
}
