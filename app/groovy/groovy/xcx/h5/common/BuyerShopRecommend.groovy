package groovy.xcx.h5.common
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.mgj.app.vo.HideControl

/**
 * Created by pananping on 2/23/21.
 */
@Translator(id = "buyerShopRecommend", defaultValue = DefaultType.NULL)
class BuyerShopRecommend implements IOneDependTranslator<LiveSimpleDO, Object> {

    class BuyerShopRecommendItem {
        String image
        String title
        String price
        String sales
        String xcxLink
        String h5Link
        Boolean showPlayIcon
    }

    class BuyerShopRecommendVO extends HideControl {
        List<BuyerShopRecommendItem> list
    }

    @Override
    Object translate(LiveSimpleDO liveSimpleDO) {
        if (liveSimpleDO?.liveItemRecommendInfos?.size() < 1) {
            return new BuyerShopRecommendVO(_extra_control_hide_: true)
        }

        return new BuyerShopRecommendVO(
                _extra_control_hide_: false,
                list: liveSimpleDO.liveItemRecommendInfos.size() > 3 ? liveSimpleDO.liveItemRecommendInfos.subList(0, 3).collect {
                    new BuyerShopRecommendItem(
                            image: it.itemImage,
                            title: it.title,
                            price: it.discountPrice,
                            sales: it.sale,
                            xcxLink: it.xcxLink,
                            h5Link: it.h5Link,
                            showPlayIcon: it.videoUrl ? true : false
                    )
                } : liveSimpleDO.liveItemRecommendInfos.collect {
                    new BuyerShopRecommendItem(
                            image: it.itemImage,
                            title: it.title,
                            price: it.discountPrice,
                            sales: it.sale,
                            xcxLink: it.xcxLink,
                            h5Link: it.h5Link,
                            showPlayIcon: it.videoUrl ? true : false
                    )
                }
        )
    }
}