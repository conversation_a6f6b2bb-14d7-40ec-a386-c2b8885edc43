package groovy.xcx.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.XCXType

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-登录信息
 */

@Translator(id = "userInfo")
class UserInfo implements ITwoDependTranslator<ItemBaseDO, ShopDO, UserInfoVO> {

    static class UserInfoVO {
        String userId;
        Boolean isLogin;
        String loginUserId;
        Boolean isSelf;
        Boolean admin;
        String shopId;
        String sellerId;
        Long systemTime;
        XCXType xcxType
        Integer xcxVersion
        BizType bizType
    }

    @Override
    UserInfoVO translate(ItemBaseDO itemBase, ShopDO shop) {

        BizType bizType = DetailContextHolder.get().getRouteInfo().bizType

        UserInfoVO userInfo = new UserInfoVO();
        userInfo.with {
            isLogin = itemBase?.loginUserId ? true : false;
            loginUserId = itemBase?.loginUserId;
            isSelf = itemBase?.isSelf;
            userId = itemBase?.userId;
            shopId = itemBase?.shopId;
            admin = itemBase?.admin;
            sellerId = shop?.userId;
            systemTime = System.currentTimeSeconds();
            xcxType = Util.getXcxType()
            xcxVersion = Util.getXcxVersion()
            bizType = bizType
        }
        return userInfo;
    }
}