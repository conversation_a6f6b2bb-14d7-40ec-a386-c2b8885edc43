package groovy.xcx.h5.ptg

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator

/**
 * Create by changsheng on 2018/9/12 00:24
 * Package groovy.xcx.h5.ptg
 */
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.CountdownMaitId

@Translator(id = "priceBanner")
class PriceBanner implements ITwoDependTranslator<ItemBaseDO, GroupbuyingDO, PriceBannerVO> {


    @Override
    PriceBannerVO translate(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: "大牌闪购专享"
                        )
                ]
            }

            Util.setEsiDataForPrice(itemPrice)

            // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
            itemPrice.isHideOldPrice(itemBase)
        }

        Long startTime = groupbuying?.startTime
        Long endTime = groupbuying?.endTime
        PriceBannerVO priceBanner

        if (!groupbuying) {
            priceBanner = new PriceBannerVO()
        } else {
            priceBanner = new PriceBannerVO()
            priceBanner.initPriceBanner(itemPrice, startTime, endTime, CountdownMaitId.CHANNEL_PTG)
        }
        return priceBanner
    }
}