package groovy.xcx.h5.ptg

/**
 * Created by ch<PERSON><PERSON><PERSON> on 05/12/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import groovy.xcx.h5.base.TopCountdownVO

@Translator(id = "groupBuyingCountdown")
class GroupBuyingCountdown implements IOneDependTranslator<GroupbuyingDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(GroupbuyingDO groupbuying) {
        Long startTime = groupbuying?.startTime
        Long endTime = groupbuying?.endTime
        Long nowTime = System.currentTimeSeconds()

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776)
        Map<String, Object> imgData = maitData?.get(0)

        def backgroundImg = imgData?.get("tuangouImage")
        def titleColor = imgData?.get("tuangouTitleColor")

        if (!groupbuying) {
            return new TopCountdownVO()
        } else if (nowTime < startTime) {
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: endTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }
}