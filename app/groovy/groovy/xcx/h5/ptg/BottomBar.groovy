package groovy.xcx.h5.ptg

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO

@Translator(id = "bottomBar")
class BottomBar implements ITwoDependTranslator<ItemBaseDO, GroupbuyingDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {
        GroupBuyingInfoVO groupBuyingInfo = new GroupBuyingInfoVO(itemBase, groupbuying)

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
        ))

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        String buttonText
        Boolean isDisabled = false
        String name
        switch (groupBuyingInfo?.state) {
            case 1:
                buttonText = "设置提醒"
                name = "alert"
                break

            case 2:
                buttonText = "立即抢购"
                name = "buy"
                break

            case 3:
                buttonText = "已抢光"
                name = "buy"
                isDisabled = true
                break

            case 4:
                buttonText = "活动结束"
                name = "buy"
                isDisabled = true
                break

            case 5:
                buttonText = "已下架"
                name = "buy"
                isDisabled = true
                break

            default:
                buttonText = "活动结束"
                name = "buy"
                isDisabled = true
                break
        }

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: name,
                text: buttonText,
                isDisabled: isDisabled,
                startTime: groupBuyingInfo?.startTime
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }
}