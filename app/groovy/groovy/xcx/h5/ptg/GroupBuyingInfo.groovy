package groovy.xcx.h5.ptg

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator

/**
 * Created by chang<PERSON><PERSON> on 04/12/2017.
 */
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "groupBuyingInfo")
class GroupBuyingInfo implements ITwoDependTranslator<ItemBaseDO, GroupbuyingDO, GroupBuyingInfoVO> {

    @Override
    GroupBuyingInfoVO translate(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {

        return new GroupBuyingInfoVO(itemBase, groupbuying)
    }
}