package groovy.xcx.h5.ptg

import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

class GroupBuyingInfoVO {
    Long startTime
    Long endTime
    /**
     * 1：活动未开始，2：活动中，3：库存不足，4：活动结束，5：商品下架
     */
    Integer state

//        TuanType type
//        TuanBizType bizType
//        TuanStatus status
//        String price
    Long activityIdInt

    GroupBuyingInfoVO(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {
        Long startTime = groupbuying?.startTime
        Long endTime = groupbuying?.endTime
        Long nowTime = System.currentTimeSeconds()

        Integer itemState = itemBase?.state
        Integer state

        if (itemState == 1 || itemState == 3) {
            state = 5
        } else if (nowTime < startTime) {
            state = 1
        } else if (nowTime > startTime && nowTime < endTime) {
            if (itemState == 2) {
                // 没有库存
                state = 3
            } else {
                state = 2
            }
        } else {
            state = 4
        }
        this.startTime = startTime
        this.endTime = endTime
        this.state = state
        this.activityIdInt = groupbuying?.activityId
    }

}