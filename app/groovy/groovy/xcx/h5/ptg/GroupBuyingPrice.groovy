package groovy.xcx.h5.ptg

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 04/12/2017.
 */
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

@Translator(id = "groupBuyingPrice")
class GroupBuyingPrice implements ITwoDependTranslator<ItemBaseDO, GroupbuyingDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: "大牌闪购专享"
                        )
                ]
            }

            Util.setEsiDataForPrice(itemPrice)
            return itemPrice
        }
    }
}