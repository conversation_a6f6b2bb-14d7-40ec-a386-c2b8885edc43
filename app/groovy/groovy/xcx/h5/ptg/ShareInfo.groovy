package groovy.xcx.h5.ptg

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ShareInfoVO

@Translator(id = "shareInfo")
class ShareInfo implements ITwoDependTranslator<ItemBaseDO, GroupbuyingDO, ShareInfoVO> {

    @Override
    ShareInfoVO translate(ItemBaseDO itemBase, GroupbuyingDO groupbuying) {
        GroupBuyingInfoVO groupBuyingInfo = new GroupBuyingInfoVO(itemBase, groupbuying)

        Boolean isNeedShareIntegral = groupBuyingInfo?.state in [1, 2]

        return new ShareInfoVO(isNeedShareIntegral)
    }
}