package groovy.xcx.h5.sizehelper

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator

/**
 * Created by changsheng on 19/12/2017.
 */
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.TrialReportInfo
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.itemParams.domain.Rule
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import groovy.xcx.h5.base.ItemParamsRuleVO

@Translator(id = "sizeHelperInfo")
class SizeHelperInfo implements IThreeDependTranslator<ItemBaseDO, SizeHelperDO, ItemParamsDO, SizeHelperInfoVO> {

    static class SizeHelperInfoVO {
        /**
         * 用户信息id
         */
        Long userInfoId

        /**
         * 用户是否已填写个人信息
         */
        Boolean userInfoFilled

        /**
         * 头像
         */
        String avatar

        /**
         * 身高
         */
        Double height

        /**
         * 体重
         */
        Double weight

        /**
         * 胸围
         */
        Double chest

        /**
         * 腰围
         */
        Double waist

        /**
         * 臀围
         */
        Double hipline

        /**
         * 尺码对照图
         */
        String sizeMeasureImg

        /**
         * 尺码参数表
         */
        Rule rule

        /**
         * 模特试穿报告
         */
        TrialReport[] trialReports

        /**
         * 推荐的行号，没有则为-1
         */
        Integer machedSizeLine

        /**
         * 表头单位数组
         */
        List<String> headUnits
    }

    static class TrialReport {

        /**
         * 试穿尺码
         */
        String tag

        /**
         * 模特信息
         */
        String title

        /**
         * 试穿评价
         */
        String message
    }

    @Override
    SizeHelperInfoVO translate(ItemBaseDO itemBase, SizeHelperDO sizeHelper, ItemParamsDO itemParams) {

        if (DetailContextHolder.get().isDyn()) {
            return null
        }
        Rule rule = itemParams?.rule

        ItemParamsRuleVO itemParamsRule = new ItemParamsRuleVO(rule, sizeHelper)

        itemParamsRule?.rule?.key = ""
        itemParamsRule?.rule?.desc = ""


        List<TrialReport> trialReports = new ArrayList<>()
        if (itemBase != null && itemBase.trialReportInfos != null) {
            for (TrialReportInfo trial : itemBase.trialReportInfos) {
                trialReports.add(new TrialReport(
                        tag: "试穿${trial.trialSize}",
                        title: "${trial.modelName}: 身高${trial.height}cm  体重${trial.weight}kg  胸围${trial.chest}cm",
                        message: trial.effect
                ))
            }
        }
        return new SizeHelperInfoVO(
                userInfoId: sizeHelper?.userInfoId,
                userInfoFilled: sizeHelper?.userInfoFilled,
                avatar: sizeHelper?.avartar,
                height: sizeHelper?.height,
                weight: sizeHelper?.weight,
                chest: sizeHelper?.chest,
                waist: sizeHelper?.waist,
                hipline: sizeHelper?.hipline,
                sizeMeasureImg: getSizeImg(itemBase?.cids),
                trialReports: trialReports,
                rule: itemParamsRule?.rule,
                machedSizeLine: itemParamsRule?.machedSizeLine,
                headUnits: itemParamsRule?.headUnits
        )
    }

    static String getSizeImg(String cids) {
        if (cids == null) {
            return null
        }
        if (cids.contains("#710#")) {
            return ImageUtil.img("/mlcdn/c45406/170330_32k878ki186jag03eb00l9k2840k0_1125x2130.png")
        } else if (cids.contains("#684#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_6d5a9f04jk6a6dga0cc63g7g9dd75_1125x1950.png")
        } else if (cids.contains("#706#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_061lc266cbfe4dd79kk9glce60ahc_1125x1560.png")
        } else if (cids.contains("#705#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_66kh5fh6h302al1dgcc3a4a1l1k7d_1125x2250.png")
        } else {
            return null
        }
    }
}