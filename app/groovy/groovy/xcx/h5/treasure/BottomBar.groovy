package groovy.xcx.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.treasure.domain.TreasureDO
import com.mogujie.marketing.duobao.enums.BizStatus
import groovy.xcx.h5.base.BottomBarButtonPopUpVO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO
import groovy.xcx.h5.base.ItemState

/**
 * Created by jinger on 2018/11/9.
 */
@Translator(id = "bottomBar", defaultValue = DefaultType.NULL)
class BottomBar implements ITwoDependTranslator<TreasureDO, ItemBaseDO, BottomBarVO> {

    @Override
    BottomBarVO translate(TreasureDO treasure, ItemBaseDO itemBase) {

        Integer itemState = itemBase?.state

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        BottomBarButtonVO favButton = new BottomBarButtonVO(
                type: "icon",
                name: "treasureShopFav",
                width: 165,
        )

        List<BizStatus> showPopupStatus = [BizStatus.notStart, BizStatus.notGet, BizStatus.hasGet]
        if (showPopupStatus.contains(treasure?.status)) {
            favButton.with {
                popup = new BottomBarButtonPopUpVO(
                        image: 'https://s10.mogucdn.com/mlcdn/c45406/181114_3h7784kkl0eeg0b6c191ejb9a9cck_180x62.png',
                        text:  '中奖率更高哦！',
                        duration: 0,
                        width: 180,
                        align: 'center',
                        textColor: '#fff',
                        fontSize: 24
                )
            }
        }

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
                width: 165,
        ))

        // 收藏
        buttons.add(favButton)

        // treasure 为空，视为活动失效
        if (!treasure) {
            treasure = new TreasureDO()
            treasure.status = BizStatus.expired
        }

        // 活动状态
        String buttonText = '活动已结束'
        Boolean isDisabled = false
        String name = 'buy'
        String linkUrl = ''


        // 未中奖跳转链接
        List<Map<String, Object>> maitList =  MaitUtil.getMaitData(130162L)
        Map<String, Object> maitData = maitList?.get(0)
        String notAwardLink = maitData?.get("notAwardLink")


        switch (treasure?.status) {
            case BizStatus.notStart:
                buttonText = '未开始'
                name = 'buy'
                isDisabled = true
                break

            case BizStatus.notGet:
                buttonText = '分享获取夺宝码'
                name = 'share'
                break

            case BizStatus.hasGet:
                buttonText = '邀请好友夺宝'
                name = 'share'
                break

            case BizStatus.beReadyLottery:
                buttonText = '正在开奖中'
                name = 'buy'
                isDisabled = true
                break

            case BizStatus.notAward:
                buttonText = '未中奖，去逛逛'
                name = 'link'
                linkUrl = notAwardLink ?: StrategyUpUtil.upUrl('/pages/web/index?share=true&login=true&src=https%3a%2f%2fact.mogujie.com%2fduobaohuodong')
                break

            case BizStatus.notRecieve:
                buttonText = '领取奖品'
                name = 'treasure'
                break

            case BizStatus.recieved:
                buttonText = '已领取'
                name = 'buy'
                isDisabled = true
                break

            case BizStatus.expired:
                buttonText = '活动已结束'
                name = 'buy'
                isDisabled = true
                break

            case BizStatus.invilid:
                buttonText = '活动已结束'
                name = 'buy'
                isDisabled = true
                break
        }

        // 库存不足或下架
        if (itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_OUT_OF_STOCK) {
            buttons.add(new BottomBarButtonVO(
                    type: 'button',
                    name: 'buy',
                    text: getBuyText(itemState, buttonText),
                    isDisabled: true
            ))

        } else {
            buttons.add(new BottomBarButtonVO(
                    type: 'button',
                    name: name,
                    text: buttonText,
                    isDisabled: isDisabled,
                    linkUrl: linkUrl
            ))
        }


        return new BottomBarVO(
                buttons: buttons
        )

    }

    static private String getBuyText(Integer itemState, String buttonText) {
        switch (itemState) {
            case 0: return buttonText
            case 1: return "已下架"
            case 2: return "卖光啦"
            default: return buttonText
        }
    }
}