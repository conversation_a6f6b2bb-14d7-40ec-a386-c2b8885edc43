package groovy.xcx.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.CommonBannerVO

/**
 * Created by jinger on 2018/11/15.
 */
@Translator(id = "treasureBanner", defaultValue = DefaultType.NULL)
class TreasureBanner implements IOneDependTranslator<ItemBaseDO, CommonBannerVO> {


    @Override
    CommonBannerVO translate(ItemBaseDO input1) {
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(130162)
        Map<String, Object> itemData = maitData?.get(0)

        def bannerImage = itemData?.get('bannerImage')
        def bannerLink = itemData?.get('bannerLink')

        return  new CommonBannerVO(
                image: bannerImage,
                link: bannerLink
        )
    }
}