package groovy.xcx.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.treasure.domain.TreasureDO
import com.mogujie.marketing.duobao.enums.BizStatus
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceLinkVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

/**
 * Created by jinger on 2018/11/9.
 */
@Translator(id = "treasurePrice", defaultValue = DefaultType.NULL)
class TreasurePrice implements ITwoDependTranslator<ItemBaseDO, TreasureDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, TreasureDO treasure) {

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (itemBase) {

            Integer treasureCodeNums = treasure?.treasureCodeNums

            String linkText = ''

            // 未开始的时候展示0个夺宝码
            if ([BizStatus.notStart, BizStatus.notGet].contains(treasure?.status)) {
                linkText = '您已获得0个夺宝码'
            }

            // 开始后展示正确的夺宝码数，开奖及开奖后不显示
            Boolean showTreasureCode = [BizStatus.hasGet].contains(treasure?.status)
            if (showTreasureCode && treasureCodeNums > 0) {
                linkText = '您已获得' + treasureCodeNums + '个夺宝码'
            }


            itemPrice.with {
                oldPrice = getOldPriceForce(itemBase)
                nowPrice = '0'
                priceTags = [
                        new PriceTagVO(
                                text: "0元夺宝",
                                bgColor: "#FFE8EE",
                                textColor: "#FF5777"
                        )
                ]
            }
            if (linkText) {

                List<Map<String, Object>> maitList =  MaitUtil.getMaitData(130162L)
                Map<String, Object> maitData = maitList?.get(0)
                String myDuobaoLink = maitData?.get("myDuobaoLink")
                itemPrice.with {
                    priceLink = new PriceLinkVO(
                            text: linkText,
                            textColor: "#FF5777",
                            link: myDuobaoLink ?: StrategyUpUtil.upUrl('/pages/web/index?share=true&login=true&src=https%3a%2f%2fact.mogujie.com%2ffastbuy%2fmyreminderh5')  // 跳转到我的夺宝页
                    )
                }
            } else {
                itemPrice.priceLink = new PriceLinkVO()
            }
        }

        Util.setEsiDataForPrice(itemPrice)

        return itemPrice
    }
}