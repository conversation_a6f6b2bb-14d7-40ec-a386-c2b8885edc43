package groovy.xcx.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.treasure.domain.TreasureDO
import com.mogujie.marketing.duobao.enums.BizStatus
import groovy.xcx.h5.base.TopCountdownVO

/**
 * Created by jinger on 2018/11/9.
 */
@Translator(id = "treasureCountdown", defaultValue = DefaultType.NULL)
class TreasureCountdown implements IOneDependTranslator<TreasureDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(TreasureDO treasure) {
        if (!treasure) {
            treasure = new TreasureDO()
            treasure.status = BizStatus.expired
            treasure.startTime = 0
            treasure.endTime = 0
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776)
        Map<String, Object> itemData = maitData?.get(0)

        def backgroundImg = itemData?.get('treasureImage')
        def titleColor = itemData?.get('treasureTitleColor')

        Long now = System.currentTimeSeconds()
        Long endTime = treasure?.endTime

        // 活动已结束，显示氛围不显示倒计时
        List<BizStatus> showHalfStatus = [BizStatus.expired, BizStatus.invilid]
        if (showHalfStatus.contains(treasure?.status)) {
            return new TopCountdownVO(
                    image: backgroundImg,
                    titleColor: titleColor,
                    isCountdomShow: false
            )
        }

        // 未开始/开奖后，不显示氛围
        List<BizStatus> hideStatus = [BizStatus.notStart, BizStatus.beReadyLottery, BizStatus.recieved, BizStatus.notRecieve, BizStatus.notAward]
        if (hideStatus.contains(treasure?.status)) {
            return  new TopCountdownVO()
        }

        // 活动进行中，显示倒计时
        List<BizStatus> showAllStatus = [BizStatus.hasGet, BizStatus.notGet]
        if (showAllStatus.contains(treasure?.status)) {
            return new TopCountdownVO(
                    text: '距结束仅剩',
                    countdown: endTime - now,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return  new TopCountdownVO()
        }
    }
}