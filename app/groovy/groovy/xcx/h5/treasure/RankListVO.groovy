package groovy.xcx.h5.treasure

import com.mogujie.detail.module.treasure.domain.TreasureUser

class RankListVO {

    String rankTitle
    List <RankItemVO> rankColumns

    RankListVO(List<TreasureUser> rankList) {

        Integer maxLen = 5

        this.rankTitle = '中奖概率排行榜'

        List<String> imgList = ['https://s10.mogucdn.com/mlcdn/c024f5/180511_2ag2e7cbcfbgbed099hd0705ggda3_42x56.png', 'https://s10.mogucdn.com/mlcdn/c024f5/180511_444fdd28i8gge41g2286ld77gd8hd_42x56.png', 'https://s10.mogucdn.com/mlcdn/c024f5/180511_4ah3gfd62878k1j72i37a32blfk67_42x56.png', 'https://s10.mogucdn.com/mlcdn/c024f5/180511_34e5g62l76g3keg1l5g782c0d8g24_44x44.png', 'https://s10.mogucdn.com/mlcdn/c024f5/180511_16cc1535a43bh41e68jk4fi77511c_44x44.png']


        Integer rankLen = rankList.size() > maxLen ? maxLen : rankList.size()

        List<TreasureUser> rankColumns = rankList.subList(0, rankLen)

        this.rankColumns = new ArrayList<RankItemVO>()
        List<String> dataIcon = new ArrayList<String>()
        List<String> dataName = new ArrayList<String>()
        List<String> dataRate = new ArrayList<String>()

        for (int i = 0; i < rankLen; i++)
        {
            dataIcon.add(imgList.get(i))
            dataName.add(rankColumns.get(i)?.nickName)
            dataRate.add(rankColumns.get(i)?.treasureCodeNums + '倍')
        }

        this.rankColumns.add(new RankItemVO(
                "title": "名次",
                "data": dataIcon,
                "type": "image"
        ))

        this.rankColumns.add(new RankItemVO(
                "title": "用户名",
                "data": dataName
        ))

        this.rankColumns.add(new RankItemVO(
                "title": "中奖概率翻倍",
                "data": dataRate
        ))


    }

}