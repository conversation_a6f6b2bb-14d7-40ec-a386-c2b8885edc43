package groovy.xcx.h5.treasure

import com.mogujie.detail.module.treasure.domain.TreasureAwardUser

import java.text.SimpleDateFormat


class WinnerVO {

    List<InfoVO> infoList

    String avatar

    // 皇冠icon
    String crownImage

    // 已获得icon
    String winningIcon

    WinnerVO(TreasureAwardUser awardUser) {
        this.crownImage = 'https://s10.mogucdn.com/mlcdn/c45406/181115_09457k21h6af171kd1kh62g7ffkl2_35x33.png'
        this.winningIcon = 'https://s10.mogucdn.com/mlcdn/c45406/181114_3g4i217j66a3gbcd6el1b9hglhl5c_124x124.png'
        this.avatar = awardUser?.avatar
        this.infoList = new ArrayList<InfoVO>()

        if (awardUser?.nickName) {
            this.infoList.add(new InfoVO(
                    "label": "获奖用户",
                    "value": awardUser?.nickName
            ))
        }

        if (awardUser?.awardTime) {
            Long awardTime = new Long(awardUser?.awardTime)
            String awardTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(awardTime * 1000L))
            this.infoList.add(new InfoVO(
                    "label": "开奖时间",
                    "value": awardTimeStr
            ))
        }

        if (awardUser?.treasureCode) {
            this.infoList.add(new InfoVO(
                    "label": "夺宝号码",
                    "value": awardUser?.treasureCode
            ))
        }

        if (awardUser?.treasureCodeNums) {
            this.infoList.add(new InfoVO(
                    "label": "参与次数",
                    "value": awardUser?.treasureCodeNums + '次'
            ))
        }
    }
}
