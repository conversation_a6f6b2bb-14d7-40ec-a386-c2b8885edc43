package groovy.xcx.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.treasure.domain.TreasureAwardUser
import com.mogujie.detail.module.treasure.domain.TreasureDO
import com.mogujie.marketing.duobao.enums.BizStatus

/**
 * Created by jinger on 2018/11/9.
 */
@Translator(id = "treasureInfo", defaultValue = DefaultType.NULL)
class TreasureInfo implements IOneDependTranslator<TreasureDO, TreasureVO> {

    @Override
    TreasureVO translate(TreasureDO treasure) {
        if (!treasure) {
            return  new TreasureVO()
        }

        TreasureVO treasureInfo = new TreasureVO(
                hasGet: treasure?.status == BizStatus.hasGet,
                notGet: treasure?.status == BizStatus.notGet
        )

        // 活动开始到开奖前展示中奖概率排行榜
        Boolean showRankList = [BizStatus.hasGet, BizStatus.notGet].contains(treasure?.status)

        // rankList
        if (treasure?.rankList?.size() > 0 && showRankList) {
            treasureInfo.with {
                rankInfo = new RankListVO(treasure?.rankList)
            }
        }

        // winner
        if (treasure?.awardList && treasure.awardList[0]) {

            TreasureAwardUser awardUser = treasure.awardList[0]

            treasureInfo.with {
                winner = new WinnerVO(awardUser)
            }
        }


        return treasureInfo
    }
}