package groovy.xcx.h5.base

class ShareInfoVO {

    /**
     * 是否要展示分享金信息，并且是否需要在分享成功之后请求发放积分接口
     */
    Boolean isNeedShareIntegral = false

    /**
     * 直接取业务开关，代表了用户打开分享的小程序卡片的时候，是否需要请求积分翻倍接口@花泪
     */
    Boolean shareIntegral = false

    /**
     * 用户打开分享的小程序卡片时，是否需要请求抽奖接口@延霄
     */
    Boolean shareLottery = false

    /**
     * 用户打开分享的小程序卡片时，是否需要请求分享打点接口@金吾
     */
    Boolean shareLog = false

    /**
     * 如果isNeedShareIntegral为true，则返回所有分享金相关麦田配置信息
     */
    ShareIntegralInfoVO shareIntegralInfo = new ShareIntegralInfoVO()

    /**
     * 如果shareLottery为true，则返回分享抽奖麦田配置数据
     */
    ShareLotteryInfoVO shareLotteryInfo = new ShareLotteryInfoVO()

    ShareInfoVO(Boolean isNeedShareIntegral) {
        Boolean shareIntegralSwitch = Util.getSwitchConfig("shareIntegralSwitch")
        Boolean shareLotterySwitch = Util.getSwitchConfig("shareLotterySwitch")
        Boolean shareLogSwitch = Util.getSwitchConfig("shareLogSwitch")

        if (isNeedShareIntegral && shareIntegralSwitch) {
            // 商品可购买，并且开关是开的
            this.shareIntegralInfo = new ShareIntegralInfoVO()
            this.isNeedShareIntegral = true
        }

        if (isNeedShareIntegral) {
            // 分享抽奖和分享打点，需要判断商品状态和商品类型
            this.shareLottery = shareLotterySwitch
            this.shareLog = shareLogSwitch
        }

        if (isNeedShareIntegral && shareLotterySwitch) {
            // 如果商品可购买，分享抽奖开关开着，则请求相关麦田数据
            this.shareLotteryInfo = new ShareLotteryInfoVO()
        }

        this.shareIntegral = shareIntegralSwitch
    }
}