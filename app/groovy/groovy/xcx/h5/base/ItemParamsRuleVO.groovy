package groovy.xcx.h5.base

import com.mogujie.detail.module.itemParams.domain.Rule
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO

/**
 * Created by changsheng on 21/12/2017.
 */
class ItemParamsRuleVO {

    Rule rule

    Integer machedSizeLine

    List<String> headUnits

    ItemParamsRuleVO(Rule ruleObj, SizeHelperDO sizeHelper) {

        List<String> headUnits = []

        Integer machedSizeLine = -1

        Rule rule = ruleObj

        // 先来排个序
        List<List<String>> table = rule?.tables?.size() > 0 ? rule?.tables?.get(0) : null

        // 只有有尺码助手，并且带号型的尺码表，才需要排序和单位
        if (sizeHelper != null && table != null && table.size() > 0 && table.get(0) != null && table.size() > 2) {
            // 找到号型这一行
            Integer sizeTypeIndex = null
            for (int i = 0; i < table[0].size(); i++) {
                String th = table[0].get(i)
                if ("号型" == th) {
                    sizeTypeIndex = i
                }
            }

            // 换位置
            List<List<String>> orderedTable = new ArrayList<>()
            if (sizeTypeIndex != null && sizeTypeIndex > 1) {
                for (List<String> line : table) {
                    List<String> orderedLine = new ArrayList<>(line)
                    String sizeTypeVal = orderedLine.remove(sizeTypeIndex)
                    orderedLine.add(1, sizeTypeVal)
                    orderedTable.add(orderedLine)
                }
                table = orderedTable

                List<List<List<String>>> tableList = new ArrayList<>()
                tableList.add(table)
                rule?.tables = tableList
            }

            // 增加单位数组，第二项以后单位为"cm"
            List<String> tableHead = table.get(0)
            tableHead.eachWithIndex { head, index ->
                if (index != 0 && head != "号型") {
                    headUnits.add("cm")
                } else {
                    headUnits.add("")
                }

            }

            // 算出推荐的行号
            String matchedSizeType = sizeHelper?.matchedSizeType
            if (matchedSizeType != null) {
                for (int i = 1; i < table.size(); i++) {
                    List<String> valueLines = table[i]
                    for (String valItem : valueLines) {
                        if (valueLines.size() >= 2 && matchedSizeType == valueLines[1]) {
                            machedSizeLine = i
                        }
                    }
                }
            } else {
                machedSizeLine = -1
            }

        }

        this.rule = rule
        this.machedSizeLine = machedSizeLine
        this.headUnits = headUnits
    }
}