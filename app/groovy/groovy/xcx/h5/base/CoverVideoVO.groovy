package groovy.xcx.h5.base

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.module.extra.domain.ExplainInfo
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.live.domain.LiveSliceDO
import groovy.xcx.h5.base.constant.XCXType
import org.apache.commons.lang.StringUtils

/**
 * Create by jinger on 2019/10/30 18:46
 * 封面视频
 */
class CoverVideoVO {

    private String cover;

    private Long videoId;

    private Integer width;

    private Integer height;

    private Boolean muted = false;

    private Boolean autoplay = false;

    private String url

    CoverVideoVO(ItemBaseDO itemBase, ExtraDO extra, LiveSliceDO liveSlice, RouteInfo routeInfo, LiveSimpleDO liveSimple) {

        /**
         * 抖音小程序直播切片
         */
        if (liveSlice?.videoId && (routeInfo.bizType == BizType.TTNORMAL)) {
            this.videoId = liveSlice?.videoId
            this.cover = liveSlice?.coverImage
            return
        }

        /**
         * 直播商品进图墙视频切片（wifi下静音自动播放）
         */
        if (liveSimple?.pickedExplainInfo) {
            this.videoId = liveSimple?.pickedExplainInfo?.videoId
            this.muted = true
            this.autoplay = true
            this.url = liveSimple?.pickedExplainInfo?.slice
        }

        /**
         * 商家后台设置的封面视频
         */
        if (itemBase?.video?.videoId) {
            this.videoId = itemBase?.video?.videoId
            this.cover = itemBase?.video?.cover
            this.width = itemBase?.video?.width
            this.height = itemBase?.video?.height
            return
        }

        /**
         * 直播讲解视频，只有在女装的非渠道详情页
         * 出现条件：
         *      1. 没有设置商品视频
         *      2. 是直播秒杀商品
         *      3. 出现直播模块，且主播 id 直播中模块的一致
         *      4. 出现讲解模块就不显示（目前逻辑：出现直播模块，就不会出现讲解模块）
         */

        if ((Util.getXcxType() == XCXType.XCX_NZ) && (routeInfo.bizType == BizType.NORMAL) && extra?.isLiveSeckill()) {

            if (liveSimple?.actUserInfo?.isLiving()) {
                String anchorId = IdConvertor.idToUrl(liveSimple?.actUserInfo?.userId?:0)
                ExplainInfo explainInfo = extra?.explainInfos?.find { item ->
                    StringUtils.equals(anchorId, item?.actUserId)
                }
                if (explainInfo?.videoId) {
                    this.videoId = explainInfo?.videoId
                    this.cover = explainInfo?.img
                }
            }
        }

    }
}
