package groovy.xcx.h5.base

import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.sku.domain.AddressInfo
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.adt.DetailContextHolder

class SkuInfoVO {

    String title
    List<SkuData> skus
    List<PropInfo> props
    String styleKey
    String sizeKey
    String priceRange
    String defaultPrice
    Integer totalStock
    String mainPriceStr
    String subPriceStr
    Boolean canInstallment
    Integer limitTotalStock
    Integer limitNum
    String limitDesc
    String highNowPrice
    String lowNowPrice
    Integer freePhases
    AddressInfo addressInfo

    /**
     * 商品级限购
     */
    Integer purchaseLimit

    /**
     * 展示优惠时需要透传的extra，减少对应后端查商品表
     */
    String jsonExtra

    /**
     * 展示优惠时需要透传的商品标，减少对应后端查商品表
     */
    String promotionItemTags

    /**
     * 展示优惠时需要透传的除了上面jsonExtra和promotionItemTags之外其他需要透传的参数
     */
    Map<String, String> promotionExtraParams

    /**
     * 额外添加的属性
     */
    Integer maxNumber
    Integer minNumber
    Boolean isJdItem
    Boolean showDelayTime // 是否在sku显示延迟发货

    /**
     * 当前sku满足优惠信息接口需要的参数（自动领券）
     */
    AutoCouponVO autoCouponInfo

    /**
     * 新保险
     */
    List insuranceMait


    SkuInfoVO(SkuDO sku, ItemBaseDO itemBase, LiveSimpleDO liveSimpleDO) {
        this.title = sku?.title
        this.skus = sku?.skus
        this.props = sku?.props
        this.styleKey = sku?.styleKey
        this.sizeKey = sku?.sizeKey
        this.priceRange = sku?.priceRange
        this.defaultPrice = sku?.defaultPrice
        this.totalStock = sku?.totalStock
        this.mainPriceStr = sku?.mainPriceStr
        this.subPriceStr = sku?.subPriceStr
        this.canInstallment = sku?.canInstallment
        this.limitTotalStock = sku?.limitTotalStock
        this.limitNum = sku?.limitNum
        this.limitDesc = sku?.limitDesc
        this.highNowPrice = sku?.highNowPrice
        this.lowNowPrice = sku?.lowNowPrice
        this.freePhases = sku?.freePhases
        this.addressInfo = sku?.addressInfo ?: new AddressInfo()
        this.isJdItem = Tools.isJdItem()
        this.showDelayTime = false
        this.purchaseLimit = itemBase?.purchaseLimit

        this.autoCouponInfo = new AutoCouponVO(sku, liveSimpleDO)

        BizType bizType = DetailContextHolder.get().getRouteInfo().bizType
        String channelType = DetailContextHolder.get().getRouteInfo().channelType

        // 虚拟商品、医美商品，限购一件
        if (itemBase?.virtualItemType != VirtualItemType.NORMAL || Tools.isMedicalBeautyItem()
                || bizType == BizType.JIAJIAGOU || channelType == 'livelottery') {
            this.maxNumber = 1

            // 限购优先级: maxNumber > purchaseSkuLimit、purchaseLimit
            if (this.purchaseLimit) {
                this.purchaseLimit = 1
            }
            this.skus.forEach({ i ->
                if (i.purchaseSkuLimit) {
                    i.purchaseSkuLimit = 1
                }
            })
        }
    }
}