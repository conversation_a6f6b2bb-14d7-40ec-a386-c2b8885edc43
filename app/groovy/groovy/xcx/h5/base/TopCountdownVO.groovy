package groovy.xcx.h5.base

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * 公共VO-顶部倒计时
 */
class TopCountdownVO {
    Long countdown = 0
    String text = ""
    String image = ""

    // 是否展示倒计时，false表示是纯文字
    Boolean isCountdomShow = true

    String timeText

    String timeColor

    // 文字部分字体大小
    // Integer textFontSize = 26
    // String textColor = "#FFFFFF"

    // 数字部分字体大小
    // Integer numFontSize = 36
    // String numCColor = "#FFF002"

    //
    String titleColor = "#FFFFFF"
}
