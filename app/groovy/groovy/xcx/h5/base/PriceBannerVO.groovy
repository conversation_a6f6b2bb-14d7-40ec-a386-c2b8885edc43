package groovy.xcx.h5.base

import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import groovy.xcx.h5.base.constant.CountdownType

import java.text.SimpleDateFormat

/**
 * Create by changsheng on 2018/8/24 11:30
 * Package groovy.xcx.h5.base
 */

class PriceBannerVO {
    // 倒计时状态，IN表示活动中，PRE表示预热
    CountdownType type

    // 大促预热期券后价样式
    String discountPriceColor
    String discountPriceBgColor

    // 价格相关
    String nowPrice
    String oldPrice
    String currency
    String priceSplit
    String highNowPrice
    List<PriceTagVO> priceTags
    String priceColor
    String eventPrice
    String eventPriceDesc
    List<PriceTagVO> eventTags
    // 券后价
    String couponPrice
    String couponDesc
    String highPriceDesc

    // 比价相关
    // 比价外网价
    String outNetPrice
    // 比价外网图
    String outNetImage
    // 是否为比价氛围
    Boolean isParity
    // 比价文案
    String platformText
    String outText
    // 比价样式
    String outGoodsTag
    String outPriceColor
    String hasImgCoverBg
    String coverColor

    // 进度条
    String progressText
    Double progress
    String progressColor
    String progressBgColor

    // 快抢关注数量
    String priceDesc

    String bgImage

    // 倒计时相关，优先展示countdownText
    Long countdown
    String countdownText
    String countdownTitle
    String countdownTitleColor

    // 标题Tag和利益点banner
    String titleTagImage
    String activityBannerImage
    PriceLinkVO activityBannerInfo

    String sales

    // 价格趋势图
    CommonBannerVO priceHistoryInfo

    /**
     * 通过type判断活动状态
     *
     * @param type
     * @param itemPrice
     * @param startTime
     * @param endTime
     * @param maitId
     */
    void initPriceBanner(CountdownType type, ItemPriceVO itemPrice, Long startTime, Long endTime, Long maitId) {
        this.type = type
        this.setPriceInfo(itemPrice)
        this.setCountdownText(startTime, endTime)
        this.setCountdownImage(maitId)
    }

    /**
     * 参数为一个对象传入（比如隐藏倒计时）
     */
    void initPriceBanner(PriceBannerParamVO param) {
        this.type = param?.type
        this.setPriceInfo(param?.itemPrice)
        this.setCountdownImage(param?.maitId)
        if (!param?.isHideCountdown) {
            this.setCountdownText(param?.startTime, param?.endTime)
        }
    }

    /**
     * 通过时间判断当前活动状态
     *
     * @param itemPrice
     * @param startTime
     * @param endTime
     * @param maitId
     */
    void initPriceBanner(ItemPriceVO itemPrice, Long startTime, Long endTime, Long maitId) {
        Long nowTime = System.currentTimeSeconds()
        if (nowTime < startTime) {
            this.type = CountdownType.PRE
        } else if (nowTime > startTime && nowTime < endTime) {
            this.type = CountdownType.IN
        } else {
            return
        }
        this.type = type
        this.setPriceInfo(itemPrice)
        this.setCountdownText(startTime, endTime)
        this.setCountdownImage(maitId)
    }

    /**
     * 比价氛围
     *
     * @param itemPrice
     * @param outNetPrice
     * @param outNetImage
     * @param maitId
     */
    void initPriceBanner(ItemPriceVO itemPrice, String outNetPrice, String outNetImage, Long maitId) {
        this.isParity = true
        this.platformText = '蘑菇价'
        this.outText = '外网价'
        this.outNetImage = outNetImage
        this.outNetPrice = outNetPrice
        this.sales = itemPrice?.sales
        this.nowPrice = itemPrice?.highNowPrice ? itemPrice.nowPrice + "起" : itemPrice.nowPrice
        this.currency = itemPrice.currency
        this.setParityStyle(maitId)
    }

    void setPriceInfo(ItemPriceVO itemPrice) {
        this.nowPrice = itemPrice?.nowPrice
        this.oldPrice = itemPrice?.oldPrice
        this.currency = itemPrice?.currency
        this.priceSplit = itemPrice?.priceSplit
        this.highNowPrice = itemPrice?.highNowPrice
        this.priceTags = itemPrice?.priceTags
        this.eventPrice = itemPrice?.eventPrice
        this.eventPriceDesc = itemPrice?.eventPriceDesc?.text ? itemPrice?.eventPriceDesc?.text + "：" : ""
        this.eventTags = itemPrice?.eventTags
        this.couponPrice = itemPrice?.couponPrice
        this.sales = itemPrice?.sales
        this.couponDesc = itemPrice?.couponDesc
        this.highPriceDesc = itemPrice?.highPriceDesc
    }

    void setCountdownText(Long startTime, Long endTime) {
        if (this.type == CountdownType.PRE) {
            this.setPreCountdownText(startTime)
        } else if (this.type == CountdownType.IN) {
            this.setInCountdownText(endTime)
        }
    }

    /**
     * 设置预热时间
     *
     * 如果时间大于72小时，则展示开始时间，精确到分钟
     * 否则展示倒计时
     *
     * @param startTime
     */
    void setPreCountdownText(Long startTime) {
        // 预热
        Long nowTime = System.currentTimeSeconds()
        Long countdown = startTime - nowTime
        this.setPreCountdownText(startTime, countdown)
    }

    void setPreCountdownText(Long startTime, Long countdown) {
        // 预热
        Long nowTime = System.currentTimeSeconds()
        if (countdown == null) {
            countdown = startTime - nowTime
        }
        Boolean showCountdownText = countdown > 72 * 60 * 60
        String format = "M月d日 HH:mm"
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format)
        String countdownText = simpleDateFormat.format(new Date(startTime * 1000))
        // 24点只展示日期
        // if (countdownText.endsWith("00:00")) {
        //     countdownText = countdownText.split(" ")[0]
        // }

        this.countdownTitle = showCountdownText ? "开始时间" : "距开始"
        this.countdownText = showCountdownText ? countdownText : ""
        this.countdown = countdown
    }

    /**
     * 设置正式期时间-非渠道预热期专用
     *
     * 如果时间大于72小时，则展示开始时间，精确到分钟
     * 否则展示倒计时
     *
     * @param startTime
     */
    void setPreCountdownTextForNormal(Long startTime, Long countdown) {
        Long nowTime = System.currentTimeSeconds()
        if (countdown == null) {
            countdown = startTime - nowTime
        }
        Boolean showCountdownText = countdown > 72 * 60 * 60
        String format = "M月d日 HH:mm"
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format)
        String countdownText = simpleDateFormat.format(new Date(startTime * 1000))
        // 24点只展示日期
        // if (countdownText.endsWith("00:00")) {
        //     countdownText = countdownText.split(" ")[0]
        // }

        countdownText = countdownText + "开始"

        this.countdownTitle = showCountdownText ? "" : "距开始"
        this.countdownText = showCountdownText ? countdownText : ""
        this.countdown = countdown
    }

    /**
     * 设置正式期时间
     *
     * 如果时间大于72小时，则展示距离结束x天x小时，精确到小时
     * 否则展示倒计时
     *
     * @param startTime
     */

    void setInCountdownText(Long endTime) {
        // 正式期
        Long nowTime = System.currentTimeSeconds()
        countdown = endTime - nowTime
        this.setInCountdownText(endTime, countdown)
    }

    void setInCountdownText(Long endTime, Long countdown) {
        // 正式期
        Long nowTime = System.currentTimeSeconds()
        if (countdown == null) {
            countdown = endTime - nowTime
        }
        Boolean showCountdownText = countdown > 72 * 60 * 60
        String format = "d天h时"
        // if (countdown % (24 * 60 * 60) == 0) {
        //     format = "d天"
        // }
        String countdownText = Util.formatTime(countdown, format)

        this.countdownTitle = "距结束"
        this.countdownText = showCountdownText ? countdownText : ""
        this.countdown = countdown
    }

    void setCountdownImage(Long maitId) {
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(maitId)
        Map<String, Object> countdownInfoImgData = maitData?.get(0)

        if (this.type == CountdownType.PRE && countdownInfoImgData?.get("preCoverBg")) {
            this.bgImage = countdownInfoImgData?.get("preCoverBg")
        } else {
            this.bgImage = countdownInfoImgData?.get("coverBg")
        }
        this.countdownTitleColor = countdownInfoImgData?.get("countdownColor")
        this.priceColor = countdownInfoImgData?.get("priceColor")
        this.titleTagImage = countdownInfoImgData?.get("titleIcon")

        this.discountPriceColor = countdownInfoImgData?.get("discountPriceColor")
        this.discountPriceBgColor = countdownInfoImgData?.get("discountPriceBgColor")

        // 兼容老的 banner 图
        this.activityBannerImage = countdownInfoImgData?.get("activityBanner")

        this.activityBannerInfo = new PriceLinkVO(
                link: countdownInfoImgData?.get("xcxActivityBannerLink"),
                h5Link: countdownInfoImgData?.get("h5ActivityBannerLink"),
                image: countdownInfoImgData?.get("activityBanner")
        )
    }

    // 同 activityBannerImage 位置
    void setActivityBannerInfo(PriceLinkVO activityBannerInfo) {
        if (activityBannerInfo?.image) {
            this.activityBannerInfo = activityBannerInfo
        }
    }

    void setPriceHistoryImage(CountdownInfo countdownInfo) {
        if (countdownInfo?.priceHistoryImg) {
            this.priceHistoryInfo =  new CommonBannerVO(
                    image: countdownInfo?.priceHistoryImg
            )
        }
    }

    /**
     * 设置比价氛围条样式
     *
     * @param maitId
     */
    void setParityStyle(Long maitId) {
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(maitId)
        Map<String, Object> parityStyle = maitData?.get(0)
        this.hasImgCoverBg = parityStyle?.hasImgCoverBg
        this.bgImage = parityStyle?.coverBg
        this.outGoodsTag = parityStyle?.goodsTag
        this.coverColor = parityStyle?.coverColor
        this.outPriceColor = parityStyle?.outPriceColor
        this.priceColor = parityStyle?.priceColor
    }

    /**
     * 设置大促预热期间样式
     *
     * @param priceColor
     * @param priceBgColor
     */
    void setActivityPriceStyle(String priceColor, String priceBgColor) {
        this.discountPriceColor = priceColor
        this.discountPriceBgColor = priceBgColor
    }

}