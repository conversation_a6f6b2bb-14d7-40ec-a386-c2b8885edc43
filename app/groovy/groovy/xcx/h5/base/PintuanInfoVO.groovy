 package groovy.xcx.h5.base

 class PintuanInfoVO {
 }

// import com.mogujie.detail.core.adt.DetailContextHolder
// import com.mogujie.detail.core.adt.RouteInfo
// import com.mogujie.detail.core.constant.BizType
// import com.mogujie.detail.core.util.NumUtil
// import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
// import com.mogujie.detail.module.pintuan.domain.PinTuanDO
// import com.mogujie.enzo.api.dto.LotteryResultDTO
// import com.mogujie.enzo.api.dto.ProgressDTO
// import com.mogujie.enzo.api.dto.UserInfo

// class PintuanInfoVO {
//     Integer tuanType
//     Boolean isSystem
//     Integer lotteryProcess
//     Integer awardNum
//     Integer tuanNum
//     Boolean isNew
//     Long countdown
//     Long startTime
//     Long endTime
//     Integer successTuanNum
//     Boolean isExpire
//     List<UserInfo> successTuan
//     List<UserInfo> joinTuanList
//     SkuInfoVO skuInfo
//     String tips
//     String normalPrice
//     String pintuanPrice
//     Integer maxNumber
//     /**
//      * 拼团状态
//      * 1：已下架，2：普通+未开始，3：普通+已结束，4：普通+已抢光，5：正常拼团
//      */
//     Integer state
//     String currency
//     Integer outType
//     /**
//      * 优惠券团是否已达到最大可领取数
//      * true:表示用户已不可再领取
//      */
//     Boolean isCouponLimited
//     /**
//      * 免单开团次数
//      */
//     Integer couponFreeNum

//     ProgressDTO progressDTO

//     List<LotteryResultDTO> lotteryResultList

//     String activityId


//     PintuanInfoVO(PinTuanDO pinTuan, ItemBaseDO itemBase) {
//         if (!itemBase || Boolean.valueOf(DetailContextHolder.get().getParam("noPintuan"))) {
//             return
//         }

//         ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

//         itemPrice.setNowPriceByRange(itemBase);
//         itemPrice.setOldPriceByRange(itemBase);

//         String tips = null;
//         Long countdown = null;
//         Integer state = 0;

//         /**
//          * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
//          */
//         Integer itemState = itemBase?.state;
//         SkuInfoVO pintuanSkuInfo = pinTuan?.skuInfo ? new SkuInfoVO(pinTuan?.skuInfo, itemBase) : null
//         Integer pintuanStock = pintuanSkuInfo?.totalStock;
//         Integer tuanNum = pinTuan?.tuanNum;
//         Integer tuanType = pinTuan?.tuanType;


//         Boolean isExpire = pinTuan?.isExpire;
//         Long now = System.currentTimeSeconds();
//         Long startTime = pinTuan?.startTime;
//         Long remainTime = pinTuan?.remainTime;

//         if (itemState == 3 || itemState == 1) {
//             state = 1
//         } else if (!pinTuan) {
//             state = 3
//         } else if (now < startTime) {
//             tips = "距开始仅剩：";
//             countdown = startTime - now;
//             // 1：均置灰，2：普通+未开始
//             // 活动未开始，只有普通商品下架，才展示下架，否则正常购买
//             state = 2
//         } else if (now > startTime && !isExpire) {
//             tips = "距结束仅剩："
//             countdown = remainTime

//             RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()
//             if ((tuanType == 3 || tuanType == 8 || routeInfo.bizType == BizType.NORMAL) && pintuanStock > 0) {
//                 // 抽奖团或者非渠道拼团不用判断成团人数
//                 state = 5
//             } else if (tuanType != 3 && routeInfo.bizType == BizType.PINTUAN && pintuanStock >= tuanNum) {
//                 // 5：正常，渠道拼团
//                 state = 5
//             } else {
//                 // 1：均置灰，4：普通+已抢光
//                 state = 4
//                 if (pintuanSkuInfo != null) {
//                     pintuanSkuInfo?.totalStock = 0
//                     pintuanSkuInfo?.skus?.each {
//                         it?.stock = 0
//                     }
//                 }
//             }
//         } else if (isExpire) {
//             // 1：均置灰，3：普通+已结束
//             state = 3
//         }

//         // 限购
//         Integer maxNumber = null
//         // 团长免单
//         if (tuanType == 5 && pinTuan?.couponFreeNum > 0) {
//             maxNumber = 1
//         } else if (pinTuan?.isLimit) {
//             maxNumber = pinTuan?.limitNum
//         }

//         if (maxNumber != null && maxNumber > 0 && (pintuanSkuInfo?.maxNumber == null || (pintuanSkuInfo?.maxNumber > 0 && maxNumber < pintuanSkuInfo?.maxNumber))) {
//             pintuanSkuInfo?.setMaxNumber(maxNumber)
//         }

//         String pintuanPrice

//         // 预热期间展示预热价
//         if (state == 2 && pinTuan?.lowActivityPrice != null) {
//             pintuanPrice = NumUtil.formatNum(pinTuan?.lowActivityPrice / 100D)
//         } else if (pintuanSkuInfo?.lowNowPrice) {
//             pintuanPrice = pintuanSkuInfo?.lowNowPrice
//         } else {
//             pintuanPrice = itemPrice.nowPrice
//         }

//         this.tuanType = pinTuan?.tuanType
//         this.isSystem = pinTuan?.system
//         this.lotteryProcess = pinTuan?.lotteryProcess
//         this.awardNum = pinTuan?.awardNum
//         this.tuanNum = pinTuan?.tuanNum ?: 0
//         this.isNew = pinTuan?.isNew()
//         this.countdown = countdown
//         this.startTime = pinTuan?.startTime
//         this.endTime = pinTuan?.endTime
//         this.successTuanNum = pinTuan?.successTuanNum
//         this.joinTuanList = pintuanStock > 0 ? (pinTuan?.joinTuanList ?: []) : []
//         this.isExpire = pinTuan?.isExpire()
//         this.successTuan = pinTuan?.successTuan ?: []
//         this.skuInfo = pintuanSkuInfo
//         this.tips = tips
//         this.normalPrice = itemPrice.nowPrice
//         this.pintuanPrice = pintuanPrice
//         this.state = state
//         // 团长免单的团限购一件
//         this.maxNumber = maxNumber
//         this.currency = itemBase?.currency ?: "¥"
//         this.outType = pinTuan?.outType
//         this.isCouponLimited = pinTuan?.getCouponLimited()
//         this.couponFreeNum = pinTuan?.couponFreeNum
//         this.progressDTO = pinTuan?.progressDTO
//         this.lotteryResultList = pinTuan?.lotteryResultList
//         this.activityId = pinTuan?.activityId
//     }
// }