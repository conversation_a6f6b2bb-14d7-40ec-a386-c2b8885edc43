package groovy.xcx.h5.base

import com.mogujie.detail.core.util.MaitUtil

class ShareIntegralInfoVO {

    String shareIcon
    String bgImage
    String friendsIcon
    String momentsIcon
    String titleIcon
    String title
    String ruleTitle
    String ruleLink
    String color
    String strongColor
    List<ShareIntegralRulesVO> rules

    String newTitleImage

    ShareIntegralInfoVO() {
        List<Map<String, Object>> rulesMaitData = MaitUtil.getMaitData(106819)
        List<ShareIntegralRulesVO> rules = []

        for (Map<String, Object> rule in rulesMaitData) {
            rules.add(new ShareIntegralRulesVO(
                    titleNumber: rule?.get("titleNumber"),
                    titleDesc: rule?.get("titleDesc"),
                    ruleContent: rule?.get("ruleContent"),
                    ruleDesc: rule?.get("ruleDesc"),
                    ruleTips: rule?.get("ruleTips"),
                    tips: rule?.get("tips")
            ))
        }

        List<Map<String, Object>> shareIntegralInfoMaitData = MaitUtil.getMaitData(106740)
        Map<String, Object> shareIntegralInfoMaitDataItem = shareIntegralInfoMaitData?.get(0)

        this.shareIcon = shareIntegralInfoMaitDataItem?.get("shareIcon")
        this.bgImage = shareIntegralInfoMaitDataItem?.get("bgImage")
        this.friendsIcon = shareIntegralInfoMaitDataItem?.get("friendsIcon")
        this.momentsIcon = shareIntegralInfoMaitDataItem?.get("momentsIcon")
        this.titleIcon = shareIntegralInfoMaitDataItem?.get("titleIcon")
        this.title = shareIntegralInfoMaitDataItem?.get("title")
        this.ruleTitle = shareIntegralInfoMaitDataItem?.get("ruleTitle")
        this.ruleLink = shareIntegralInfoMaitDataItem?.get("ruleLink")
        this.color = shareIntegralInfoMaitDataItem?.get("color")
        this.strongColor = shareIntegralInfoMaitDataItem?.get("strongColor")
        this.rules = rules
        this.newTitleImage = shareIntegralInfoMaitDataItem?.get("newTitleImage")

    }
}