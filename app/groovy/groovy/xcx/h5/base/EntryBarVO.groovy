package groovy.xcx.h5.base

class EntryBarVO {
    /**
     * 左侧标题图标
     */
    String icon

    /**
     * 左侧标题图标宽度
     */
    Integer iconWidth

    /**
     * 左侧标题图标高度
     */
    Integer iconHeight

    /**
     * 左侧标题文案
     */
    String iconTitle

    /**
     * 左侧标题文案背景色
     */
    String tagBgColor

    /**
     * 左侧标题文案颜色
     */
    String tagTextColor

    /**
     * 左侧内容文案
     */
    String title

    /**
     * 左侧文字
     */
    String titleV2

    /**
     * 右侧内容
     */
    String message

    /**
     * 左侧内容文案颜色
     */
    String titleColor

    /**
     * banner背景图
     */
    String img

    /**
     * 右侧箭头前描述文案
     */
    String accessoryTitle

    /**
     * 右侧箭头前描述文案颜色
     */
    String accessoryTitleColor

    /**
     * banner跳转链接
     */
    String linkUrl

    /**
     * 点击之后弹窗
     */
    PageDialogVO dialog

    String acm
}