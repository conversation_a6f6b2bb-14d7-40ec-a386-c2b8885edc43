package groovy.xcx.h5.base

import com.mogujie.detail.core.util.MaitUtil

class ShareLotteryInfoVO {

    String bg

    String btnColor

    String btnText

    String relationKey

    ShareLotteryInfoVO() {
        List<Map<String, Object>> shareLotteryMaitData = MaitUtil.getMaitData(109455)
        Map<String, Object> shareLotteryMaitDataItem = shareLotteryMaitData?.get(0)

        this.bg = shareLotteryMaitDataItem?.get("bg")
        this.btnColor = shareLotteryMaitDataItem?.get("btnColor")
        this.btnText = shareLotteryMaitDataItem?.get("btnText")
        this.relationKey = shareLotteryMaitDataItem?.get("relationKey")
    }
}