package groovy.xcx.h5.base

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.service.item.domain.basic.ItemTagDO
import com.mogujie.contentcenter.utils.JsonUtil
import groovy.xcx.h5.base.constant.XCXType

/**
 * Create by jinger on 2020/02/27 09:37
 */
class AutoCouponVO {

    // 底部按钮是否显示由 nextBtnText 控制
    // 前端功能透出由底部按钮控制

    Boolean isDisable = true   // 是否禁用该功能，默认为true

    String btnText      // 第一行按钮文案
    String nextBtnText  // 第二行价格
    String defaultText  // 第一行默认按钮文案

    String showBtnText // 控制什么按钮下才支持sku面板出优惠信息（目前只有立即购买）

    // 自动领券优惠时透传给后端的参数
    String jsonExtra
    String promotionItemTags
    String sellerId    // 卖家id

    AutoCouponVO (SkuDO skuDO, LiveSimpleDO liveSimpleDO) {

        String nextBtnText = ""
        Boolean isNZ3950Version = ((Util.getXcxType() == XCXType.XCX_NZ)) && (Util.getXcxVersion() > 3940)
        Boolean isLIVE1100Version = ((Util.getXcxType() == XCXType.XCX_LIVE)) && (Util.getXcxVersion() >= 11100)
        Boolean isH5 = Util.getXcxType() == XCXType.H5
        BizType bizType = DetailContextHolder.get().getRouteInfo().bizType
        Boolean isLiveWallItem = liveSimpleDO?.pickedExplainInfo != null

        // 女装3950版本和非渠道 购物台1100以上才透出
        // skuTip，前端会根据这个字段控制
        // 领券按钮，nextBtnText 会根据这个字段控制（为空不显示）
        if ((bizType == BizType.NORMAL) && !isLiveWallItem) {
            this.isDisable = false
        }


        if (skuDO?.promotionPrice != null
                && skuDO.promotionPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal) {
            if (DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal != DetailContextHolder.get()?.getItemDO()?.highNowPrice) {
                nextBtnText = "¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}起"
            } else {
                nextBtnText = "¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}"
            }
        }

        this.btnText = "领券购买"
        this.defaultText = "立即购买"
        this.showBtnText = "立即购买"
        this.nextBtnText = this.isDisable ? "" : nextBtnText

        Long sellerId = DetailContextHolder.get().getItemDO().getUserId()
        this.sellerId = sellerId ? IdConvertor.idToUrl(sellerId) : null


        // 设置领券参数
        this.jsonExtra = DetailContextHolder.get().getItemDO().getJsonExtra()
        // 只需要pp标
        List<ItemTagDO> tags = DetailContextHolder.get().getItemDO().getItemTags()
        if (tags != null) {
            this.promotionItemTags = JsonUtil.toJson(tags.findAll {"pp" == it.getTagKey()})
        }
        this.jsonExtra = this.jsonExtra ?: ""
        this.promotionItemTags = this.promotionItemTags ?: ""

    }
}
