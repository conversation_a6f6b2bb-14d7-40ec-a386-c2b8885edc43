package groovy.xcx.h5.base.utils

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.LimitDiscountInfo
import groovy.xcx.h5.base.PriceLinkVO
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.base.constant.CountdownType

/**
 * Create by jinger on 2019/04/08 10:19
 */
class LimitDiscountHelper {

    static CountdownType activityType(ItemBaseDO itemBase) {
        Long nowTime = System.currentTimeSeconds()
        LimitDiscountInfo limitDiscountInfo = itemBase?.limitDiscountInfo

        Long startTime = limitDiscountInfo?.startTime
        Long endTime = limitDiscountInfo?.endTime

        if (nowTime < startTime) {
            return CountdownType.PRE
        } else if (nowTime > startTime && nowTime < endTime) {
            return CountdownType.IN
        } else {
            return CountdownType.AFTER
        }
    }

    static boolean isShowLimitDiscount(ItemBaseDO itemBase) {
        CountdownType activityType = activityType(itemBase)
        return activityType != CountdownType.AFTER
    }

    // 限量立减
    static PriceLinkVO getActivityBannerInfo(ItemBaseDO itemBase) {
        Long nowTime = System.currentTimeSeconds()
        LimitDiscountInfo limitDiscountInfo = itemBase?.limitDiscountInfo


        // 限量立减时间一般和大促正式期重合
        if (limitDiscountInfo && (nowTime < limitDiscountInfo?.endTime)) {

            List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(CountdownMaitId?.CHANNEL_LIMITCOUNT)
            Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)
            String coverBg = countdownInfoImgData?.get("guideAtmosphereBackground")?: "https://s10.mogucdn.com/mlcdn/c45406/190201_56l6dd4d1if7h55f1g07d520k1c3j_750x72.png"

//            String discount = Math.ceil((itemBase?.activityPrice - limitDiscountInfo?.dicountPirce) / 100D)
            String discount = NumUtil.formatPriceDrawer(limitDiscountInfo?.limitPirce)
            String discountDesc = "此商品<${discount}元>"

            CountdownType activityType = activityType(itemBase)


            // 活动未开始
//            if (activityType == CountdownType?.PRE) {
//                return new PriceLinkVO(
//                        text: "${discountDesc}，即将开抢！",
//                        rightText: "去设置提醒",
//                        image: coverBg,
//                        textColor: '#ffffff',
//                        link: "/pages/detail/pages/rush/index?itemId=${itemBase?.iid}&activityId=${IdConvertor.idToUrl(limitDiscountInfo?.activityId)}",
//                        h5Link: "https://h5.mogu.com/detail/fastbuy.html?itemId=${itemBase?.iid}&activityId=${IdConvertor.idToUrl(limitDiscountInfo?.activityId)}",
//                        margin: 20
//                )
//            }
            // 活动进行中，且有库存
            if (activityType == CountdownType?.IN && limitDiscountInfo?.stock > 0 && limitDiscountInfo?.limitPirce) {
                return new PriceLinkVO(
                        text: "${discountDesc}闪购中，仅剩<${limitDiscountInfo?.stock}件>",
                        rightText: "快去抢",
                        image: coverBg,
                        textColor: '#ffffff',
                        link: "/pages/detail/pages/rush/index?itemId=${itemBase?.iid}&activityId=${IdConvertor.idToUrl(limitDiscountInfo?.activityId)}",
                        h5Link: "https://h5.mogu.com/detail/fastbuy.html?itemId=${itemBase?.iid}&activityId=${IdConvertor.idToUrl(limitDiscountInfo?.activityId)}",
                        margin: 20,
                        countdown: limitDiscountInfo?.endTime - nowTime,
                        countdownText: "距结束<hh:mm:ss>"
                )
            }
            // 活动进行中，没有库存
            if (activityType == CountdownType?.IN && limitDiscountInfo?.stock == 0 && limitDiscountInfo?.limitPirce) {
                return new PriceLinkVO(
                        text: "${discountDesc}闪购库存已售罄",
                        rightText: "",
                        image: coverBg,
                        textColor: '#ffffff',
                        link: "",
                        h5Link: "",
                        margin: 20,
                        countdown: 0
                )
            }
        }
        return new PriceLinkVO()
    }

}