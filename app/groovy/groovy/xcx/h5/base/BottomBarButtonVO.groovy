package groovy.xcx.h5.base

/**
 * Created by ch<PERSON><PERSON><PERSON> on 04/01/2018.
 */
class BottomBarButtonVO {
    /**
     * icon、button
     */
    String type

    /**
     * 按钮名，包括shop/fav/cart/buy/alert/refresh/share/link/treasure(地址栏下单)
     */
    String name

    /**
     * 跳转链接
     */
    String linkUrl

    /**
     * 跳转小程序或跳转链接的信息
     */
    LinkInfoVO linkInfo

    /**
     * 文案前缀
     */
    String prefix

    /**
     * icon类型的名称，或者带prefix的icon名称
     */
    String iconName

    /**
     * 按钮文案
     */
    String text

    /**
     * 第二行文案
     */
    String nextText

    /**
     * 是否可点
     */
    Boolean isDisabled

    /**
     * 悬浮的气泡
     */
    BottomBarButtonPopUpVO popup

    /**
     * 按钮宽度
     */
    Integer width

    /**
     * 设置提醒的活动开始时间
     */
    Long startTime

    /**
     * 是否已设置提醒
     */
    Number isNoticed

}
