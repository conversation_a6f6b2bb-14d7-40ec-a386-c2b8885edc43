package groovy.xcx.h5.base

import com.alibaba.fastjson.JSONObject
import com.mogujie.commons.utils.EnvUtil
import com.mogujie.detail.core.adt.DetailContext
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.MetabaseTool
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail
import groovy.xcx.h5.base.constant.SourceType
import groovy.xcx.h5.base.constant.TagKey
import groovy.xcx.h5.base.constant.XCXType
import org.apache.commons.lang3.StringUtils
import org.apache.commons.collections4.CollectionUtils
import com.mogujie.detail.module.shop.util.WaitressUtil

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.mogujie.detail.core.adt.DetailContext
import org.apache.http.util.TextUtils

import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * Created by changsheng on 13/09/2017.
 */

class Util {

    static void setEsiDataForPrice(ItemPriceVO itemPrice) {
        itemPrice.with {
            if (nowPrice == null) {
                nowPrice = ""
            }
            if (nowPriceNum == null) {
                nowPriceNum = 0
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (highNowPrice == null) {
                highNowPrice = ""
            }
            if (priceTags == null) {
                priceTags = []
            }
            if (prePriceTag == null) {
                prePriceTag = new PriceTagVO()
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (eventPriceDesc == null) {
                eventPriceDesc = new PriceTagVO()
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (eventDesc == null) {
                eventDesc = ""
            }
            if (mobilePrice == null) {
                mobilePrice = ""
            }
            if (mobileDownloadLink == null) {
                mobileDownloadLink = ""
            }
            if (extraDesc == null) {
                extraDesc = ""
            }
            if (extraDescs == null) {
                extraDescs = []
            }


            if (highNowPrice && nowPrice?.endsWith(".00") && highNowPrice?.endsWith(".00")) {
                nowPrice = nowPrice?.split(/\./)[0]
                highNowPrice = highNowPrice?.split(/\./)[0]
            }
        }
    }

    static Boolean isPintuanItem(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        // pintuanDO不为null，并且拼团库存不为0，并且商品不是下架、待开售、预售
        return pinTuan && (!Tools.isJdItem() && pinTuan?.skuInfo?.totalStock > 0 || Tools.isJdItem()) && itemBase?.state != 1 && itemBase?.state != 3 && presale == null && !Boolean.valueOf(DetailContextHolder.get().getParam("noPintuan"))
    }

    static Boolean getSwitchConfig(String configName) {
        return "true".equalsIgnoreCase(MetabaseTool.getValue(configName))
    }

    static Long getMaitIdByEnv(Long maitId) {
        if (EnvUtil.isOfflineEnv() && maitId) {
            // 线下
            String maitIdMapString = MetabaseTool.getValue("maitIdMap")
            if (maitIdMapString) {
                try {
                    JSONObject object = JSONObject.parseObject(maitIdMapString)
                    Object maitIdObject = object.get(maitId.toString())
                    if (maitIdObject) {
                        return Long.valueOf(maitIdObject.toString())
                    }
                } catch (Throwable e) {
                    throw e
                }
            }
        }
        return maitId
    }

    static String formatTime(Long timeStamp, String format) {
        Long secondFlag = 1
        Long minuteFlag = 60
        Long hourFlag = 3600
        Long dayFlag = 86400

        String timeStampTmp = timeStamp

        Long day = (Long) (timeStamp - timeStamp % dayFlag) / dayFlag
        timeStamp = timeStamp % dayFlag

        Long hour = (Long) (timeStamp - timeStamp % hourFlag) / hourFlag
        timeStamp = timeStamp % hourFlag

        Long minute = (Long) (timeStamp - timeStamp % minuteFlag) / minuteFlag
        timeStamp = timeStamp % minuteFlag

        Long second = (Long) (timeStamp - timeStamp % secondFlag) / secondFlag
        timeStamp = timeStamp % secondFlag

        Long ms = timeStamp * 1000 >>> 0

        format = format.toLowerCase()

        Pattern pattern = Pattern.compile(/([a-z])(\1)*/)
        Matcher matcher = pattern.matcher(format)
        List<String> flagList = new ArrayList<>()

        while (matcher.find()) {
            flagList.add(matcher.group())
        }

        if (flagList.indexOf("d") == -1 && flagList.indexOf("dd") == -1) {
            hour += day * 24
        }
        if (flagList.indexOf("h") == -1 && flagList.indexOf("hh") == -1) {
            minute += hour * 60
        }
        if (flagList.indexOf("m") == -1 && flagList.indexOf("mm") == -1) {
            second += minute * 60
        }

        def config = [
                dd: day > 9 ? day : '0' + day,
                hh: hour > 9 ? hour : '0' + hour,
                mm: minute > 9 ? minute : '0' + minute,
                ss: second > 9 ? second : '0' + second,
                d : day,
                h : hour,
                m : minute,
                s : second,
        ]

        flagList.each {
            format = format.replaceAll(it, config.get(it).toString())
        }

        return format
    }

    static String tenThousandFormat(Integer num, String unit) {
        if (num < 10000 ) {
            return num
        }
        double tenThousandNum = (double)num / 10000
        DecimalFormat df = new DecimalFormat("0.0")
        df.setRoundingMode(RoundingMode.DOWN)
        return  df.format(tenThousandNum) + unit
    }

    // 从 mwp 取版本号
    static int getXcxVersion() {
        String av = DetailContextHolder.get().getParam("_av")
        av = StringUtils.isEmpty(av) ? "0" : av
        av = av.isNumber() ? av : "0"
        Integer avInt = av.toInteger()
        return avInt
    }


    static XCXType getXcxType() {
        String appkey = DetailContextHolder.get().getParam("appkey")
        if (!StringUtils.isEmpty(appkey)) {
            switch (appkey) {
                case "100063":
                    return XCXType.XCX_NZ;

                case "100060":
                    return XCXType.XCX_LIVE;

                case "100195":
                    return XCXType.XCX_DOUYIN;

                case "100028":
                    return XCXType.H5;

                default:
                    return XCXType.ALL;
            }
        }
        return XCXType.ALL

    }

    static String addAcmToUrl(String linkUrl, def acm) {
        if (!linkUrl) return ""
        if (!acm) return linkUrl
        if (linkUrl.contains('?')) {
            return "${linkUrl}&acm=${acm}".toString()
        }
        return "${linkUrl}?acm=${acm}".toString()
    }

    // 8位颜色转换为rgba #77FFFFFF #AARRGGBB
    static String formatColor(def color) {

        if (!color) return color

        String hex = String.valueOf(color ?: "")

        if (hex?.length() == 9) {
            // 8位色值转为rgb
            int a = Integer.valueOf(hex.substring(1, 3), 16);
            int r = Integer.valueOf(hex.substring(3, 5), 16);
            int g = Integer.valueOf(hex.substring(5, 7), 16);
            int b = Integer.valueOf(hex.substring(7, 9), 16);

            Double alpha = (float)a / (float)255
            DecimalFormat df = new DecimalFormat("0.##");

            return "rgba(${r},${g},${b},${df.format(alpha)})"
        }

        // 直接返回6位的色值
        return hex
    }

    /**
     * @param cidsStr "683,10004501" 类目id
     * @param itemBase
     * @return
     */
    static boolean isInCids(String cidsStr, ItemBaseDO itemBase) {
        boolean ret = false
        def cids = cidsStr.tokenize(',')//["#705#", "#706#", "#684#", "#710#"]
        cids.each {
            if (itemBase?.cids?.contains("#${it}#")) {
                ret = true
            }
        }
        return ret
    }

    /**
     * 是否包含某个服务体系
     *
     * @param context
     * @return 要检查的服务体系
     */
    static boolean containsService(DetailContext context, ServiceDetailEnum service) {
        if (context == null || service == null) return false
        List<ItemServiceDetail> serviceDetails = WaitressUtil.getItemServices(context)
        if (CollectionUtils.isEmpty(serviceDetails)) {
            return false
        }
        return serviceDetails
                ?.any { itemServiceDetail -> itemServiceDetail.getServiceDetailId() == service.getDetailId() }
    }

    /**
     * 隐藏店铺入口，展示供应商xxx提供（好物优选）
     * @return
     */
    static boolean isHideEntryShop(ShopDO shopDO) {
        boolean ret = false
        List<Integer> tags = [TagKey.HAO_DIAN_YOU_XUAN_TAG]

        tags.each {
            if (shopDO?.tags?.contains(it)) {
                ret = true
            }
        }
        return ret
    }

    /**
     * 2020.3 商品改造后（尺码作为一张图放在商品详情图的后边，隐藏尺码表）
     * @param itemBase
     * @return
     */
    static boolean isNewSizeHelper(ItemBaseDO itemBase) {
        return itemBase?.imageInfo?.size() > 0
    }

    /**
     * 直播商品进图墙，sourceParams {"actorId":"16u922u","type":1}
     * @return
     */
    static SourceType getSourceType(DetailContext context) {
        if (context == null) return null
        try {
            String sourceParams
            if (!TextUtils.isEmpty(sourceParams = context.getParam("sourceParams"))) {
                Gson gson = new Gson()
                JsonObject json = gson.fromJson(sourceParams, JsonObject)
                Integer type = json.get("type").getAsInt()
                switch (type) {
                    case 1:
                        return SourceType.LIVE_WALL;

                    default:
                        return SourceType.NULL;
                }
            }
        } catch (Exception ignore) {
        }
        return SourceType.NULL;
    }

}