package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.collocation.domain.CollocationDO;

/**
 * Created by enmeen on 2020/11/3.
 */
@Translator(id = "collocation", defaultValue = DefaultType.NULL)
class Collocation implements IOneDependTranslator<CollocationDO, Object> {

    @Override
    Object translate(CollocationDO collocationDO) {
        return collocationDO
    }
}