package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO

/**
 * Created by pananping on 2020/11/2.
 */
@Translator(id = "hotSaleRankInfo", defaultValue = DefaultType.NULL)
class HotSaleRankInfo implements IOneDependTranslator<ExtraDO, Object> {

    @Override
    Object translate(ExtraDO extraDO) {
        groovy.mgj.app.common.HotSaleRankInfo appTranslator = new groovy.mgj.app.common.HotSaleRankInfo()
        return appTranslator.translate(extraDO)
    }
}