package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by chang<PERSON><PERSON> on 23/03/2017.
 */

@Translator(id = "normalShareInfo")
class NormalShareInfo implements IOneDependTranslator<ItemBaseDO, NormalShareInfoVO> {

    static class NormalShareInfoVO {
        String title;
        String content;
        String url;
        String imageUrl;
    }

    @Override
    NormalShareInfoVO translate(ItemBaseDO itemBase) {
        if (!itemBase) {
            return null;
        }
        String url = "//h5.mogujie.com/detail-normal/index.html?itemId=" + itemBase?.iid + "&from=share"
        return new NormalShareInfoVO(
                title: itemBase?.title,
                content: "亲爱的～这件宝贝不错哦，快进来看看吧～",
                url: itemBase?.iid ? StrategyUpUtil.upUrl(url) : null,
                imageUrl: itemBase?.topImages && itemBase?.topImages?.size() > 0 ? itemBase?.topImages?.get(0) : ""
        );
    }
}