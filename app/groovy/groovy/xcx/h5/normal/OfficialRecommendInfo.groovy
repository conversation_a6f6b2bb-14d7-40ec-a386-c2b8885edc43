package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.PriceBannerVO

/**
 * Create by changsheng on 2018/11/26 14:53
 * Package groovy.xcx.h5.common
 */
@Translator(id = "officialRecommend")
class OfficialRecommendInfo implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO, OfficialRecommendInfoVO> {

    static class OfficialRecommendInfoVO {
        List<String> tags
        String title
        String desc
    }

    @Override
    OfficialRecommendInfoVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown,LiveSimpleDO liveSimpleDO) {
        OfficialRecommend officialRecommend = itemBase?.officialRecommend

        PriceBannerVO priceBannerInfo = new PriceBanner().translate(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimpleDO)

        Long startTime = officialRecommend?.startTime
        Long endTime = officialRecommend?.endTime
        Long now = System.currentTimeSeconds()

        if (startTime <= now && now <= endTime) {
            // 氛围标 + 推荐标
            List<String> tags = []
            if (priceBannerInfo?.titleTagImage) {
                tags.add(priceBannerInfo?.titleTagImage)
            }
            List<Map<String, Object>> maitList =  MaitUtil.getMaitData(127253L)
            Map<String, Object> maitData = maitList?.get(0)
            String officialRecommendIcon = maitData?.get("officialRecommendIcon")
            if (officialRecommendIcon) {
                tags.add(officialRecommendIcon)
            }
            return new OfficialRecommendInfoVO(
                    title: officialRecommend?.title,
                    desc: officialRecommend?.desc,
                    tags: tags,
            )
        } else {
            return new OfficialRecommendInfoVO()
        }

    }
}