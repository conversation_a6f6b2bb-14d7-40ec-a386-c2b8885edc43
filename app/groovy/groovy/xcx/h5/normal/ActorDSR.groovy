package groovy.xcx.h5.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.live.domain.LiveAnchorInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.Tools
import com.mogujie.detail.core.util.MaitUtil

/**
 * Created by pananping on 2020/11/25.
 */

@Translator(id = "actorDSR", defaultValue = DefaultType.NULL)
class ActorDSR implements IOneDependTranslator<LiveSimpleDO, Object> {

    class ActorDSRTagVO {
        String text
        String jumpURL
    }

    class ActorDSRVO extends HideControl {
        String bgImage
        String avatar
        String title
        String subTitle
        String scoreTag
        String link
        String xcxLink
        String jumpHint
        List<ActorDSRTagVO> rateTags

        String fansNum
        String monthlySales
        String moreLink
    }

    @Override
    Object translate(LiveSimpleDO liveSimpleDO) {
        LiveAnchorInfo info = liveSimpleDO?.actUserInfo

        if (!info) {
            return new ActorDSRVO(_extra_control_hide_: true)
        }

        ActorDSRVO vo = new ActorDSRVO(_extra_control_hide_: false)
        String avatarUrl = ImageUtil.img(info.avatar)
        vo.avatar = avatarUrl
        vo.bgImage = avatarUrl
        vo.title = "${info.name}的买手店"
        vo.jumpHint = "TA卖过的宝贝"
        /**
         * 买手店来源 - buyerShopFrom
         *
         * 0, 商详页底部入口
         * 1, 主播 DSR 入口 √
         *
         * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
         */
        vo.link = Tools.appendRequestAcm("https://act.mogu.com/msd?buyerShopFrom=1&actorId=${IdConvertor.idToUrl(info.userId)}")
        vo.xcxLink = Tools.appendRequestAcm("/pages/lookPersonal/index?uid=${IdConvertor.idToUrl(info.userId)}")

        Long maitID = 151734L
        String textColor
        String scoreTag = ""
        Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
        int dsrLevel = "高".equals(info.dsrLevel) ? 3 : ("中".equals(info.dsrLevel) ? 2 : ("低".equals(info.dsrLevel) ? 1 : 0))
        if (info.dsr) {
            // 上线初期颜色统一为蘑菇红色
            //Modify 2023.07.06 麦田来控制
            switch (dsrLevel){
                case 3:
                    textColor = maitData?.get("dsrHighLevelColor")
                    scoreTag = maitData?.get("dsrHighLevelIcon")
                    break;
                case 2:
                    textColor = maitData?.get("dsrMiddleLevelColor")
                    scoreTag = maitData?.get("dsrMiddleLevelIcon")
                    break;
                case 1:
                    textColor = maitData?.get("dsrLowLevelColor")
                    scoreTag = maitData?.get("dsrLowLevelIcon")
                    break;
                default:
                    textColor = "#FF4466"
                    scoreTag = ""
                    break;
            }
            if (!textColor){
                textColor = "#FF4466"
            }
            vo.subTitle = "<span style=\"color: #999999; font-size: 12px\">主播口碑</span><span style=\"color: " +
                    textColor +
                    "; font-size: 12px\">${info.dsr}</span>"
        } else {
            vo.subTitle = "<span style=\"color: #999999; font-size: 12px\">暂无主播口碑评分</span>"
        }

        if (info.monthlySales) {
            vo.monthlySales = "<span style=\"color: #999999; font-size: 12px\">已售" + formatNumber(info.monthlySales) + "</span>"
        } else {
            vo.monthlySales = ""
        }

        if (info.fansNum >= 10) {
            vo.fansNum = "<span style=\"color: #999999; font-size: 12px\">粉丝" + formatNumber(info.fansNum) + "</span>"
        } else {
            vo.fansNum = ""
        }

        // 高中低标签先不上
        //Modify 2023.07.06 麦田来控制
        vo.scoreTag = scoreTag

        vo.rateTags = []
        if (info.rateTags?.size() > 0) {
            // 最多只返回 8 个标签
            if (info.rateTags.size() > 8) {
                info.rateTags = info.rateTags.subList(0, 8)
            }
            vo.rateTags = info.rateTags.collect {
                new ActorDSRTagVO(
                        text: it.value + " " + formatNumber(it.num),
                        jumpURL: "https://h5.mogu.com/rate/actor.html?actUserId=${IdConvertor.idToUrl(info.userId)}&_labelIds=${it.labelIds}"
                )
            }
            vo.moreLink = "https://h5.mogu.com/rate/actor.html?actUserId=${IdConvertor.idToUrl(info.userId)}"
        }

        return vo
    }

    static def formatNumber(int num) {
        if (num >= 10000) {
            return String.format("%.1fw", num / 10000.0)
        } else {
            return "" + num
        }
    }
}