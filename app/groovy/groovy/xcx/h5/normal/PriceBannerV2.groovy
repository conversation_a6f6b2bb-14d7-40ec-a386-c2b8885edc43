package groovy.xcx.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.translator.ITenDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.TagUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.dailyconfig.domain.NewActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerParamVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.PriceLinkVO
import groovy.xcx.h5.base.constant.ActivityType
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.base.constant.CountdownType
import groovy.xcx.h5.base.utils.LimitDiscountHelper
import groovy.xcx.h5.normal.utils.ActivityManager
import groovy.xcx.h5.normal.utils.NormalCountdownKey
import org.apache.http.util.TextUtils


/**
 * Create by changsheng on 2018/8/23 15:38
 * Package groovy.xcx.h5.normal
 */

/**
 * 展示优先级：限量立减 ———> 跨店满折 ——> 其他氛围
 */
@Translator(id = "priceBannerV2")
class PriceBannerV2 implements ITenDependTranslator<ItemBaseDO, NewActivityDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO, PriceBannerVO> {

    @Override
    PriceBannerVO translate(ItemBaseDO itemBase, NewActivityDO newActivity,  ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimple) {

        /**
         * 价格
         */
        ItemPriceVO itemPrice = new NormalPriceV2().translate(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimple)


        /**
         * 判断当前生效的活动
         * 根据活动以及活动状态取氛围
         */
        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimple)

        /**
         * 限量立减的activityBannerInfo
         */
        PriceLinkVO activityBannerInfo = LimitDiscountHelper.getActivityBannerInfo(itemBase)


        /**
         * 某个商品标的商品
         * 如跨店满折的activityBannerInfo，用于跳转凑单图墙
         */
        PriceLinkVO crossShopDiscount = new PriceLinkVO()
        List<Map<String, Object>> tagMaitDatas = MaitUtil.getMaitData(CountdownMaitId?.CROSS_SHOP_DISCOUNT)
        if (tagMaitDatas) {
            for (Map<String, Object> tagMaitData : tagMaitDatas) {
                int tag
                try {
                    tag = Integer.valueOf((String) tagMaitData?.get("tag"))
                } catch (Throwable ignore) {
                    continue
                }
                if (TagUtil.isContainsTag(DetailContextHolder.get()?.getItemDO()?.getItemTags(), tag) && !activityBannerInfo?.image) {
                    crossShopDiscount.image = tagMaitData.get("activityBanner")
                    crossShopDiscount.link = tagMaitData.get("xcxLink")
                    crossShopDiscount.margin = 0
                    activityBannerInfo = crossShopDiscount
                }
            }
        }

        switch (activityType) {

            case ActivityType.LIVE_WALL_ITEM:
                def maitData = MaitUtil.getMaitData(CountdownMaitId.LIVE_WALL_ITEM)?.get(0)
//                String needPreCountDown = maitData.get("needCountDown") != null ? String.valueOf(maitData.get("needCountDown")) : "1"

                PriceBannerParamVO bannerParam = new PriceBannerParamVO(
                        itemPrice: itemPrice,
                        type: CountdownType.IN,
                        maitId: CountdownMaitId.LIVE_WALL_ITEM,
                        isHideCountdown: true

                )
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(bannerParam)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner
            
            //大促预热期：有newActivityDO时，不展示倒计时和图片，展示newActivity的img
            case ActivityType.DACU_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO(
                        countdownTitleColor: activity?.endTimeHintColor,
                        priceColor: activity?.endTimeHintColor,
                        titleTagImage: activity.activityTitleImage,
                        activityBannerImage: activity?.activitySphereImage,
                        bgImage: activity?.preActivityInImage1110,
                        type: CountdownType.PRE,
                )
                priceBanner.setPriceInfo(itemPrice)
                priceBanner.setActivityPriceStyle(activity?.discountPriceColor, activity?.discountPriceBgColor)
                if (!TextUtils.isEmpty(newActivity?.img)) {
                    //不走下面的逻辑：背景图不展示，倒计时也不展示
                    priceBanner.bgImage = ""
                }
                else {//2023.01.04号:老逻辑不动】
                    priceBanner.setPreCountdownTextForNormal(activity?.startTime, activity?.countdown)
                }
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            //大促活动正式期：有newActivityDO时，不展示倒计时和图片，展示newActivity的img
            case ActivityType.DACU_IN:
                PriceBannerVO priceBanner = new PriceBannerVO(
                        countdownTitleColor: activity?.endTimeHintColor,
                        priceColor: activity?.endTimeHintColor,
                        titleTagImage: activity.activityTitleImage,
                        activityBannerImage: activity?.activitySphereImage,
                        bgImage: activity?.activityInImage1110,
                        type: CountdownType.IN,
                )
                priceBanner.setPriceInfo(itemPrice)
                if (!TextUtils.isEmpty(newActivity?.img)) {
                    //不走下面的逻辑：背景图不展示，倒计时也不展示
                    priceBanner.bgImage = ""
                }
                else {//2023.01.04号:老逻辑不动】
                    priceBanner.setInCountdownText(activity?.endTime, activity?.countdown)
                }
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XSBK_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XSBK)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XSBK_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XSBK)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PPTM_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.PPTM)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PPTM_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.PPTM)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XINPIN_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XINPIN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XINPIN_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XINPIN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.SHANGOU_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)

                def maitData = MaitUtil.getMaitData(countdownInfo?.maitId1110)?.get(0)
                String needPreCountDown = maitData.get("needPreCountDown") != null ? String.valueOf(maitData.get("needPreCountDown")) : "1"

                PriceBannerParamVO bannerParam = new PriceBannerParamVO(
                        itemPrice: itemPrice,
                        type: CountdownType.PRE,
                        startTime: countdownInfo?.startTime,
                        endTime: countdownInfo?.endTime,
                        maitId: countdownInfo?.maitId1110,
                        isHideCountdown: needPreCountDown != "1",

                )
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(bannerParam)
                if (!bannerParam?.isHideCountdown) {
                    priceBanner.setPreCountdownTextForNormal(countdownInfo?.startTime, null)    // 预热期间的倒计时
                }
                priceBanner.setActivityBannerInfo(activityBannerInfo)

                // 根据canShowStrikethroughPrice判断是否能展示价格曲线图
                if (itemBase.canShowStrikethroughPrice) {
                    priceBanner.setPriceHistoryImage(countdownInfo)
                }
                return priceBanner

            case ActivityType.SHANGOU_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)
                def maitData = MaitUtil.getMaitData(countdownInfo?.maitId1110)?.get(0)
                String needCountDown = maitData?.get("needCountDown") != null ? String.valueOf(maitData?.get("needCountDown")) : "1"

                PriceBannerParamVO bannerParam = new PriceBannerParamVO(
                        isHideCountdown: needCountDown != "1",
                        itemPrice: itemPrice,
                        type: CountdownType.IN,
                        startTime: countdownInfo?.startTime,
                        endTime: countdownInfo?.endTime,
                        maitId: countdownInfo?.nbt == '117' ? Long.valueOf('152342') : countdownInfo?.maitId1110
                )
                PriceBannerVO priceBanner = new PriceBannerVO()

                // 判断是否为比价商品
                if (countdownInfo?.nbt == '117' && countdownInfo?.outNetPrice) {
                    priceBanner.initPriceBanner(itemPrice, countdownInfo?.outNetPrice, countdownInfo?.outNetImage, countdownInfo?.maitId1110)
                } else {
                    priceBanner.initPriceBanner(bannerParam)
                }
                priceBanner.setActivityBannerInfo(activityBannerInfo)

                // 根据canShowStrikethroughPrice判断是否能展示价格曲线图
                if (itemBase.canShowStrikethroughPrice) {
                    priceBanner.setPriceHistoryImage(countdownInfo)
                }

                return priceBanner

            case ActivityType.LIVESECKILL_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.LIVESECKILL)
                priceBanner.setPreCountdownTextForNormal(countdownInfo?.startTime, null)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.LIVESECKILL_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.LIVESECKILL)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.TUANGOU_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, groupbuying?.startTime, groupbuying?.endTime, CountdownMaitId.TUANGOU)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.TUANGOU_IN:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, groupbuying?.startTime, groupbuying?.endTime, CountdownMaitId.TUANGOU)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PINTUAN_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, pinTuan?.startTime, pinTuan?.endTime, CountdownMaitId.PINTUAN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PINTUAN_IN:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, pinTuan?.startTime, pinTuan?.endTime, CountdownMaitId.PINTUAN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            default:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner
        }
    }
}
