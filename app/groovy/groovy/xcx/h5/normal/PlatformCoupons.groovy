package groovy.xcx.h5.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.coupon.domain.CouponConfigType
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.coupon.domain.CrossShopDiscount
import com.mogujie.detail.module.coupon.domain.PlatformCoupon
import com.mogujie.detail.module.mycoupon.domain.MyCoupon
import com.mogujie.detail.module.mycoupon.domain.MyCouponDO
import com.mogujie.detail.spi.dslutils.Tools

import java.text.SimpleDateFormat

/**
 * Created by chang<PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-部分商品优惠券
 */

@Translator(id = "platformCoupons")
class PlatformCoupons implements ITwoDependTranslator<CouponDO, MyCouponDO, PlatformCouponsVO> {

    enum CouponType {
        // 折扣券
        DISCOUNT,
        // 满减券
        REACH_REDUCE,
        // 立减券
        REDUCE,
    }

    enum PlatformCouponShowType {
        // 券包
        PACKAGE,
        // 多张券
        MULTI_COUPONS,
        // 购物金（跨店满减）
        BONUS,
    }

    static class PlatformCouponsVO {
        // 平台优惠券（废弃）
        List<PlatformCouponVO> coupons
        // 平台券-外部模块展示
        PlatformCouponV2VO couponInfo
        // 平台券-内部弹窗展示
        PopoverPlatformCouponsVO popoverPlatformCoupons

        // 我的优惠券-App专享券，外部模块展示，点击跳转
        PlatformCouponV2VO activityInfo

        MyCouponVO myCouponInfo
    }

    static class PopoverPlatformCouponsVO {
        String title
        List<PlatformCouponItemVO> list
    }

    static class PlatformCouponItemVO {
        String currency
        PlatformCouponShowType showType
        Long promotionId
        String campId
        String effect
        String validTime
        Boolean isAlreadGet
        String limitDesc
        String effectDesc
        String bgImage
        String bgImageV2 // 新版本购物金领取背景图
        String tagImg
        String couponInfo
        String terminal
        // 跨店满减新增
        String title
        String buttonText
        // 我的购物金
        String bonusTitle
        String bonusCurrency
        String activityCode

        Boolean isUseOut
        LinkInfoVO linkInfo // 跳转凑单图墙参数
        String canGetStr // 立即领取
        String useUpStr // 去逛逛
        String mainTxColor  // 立即领取颜色

    }

    static class PlatformCouponV2VO {
        String title
        List<String> couponList
        String image
        String relationKey
        String link
    }

    static class PlatformCouponVO {
        String iconTitle
        String title
        String useTimeDesc
        String img
        String accessoryTitle
        String linkUrl
        String tagBgColor
        String tagTextColor

        PlatformCouponVO(PlatformCoupon platformCoupon) {
            iconTitle = platformCoupon?.iconTitle
            title = platformCoupon?.title
            useTimeDesc = platformCoupon?.useTimeDesc
            img = platformCoupon?.img
            accessoryTitle = platformCoupon?.accessoryTitle
            linkUrl = platformCoupon?.linkUrl
        }
    }

    static class MyCouponVO {
        Integer bonusCountNum
    }


    /**
     * 购物金跳转凑单图墙参数，预热跳预热，正式跳正式
     */
    static class LinkInfoVO {
        String title
        String promotionId // 即 activityCode
        String cKey // 'xcx-coupon-mass'
        String promotionCode // 'platformBonus'
        String promotionStatus
        String url
    }

    @Override
    PlatformCouponsVO translate(CouponDO coupon, MyCouponDO myCoupon) {

        def promotionTagsMap = [:]
        def promotionTags = MaitUtil.getMaitData(18353)
        promotionTags?.each {
            def activityName = it.get("activityName")
            if (activityName) {
                promotionTagsMap[activityName] = [
                        tagBgColor  : it.get("tagBgColor"),
                        tagTextColor: it.get("tagTextColor")
                ]
            }
        }

        List<PlatformCouponVO> coupons = coupon?.platformCoupon?.collect {
            PlatformCouponVO platformCoupon = new PlatformCouponVO(it)
            platformCoupon.with {
                tagBgColor = promotionTagsMap[iconTitle] ? promotionTagsMap[iconTitle]?.tagBgColor : "#FF5577"
                tagTextColor = promotionTagsMap[iconTitle] ? promotionTagsMap[iconTitle]?.tagTextColor : "#FFFFFF"
            }
            return platformCoupon
        }

        // 平台券，详情页促销模块展示的文案
        List<String> couponList = []

        Boolean canShowCrossShopDiscount = Tools.isBonusItem() && coupon?.crossShopDiscount
        CrossShopDiscount crossShopDiscount = coupon?.crossShopDiscount
        if (canShowCrossShopDiscount && crossShopDiscount?.limitPrice > 0 && crossShopDiscount?.cutPrice > 0) {
            // 跨店满减
            String couponText = "每满${NumUtil.formatPriceDrawer(crossShopDiscount?.limitPrice)}减${NumUtil.formatPriceDrawer(crossShopDiscount?.cutPrice)} | 限APP".toString()
            couponList.add(0, couponText)
        } else if (coupon?.platformCouponV2?.size() > 0) {
            // 普通的平台券，风车配置
            couponList = coupon?.platformCouponV2?.collect {
                if (it?.limitPrice > 0 && it?.cutPrice > 0) {
                    return "满${NumUtil.formatPriceDrawer(it?.limitPrice)}减${NumUtil.formatPriceDrawer(it?.cutPrice)}".toString()
                } else if (it?.discount > 0) {
                    return "${NumUtil.formatPriceDrawer(it?.discount)}折券".toString()
                } else {
                    return ""
                }
            }
        }

        if (couponList && couponList?.size() > 2) {
            couponList = couponList.subList(0, 2)
        }

        PlatformCouponV2VO couponInfo = null

        if (couponList && couponList.size() > 0) {
            couponInfo = new PlatformCouponV2VO(
                    // 如果有跨店满减券，则显示“跨店满减”，否则显示之前风车上的券包标题
                    title: canShowCrossShopDiscount? "跨店满减" : coupon?.couponPkgTitle,
                    image: coupon?.couponPkgImg,
                    relationKey: coupon?.relationKey,
                    couponList: couponList,
            )
        } else {
            couponInfo = new PlatformCouponV2VO(
                    title: "",
                    couponList: []
            )
        }

        // 平台券领券弹窗
        List<PlatformCouponItemVO> platformCouponList = []
        // 购物金跳转凑单图墙参数
        LinkInfoVO linkInfo = null

        // 跨店满减（购物金）
        if (canShowCrossShopDiscount && crossShopDiscount?.limitPrice > 0 && crossShopDiscount?.cutPrice > 0) {
            String limitDesc = "·每满${NumUtil.formatPriceDrawer(crossShopDiscount?.limitPrice)}减${NumUtil.formatPriceDrawer(crossShopDiscount?.cutPrice)}"
            // 上限大于10000，不展示
            if (crossShopDiscount?.upperLimit > 0 && crossShopDiscount?.upperLimit < 1000000) {
                limitDesc = "${limitDesc}，上限${NumUtil.formatPriceDrawer(crossShopDiscount?.upperLimit)}"
            }

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("M.d")
            String startTimeStr = simpleDateFormat.format(new Date(crossShopDiscount?.startTime * 1000L))
            String endTimeStr = simpleDateFormat.format(new Date(crossShopDiscount?.endTime * 1000L))
            String endTimeStrTemp = simpleDateFormat.format(new Date(crossShopDiscount?.endTime * 1000L - 1))
            String timeStr
            if (startTimeStr == endTimeStrTemp) {
                timeStr = "仅限${startTimeStr}当日"
            } else {
                timeStr = "${startTimeStr}-${endTimeStr}"
            }
            String validTime = "·${timeStr}，${crossShopDiscount?.terminal}${crossShopDiscount?.terminal? "，" : ""}可跨店使用".toString()

            List<Map<String, Object>> maitDataList = MaitUtil.getTargetedMaitData(134291)
            Map<String, Object> maitData = maitDataList?.get(0)


            // 传给图墙的参数
            if (crossShopDiscount?.startTime > 0 && crossShopDiscount?.endTime > 0) {
                SimpleDateFormat wallSimpleDateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm")
                String wallStartTimeStr = wallSimpleDateFormat.format(new Date(crossShopDiscount?.startTime * 1000L))
                String wallEndTimeStr = wallSimpleDateFormat.format(new Date(crossShopDiscount?.endTime * 1000L))
                String wallValidTimeStr = "/n使用时间：${wallStartTimeStr}-${wallEndTimeStr}".toString()
                String wallLimitDesc = limitDesc.replace('·', "")
                Long nowTime = System.currentTimeSeconds()

                if (nowTime < crossShopDiscount?.endTime) {
                    linkInfo = new LinkInfoVO(
                            url: "/pages/wallPackage/index",
                            title: "${wallLimitDesc}${wallValidTimeStr}",
                            promotionId: IdConvertor.idToUrl(crossShopDiscount?.promotionId),
                            promotionStatus: nowTime > crossShopDiscount?.startTime ? 'in' : 'pre',
                            cKey: 'xcx-coupon-mass',
                            promotionCode: crossShopDiscount?.promotionCode ?: 'platformBonus'
                    )
                }
            }


            platformCouponList.add(new PlatformCouponItemVO(
                    title: crossShopDiscount?.name,
                    limitDesc: limitDesc,
                    validTime: validTime,
                    buttonText: coupon?.crossShopDiscountBtnText,
                    bgImage: coupon?.crossShopDiscountBgImg,
                    bgImageV2: maitData?.get("crossShopDiscountBgImg1200"),
                    showType: PlatformCouponShowType.BONUS,
                    bonusTitle: "我的购物金：",
                    bonusCurrency: "元",
                    activityCode: maitData?.get("activityCode"),
                    isUseOut: false, // 标识次数是否用尽
                    linkInfo: linkInfo,
                    useUpStr: maitData?.get("useUpStr"),
                    canGetStr: maitData?.get("canGetStr"),
                    mainTxColor: maitData?.get('tagTextColor') ?: '#FF4466'

            ))
        }

        if (coupon?.couponConfigType == CouponConfigType.PACKAGE) {
            // 券包
            platformCouponList.add(new PlatformCouponItemVO(
                    bgImage: coupon?.couponPkgImg,
                    campId: coupon?.relationKey,
                    showType: PlatformCouponShowType.PACKAGE,
            ))
        } else if (coupon?.couponConfigType == CouponConfigType.MULTI_COUPONS) {
            // 多张券
            coupon?.platformCouponV2?.each {
                String limitDesc = ""
                String currency = ""
                String validTime = ""
                String effect = ""
                if (it?.limitPrice > 0 && it?.cutPrice > 0) {
                    currency = "¥"
                    effect = NumUtil.formatPriceDrawer(it?.cutPrice).toString()
                    if (it?.limitPrice > 0) {
                        limitDesc = "·满${NumUtil.formatPriceDrawer(it?.limitPrice).toString()}元立减".toString()
                    } else {
                        limitDesc = "·满${effect}元立减".toString()
                    }


                } else if (it?.discount > 0) {
                    effect = "${NumUtil.formatPriceDrawer(it?.discount)}折".toString()
                    limitDesc = "·折扣券，${effect}最高抵扣${NumUtil.formatNum(it?.maxDecrease / 100D)}元".toString()
                }

                if (it?.startTime > 0 && it?.endTime > 0) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm")
                    String startTimeStr = simpleDateFormat.format(new Date(it?.startTime * 1000L))
                    String endTimeStr = simpleDateFormat.format(new Date(it?.endTime * 1000L))
                    validTime = "·${startTimeStr}-${endTimeStr}".toString()
                }

                PlatformCouponItemVO vo = new PlatformCouponItemVO(
                        showType: PlatformCouponShowType.MULTI_COUPONS,
                        bgImage: coupon?.couponBgImg,
                        tagImg: coupon?.couponTagImg,
                        campId: IdConvertor.idToUrl(it?.couponId),
                        promotionId: it?.couponId,
                        effect: effect,
                        validTime: validTime,
                        isAlreadGet: it?.hasReceived,
                        limitDesc: limitDesc,
                        terminal: it?.terminal,
                        currency: currency,
                )

                // 2020-09-28 @yanze
                // App 端为了不发版支持新人券业务，借用平台券列表来展示新人券，H5 简便起见也用这个方案
                // 因 UI 细节与平台券不同，根据 PlatformCouponV2 中给到的是否为新人券标记，调整一下返回 VO 的内容
                // TODO: 强行复用平台券很不合理，将来一定要剥离出去
                if (it.forNewUser) {
                    vo.bgImage = "https://s10.mogucdn.com/mlcdn/c45406/200928_1g7ig8f9jfelh34kh1f8a87fgdiak_1035x225.png"
                    vo.terminal = limitDesc // 把「满xxx元立减」放在券面额右边
                    vo.limitDesc = ""
                    vo.validTime = "App和小程序均可使用"
                }
                // =============================================

                platformCouponList.add(vo)
            }
        }

        PopoverPlatformCouponsVO platformCoupons = null
        if (platformCouponList && platformCouponList.size() > 0) {
            platformCoupons = new PopoverPlatformCouponsVO(
                    list: platformCouponList,
                    // 优惠券列表弹窗的标题，一律显示风车上的券包标题
                    title: coupon?.couponPkgTitle,
            )
        } else {
            platformCoupons = new PopoverPlatformCouponsVO(
                    list: [],
                    title: "",
            )
        }

        // 活动
        PlatformCouponV2VO activityInfo = null
        List<MyCoupon> myAppCoupons = myCoupon?.myAppCoupons

        if (myAppCoupons?.size() > 0) {
            List<String> activityCouponList = myAppCoupons?.collect {
                if (it?.limitPrice > 0 && it?.cutPrice > 0) {
                    return "满${NumUtil.formatPriceDrawer(it?.limitPrice)}减${NumUtil.formatPriceDrawer(it?.cutPrice)}".toString()
                } else if (it?.discount > 0) {
                    return "${NumUtil.formatPriceDrawer(it?.discount)}折券".toString()
                } else {
                    return ""
                }
            }

            if (activityCouponList?.size() > 2) {
                activityCouponList = activityCouponList?.subList(0, 2)
            }

            List<Map<String, Object>> maitDataList = MaitUtil.getMaitData(133975)
            Map<String, Object> maitData = maitDataList?.get(0)

            activityInfo = new PlatformCouponV2VO(
                    title: maitData?.get("title"),
                    couponList: activityCouponList,
                    link: maitData?.get("link"),
            )
        }


        MyCouponVO myCouponInfo = null
        if (myCoupon?.bonusCount != null) {
            myCouponInfo = new MyCouponVO(
                    bonusCountNum: myCoupon?.bonusCount != null ? myCoupon?.bonusCount: 0,
            )
        }

        return new PlatformCouponsVO(
                coupons: coupons,
                couponInfo: couponInfo,
                popoverPlatformCoupons: platformCoupons,
                activityInfo: activityInfo,
                myCouponInfo: myCouponInfo,

        )
    }
}