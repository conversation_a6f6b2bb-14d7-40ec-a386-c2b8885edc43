package groovy.xcx.h5.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.LimitDiscountInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceBannerParamVO
import groovy.xcx.h5.base.PriceBannerVO
import groovy.xcx.h5.base.PriceLinkVO
import groovy.xcx.h5.base.constant.ActivityType
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.base.constant.CountdownType
import groovy.xcx.h5.base.utils.LimitDiscountHelper
import groovy.xcx.h5.normal.utils.ActivityManager
import groovy.xcx.h5.normal.utils.NormalCountdownKey

/**
 * Create by changsheng on 2018/8/23 15:38
 * Package groovy.xcx.h5.normal
 */


@Translator(id = "priceBanner")
class PriceBanner implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO,PriceBannerVO> {

    @Override
    PriceBannerVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimpleDO) {

        /**
         * 价格
         */
        ItemPriceVO itemPrice = new NormalPriceV2().translate(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimpleDO)


        /**
         * 判断当前生效的活动
         * 根据活动以及活动状态取氛围
         */
        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimpleDO)

        /**
         * 限量立减
         */
        PriceLinkVO activityBannerInfo = LimitDiscountHelper.getActivityBannerInfo(itemBase)

        switch (activityType) {
            case ActivityType.DACU_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO(
                        countdownTitleColor: activity?.endTimeHintColor,
                        priceColor: activity?.endTimeHintColor,
                        titleTagImage: activity.activityTitleImage,
                        activityBannerImage: activity?.activitySphereImage,
                        bgImage: activity?.preActivityInImage1110,
                        type: CountdownType.PRE,
                )
                priceBanner.setPriceInfo(itemPrice)
                priceBanner.setPreCountdownTextForNormal(activity?.startTime, activity?.countdown)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.DACU_IN:
                PriceBannerVO priceBanner = new PriceBannerVO(
                        countdownTitleColor: activity?.endTimeHintColor,
                        priceColor: activity?.endTimeHintColor,
                        titleTagImage: activity.activityTitleImage,
                        activityBannerImage: activity?.activitySphereImage,
                        bgImage: activity?.activityInImage1110,
                        type: CountdownType.IN,
                )
                priceBanner.setPriceInfo(itemPrice)
                priceBanner.setInCountdownText(activity?.endTime, activity?.countdown)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XSBK_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XSBK)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XSBK_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XSBK)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PPTM_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.PPTM)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PPTM_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.PPTM)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XINPIN_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XINPIN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.XINPIN_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.XINPIN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.SHANGOU_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)

                def maitData = MaitUtil.getMaitData(countdownInfo?.maitId1110)?.get(0)
                String needPreCountDown = maitData.get("needPreCountDown") != null ? String.valueOf(maitData.get("needPreCountDown")) : "1"

                PriceBannerParamVO bannerParam = new PriceBannerParamVO(
                        itemPrice: itemPrice,
                        type: CountdownType.PRE,
                        startTime: countdownInfo?.startTime,
                        endTime: countdownInfo?.endTime,
                        maitId: countdownInfo?.maitId1110,
                        isHideCountdown: needPreCountDown != "1",
                )
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(bannerParam)
                if (!bannerParam?.isHideCountdown) {
                    priceBanner.setPreCountdownTextForNormal(countdownInfo?.startTime, null)    // 预热期间的倒计时
                }
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.SHANGOU_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)

                def maitData = MaitUtil.getMaitData(countdownInfo?.maitId1110)?.get(0)
                String needCountDown = maitData?.get("needCountDown") != null ? String.valueOf(maitData?.get("needCountDown")) : "1"

                PriceBannerParamVO bannerParam = new PriceBannerParamVO(
                        isHideCountdown: needCountDown != "1",
                        itemPrice: itemPrice,
                        type: CountdownType.IN,
                        startTime: countdownInfo?.startTime,
                        endTime: countdownInfo?.endTime,
                        maitId: countdownInfo?.maitId1110
                )
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(bannerParam)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner
            
            case ActivityType.LIVESECKILL_PRE:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.LIVESECKILL)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.LIVESECKILL_IN:
                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, CountdownMaitId.LIVESECKILL)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.TUANGOU_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, groupbuying?.startTime, groupbuying?.endTime, CountdownMaitId.TUANGOU)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.TUANGOU_IN:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, groupbuying?.startTime, groupbuying?.endTime, CountdownMaitId.TUANGOU)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PINTUAN_PRE:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.PRE, itemPrice, pinTuan?.startTime, pinTuan?.endTime, CountdownMaitId.PINTUAN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            case ActivityType.PINTUAN_IN:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, pinTuan?.startTime, pinTuan?.endTime, CountdownMaitId.PINTUAN)
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner

            default:
                PriceBannerVO priceBanner = new PriceBannerVO()
                priceBanner.setActivityBannerInfo(activityBannerInfo)
                return priceBanner
        }
    }
}



//package groovy.xcx.h5.normal
//
//import com.mogujie.detail.core.annotation.Translator
//import com.mogujie.detail.core.translator.IEightDependTranslator
//import com.mogujie.detail.module.activity.domain.ActivityDO
//import com.mogujie.detail.module.extra.domain.ExtraDO
//import com.mogujie.detail.module.groupbuying.constants.TuanStatus
//import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
//import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
//import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
//import com.mogujie.detail.module.normalCountdown.domain.CountdownState
//import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
//import com.mogujie.detail.module.pintuan.domain.PinTuanDO
//import com.mogujie.detail.module.presale.domain.PresaleDO
//import com.mogujie.detail.module.sku.domain.SkuDO
//import groovy.xcx.h5.base.ItemPriceVO
//import groovy.xcx.h5.base.PriceBannerVO
//import groovy.xcx.h5.base.Util
//import groovy.xcx.h5.base.constant.CountdownMaitId
//import groovy.xcx.h5.base.constant.CountdownType
//
///**
// * Create by changsheng on 2018/8/23 15:38
// * Package groovy.xcx.h5.normal
// */
//
//
//@Translator(id = "priceBanner")
//class PriceBanner implements IEightDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, PriceBannerVO> {
//
//    @Override
//    PriceBannerVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown) {
//
//        ItemPriceVO itemPrice = new NormalPrice().translate(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown)
//        Map<String, CountdownInfo> countdownInfoMap = normalCountdown?.countdownInfoMap
//        CountdownInfo countdownInfo = countdownInfoMap?.find {
//            it?.value != null && it?.value?.state == CountdownState.IN_ACTIVITY
//        }?.value
//        CountdownState state = countdownInfo?.state
//        Long maitId1110 = countdownInfo?.maitId1110
//
//        /**
//         * 有任何预热价的情况，都不展示
//         */
//        if (itemPrice?.eventPrice) {
//            return new PriceBannerVO()
//        } else if (activity?.countdown && activity?.activityState == 2) {
//            /**
//             * 大促正式
//             */
//            PriceBannerVO priceBanner = new PriceBannerVO(
//                    bgImage: activity?.activityInImage1110,
//                    countdown: activity?.countdown,
//                    countdownTitle: "距结束仅剩",
//                    countdownTitleColor: activity?.endTimeHintColor,
//                    priceColor: activity?.endTimeHintColor,
//                    titleTagImage: activity.activityTitleImage,
//                    activityBannerImage: activity?.activitySphereImage,
//            )
//            priceBanner.setPriceInfo(itemPrice)
//            return priceBanner
//        } else if (countdownInfo != null && state == CountdownState.IN_ACTIVITY) {
//            /**
//             * 限时爆款、品牌特卖
//             */
//            PriceBannerVO priceBanner = new PriceBannerVO()
//            priceBanner.initPriceBanner(CountdownType.IN, itemPrice, countdownInfo?.startTime, countdownInfo?.endTime, maitId1110)
//            return priceBanner
//        } else if (groupbuying && groupbuying?.status == TuanStatus.IN && groupbuying?.endTime) {
//            /**
//             * 团购正式
//             */
//            PriceBannerVO priceBanner = new PriceBannerVO()
//            priceBanner.initPriceBanner(CountdownType.IN, itemPrice, groupbuying?.startTime, groupbuying?.endTime, CountdownMaitId.TUANGOU)
//            return priceBanner
//        } else if (Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
//            /**
//             * 非渠道拼团-招商拼团
//             */
//            Boolean isExpire = pinTuan?.isExpire()
//            Long now = System.currentTimeSeconds()
//            Long startTime = pinTuan?.startTime
//            if (pinTuan && now > startTime && !isExpire) {
//                PriceBannerVO priceBanner = new PriceBannerVO()
//                priceBanner.initPriceBanner(CountdownType.IN, itemPrice, pinTuan?.startTime, pinTuan?.endTime, CountdownMaitId.PINTUAN)
//                return priceBanner
//            } else {
//                return new PriceBannerVO()
//            }
//        } else {
//            /**
//             * 其他情况不展示新版氛围
//             */
//            return new PriceBannerVO()
//        }
//    }
//}