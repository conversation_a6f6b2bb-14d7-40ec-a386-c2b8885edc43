package groovy.xcx.h5.normal

/**
 * Created by chang<PERSON><PERSON> on 21/12/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendDO

@Translator(id = "shopRecommend")
class ShopRecommend implements IOneDependTranslator<ShopRecommendDO, ShopRecommendDO> {

    @Override
    ShopRecommendDO translate(ShopRecommendDO shopRecommend) {
        if (shopRecommend?.recommendItemList == null && shopRecommend?.realTimeRecommendItemList == null) {
            return null
        } else {
            return shopRecommend
        }
    }
}