package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveAnchorInfo
import com.mogujie.detail.module.live.domain.LiveDO
import groovy.xcx.h5.base.LinkInfoVO

/**
 * Created by jinger on 2018/12/6.
 */
@Translator(id = "liveLabelInfo", defaultValue = DefaultType.NULL)
class LiveLabelInfo implements IOneDependTranslator<LiveDO, LiveLabelVO> {

    static class LiveLabelVO {
        String avatar
        String title
        String desc
    }

    @Override
    LiveLabelVO translate(LiveDO live) {
        return new LiveLabelVO()
    }
}