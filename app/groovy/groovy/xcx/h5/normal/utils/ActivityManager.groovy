package groovy.xcx.h5.normal.utils

import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ItemState
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.ActivityType

/**
 * Create by changsheng on 2018/9/13 18:36
 * Package groovy.xcx.h5.normal.utils
 */
class ActivityManager {

    ActivityType activityType

    ActivityManager(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimple) {
        this.activityType = getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimple)
    }


    static ActivityType getActivityType(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimple) {

        List<StartTimeData> startTimeList = new ArrayList<>()
        Long nowTime = System.currentTimeSeconds()

        /**
         * 先判断正式期活动，如果有的话，就直接返回
         */

        if (liveSimple?.pickedExplainInfo != null) {
            /**
             * 直播商品进图墙
             */
            return ActivityType.LIVE_WALL_ITEM
        } else if (presale != null) {
            /**
             * 预售
             */
            return ActivityType.PRESALE
        } else if (itemBase?.state == ItemState.ITEM_WAIT_FOR_SALE && extra?.onSaleTime > nowTime) {
            /**
             * 定时开售
             */
            return ActivityType.SALEONTIME
        } else if (activity != null && activity.activityState == 2) {
            /**
             * 大促正式
             */
            return ActivityType.DACU_IN
        } else if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK) && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)?.state == CountdownState.IN_ACTIVITY) {
            /**
             * 限时爆款
             */
            return ActivityType.XSBK_IN
        } else if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM) && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)?.state == CountdownState.IN_ACTIVITY) {
            /**
             * 品牌特卖
             */
            return ActivityType.PPTM_IN
        } else if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN) && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)?.state == CountdownState.IN_ACTIVITY) {
            /**
             * 新品
             */
            return ActivityType.XINPIN_IN
        } else if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU) && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)?.state == CountdownState.IN_ACTIVITY) {
            /**
             * 非渠道闪购
             */
            return ActivityType.SHANGOU_IN
        } else if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL) && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)?.state == CountdownState.IN_ACTIVITY) {
            /**
             * 非渠道直播秒杀
             */
            return ActivityType.LIVESECKILL_IN
        } else if (groupbuying != null && groupbuying.status == TuanStatus.IN) {
            /**
             * 团购
             */
            return ActivityType.TUANGOU_IN
        } else if (Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system && nowTime > pinTuan?.startTime && !pinTuan?.isExpire()) {
            /**
             * 非渠道招商拼团
             */
            return ActivityType.PINTUAN_IN
        } else {
            /**
             * 开始处理预热活动
             *
             * 1. 取商品报名的所有活动，
             * 2. 如果有处于正式期间的，则不展示预热价，展示正式氛围
             * 3. 如果没有正式期活动，则取出所有活动的开始时间，距离现在最近的一个活动，展示该活动的预热价，和预热氛围
             */

            /**
             * 大促预热且能取到预热价
             */
            if (activity != null && activity.activityState == 1 && activity?.warmUpPrice?.price) {
                startTimeList.push(new StartTimeData(
                        startTime: activity?.startTime,
                        activityType: ActivityType.DACU_PRE,
                ))
            }
            /**
             * 限时爆款
             */
            if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK) != null && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)?.state == CountdownState.WARM_UP) {
                startTimeList.push(new StartTimeData(
                        startTime: normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XSBK)?.startTime,
                        activityType: ActivityType.XSBK_PRE,
                ))
            }
            /**
             * 品牌特卖
             */
            if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM) != null && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)?.state == CountdownState.WARM_UP) {
                startTimeList.push(new StartTimeData(
                        startTime: normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)?.startTime,
                        activityType: ActivityType.PPTM_PRE,
                ))
            }
            /**
             * 新品
             */
            if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN) != null && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)?.state == CountdownState.WARM_UP) {
                startTimeList.push(new StartTimeData(
                        startTime: normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.XINPIN)?.startTime,
                        activityType: ActivityType.XINPIN_PRE,
                ))
            }
            /**
             * 闪购
             */
            if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU) != null && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)?.state == CountdownState.WARM_UP) {
                startTimeList.push(new StartTimeData(
                        startTime: normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)?.startTime,
                        activityType: ActivityType.SHANGOU_PRE,
                ))
            }
            /**
             * 直播秒杀
             */
            if (normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL) != null && normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)?.state == CountdownState.WARM_UP) {
                startTimeList.push(new StartTimeData(
                        startTime: normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.LIVESECKILL)?.startTime,
                        activityType: ActivityType.LIVESECKILL_PRE,
                ))
            }
            /**
             * 团购
             */
            if (groupbuying != null && groupbuying.status == TuanStatus.PRE) {
                startTimeList.push(new StartTimeData(
                        startTime: groupbuying?.startTime,
                        activityType: ActivityType.TUANGOU_PRE,
                ))
            }
            /**
             * 招商拼团
             */
            if (pinTuan != null && pinTuan?.system && nowTime < pinTuan?.startTime) {
                startTimeList.push(new StartTimeData(
                        startTime: pinTuan?.startTime,
                        activityType: ActivityType.PINTUAN_PRE,
                ))
            }

            return startTimeList.min { it?.startTime - nowTime }?.activityType ?: ActivityType.NORMAL
        }

    }

    static class StartTimeData {
        Long startTime
        ActivityType activityType
    }
}