package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveType
import groovy.xcx.h5.base.TabpanelHideInfoVO

/**
 * Created by jinger on 2018/12/6.
 */
@Translator(id = "tabpanelHideInfo", defaultValue = DefaultType.NULL)
class TabpanelHideInfo implements IOneDependTranslator<LiveDO, TabpanelHideInfoVO> {

    @Override
    TabpanelHideInfoVO translate(LiveDO live) {
        return new TabpanelHideInfoVO(
                moduleTabpanelRecommend: live?.liveType == LiveType.LIVE_SUPPLY_CHAIN
        )
    }
}