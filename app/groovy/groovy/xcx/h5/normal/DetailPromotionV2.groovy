package groovy.xcx.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.detail.core.adt.PromotionDecorate
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.coupon.domain.CouponConfigType
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.mycoupon.domain.MyCouponDO
import com.mogujie.detail.module.coupon.domain.PlatformCouponV2
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.common.Switcher

import java.text.SimpleDateFormat

/**
 * Created by pananping on 2020/11/2.
 */
@Translator(id = "promotionV2", defaultValue = DefaultType.NULL)
class DetailPromotionV2 implements ISixDependTranslator<ItemBaseDO, SkuDO, CouponDO, PinTuanDO, MyCouponDO, LiveDO, Object> {

    class DetailPromotionVO {
        String sellerId
        String iid
        Map<String, Object> request
        Boolean showShopPromotion = true

        List<PopoverItemVO> popovers            // 优惠弹窗数据
        List<OuterTagVO> outers                 // 详情页上预览的优惠标
        List<PopoverItemVO> popoversRealtime    // 优惠弹窗数据（不走缓存）
        List<OuterTagVO> outersRealtime         // 详情页上预览的优惠标（不走缓存）

        PriceCalculationDO calculateDiscount    // 优惠计算相关（不走缓存）

        Map<String, Object> promotionStyle = [
                "arrowImg": "https://s10.mogucdn.com/mlcdn/c45406/210107_21h4k1gj40d3j5b68a9k7dk62i5ik_120x50.png",
                "couponArrowImg": "https://s10.mogucdn.com/mlcdn/c45406/210107_13dkdf1076bbfijhaddgh2i07917g_120x50.png"
        ]

        class PopoverItemVO {
            String type
            Integer sort
            Map<String, Object> data

            PopoverItemVO(String type, String promotionCode, Integer sort, Map data) {
                this.type = type
                this.sort = sort

                this.data = new HashMap<>()
                this.data.put("promotionCode", promotionCode)
                this.data.putAll(data)
            }
        }

        class OuterTagVO {
            Integer sort
            String text
            String textColor = "#FF4466"
            String bgImg = "https://s10.mogucdn.com/mlcdn/c45406/201030_4jeji9aa62b132b650927c625abdc_39x57.png"
        }

        class PriceCalculationDO {
            String discountDesc
            String originalPrice
            String originalPriceDesc
            List<PromotionDecorate> discountList

            PriceCalculationDO() {
                this.discountDesc = ""
                this.originalPrice = ""
                this.originalPriceDesc = ""
                this.discountList = []
            }

            PriceCalculationDO(ItemBaseDO itemBaseDO, SkuDO skuDO) {
                this()

                DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()
                this.discountList = itemDO.promotionPriceDetail

                this.discountDesc = "预估到手价 ¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}"
                if (DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal != DetailContextHolder.get()?.getItemDO()?.highNowPriceVal) {
                    this.discountDesc += "起"
                }

                ItemPriceVO itemPriceVO = new ItemPriceVO()
                itemPriceVO.highNowPrice = itemBaseDO?.highNowPrice
                itemPriceVO.lowNowPrice = itemBaseDO?.lowNowPrice
                itemPriceVO.updatePrices(true)
                this.originalPrice = itemPriceVO.price

                this.originalPriceDesc = "活动价"
            }
        }

        DetailPromotionVO() {
            this.popoversRealtime = []
            this.outersRealtime = []
            this.calculateDiscount = new PriceCalculationDO()
        }

        DetailPromotionVO(ItemBaseDO itemBaseDO, SkuDO skuDO, CouponDO couponDO, PinTuanDO pintuanDO, MyCouponDO myCouponDO) {
            this()

            if (itemBaseDO) {
                this.sellerId = itemBaseDO.userId
                this.iid = itemBaseDO.iid

                this.request = new HashMap<>()
                this.request.put("sellerId", itemBaseDO.userId)
                this.request.put("itemIds", itemBaseDO.iid)
                this.request.put("preSale", itemBaseDO.saleType == 1)
                if (Tools.isPintuan(itemBaseDO, pintuanDO) && pintuanDO?.skuInfo?.lowNowPrice) {
                    this.request.put("itemPrice", (pintuanDO.skuInfo.lowNowPrice?.toBigDecimal() * 100).toInteger())
                } else if (itemBaseDO.lowNowPrice) {
                    this.request.put("itemPrice", (itemBaseDO.lowNowPrice.toBigDecimal() * 100).toInteger())
                }

                // 优惠浮窗计价公式，展示条件完全同底部的领券购买按钮
                if (Switcher.showBuyPromotion()
                        && skuDO?.promotionPrice != null
                        && skuDO.promotionPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal) {

                    this.calculateDiscount = new PriceCalculationDO(itemBaseDO, skuDO)
                }
            }

            if (couponDO) {
                if (couponDO.crossShopDiscount && Tools.isBonusItem()) {
                    this.processBonus(itemBaseDO, couponDO, myCouponDO)  // 购物金（老跨店满减）
                }

                if (couponDO.couponConfigType == CouponConfigType.PACKAGE) {
                    this.processCouponPackage(couponDO)   // 平台券券包
                } else if (couponDO.platformCouponV2) {
                    this.processPlatCoupons(couponDO)     // 平台券列表
                }
            }

            if (myCouponDO) {
                this.processModouCoupon(myCouponDO)  // 蘑豆兑券
            }

            if (skuDO) {
                this.processFreeInstallment(skuDO)  // 分期免息
            }
        }

        void processBonus(ItemBaseDO itemBaseDO, CouponDO couponDO, MyCouponDO myCouponDO) {

            this.popovers = this.popovers ?: []
            this.outers = this.outers ?: []

            String limitPrice = NumUtil.formatPriceDrawer((int) couponDO.crossShopDiscount.limitPrice)
            String cutPrice = NumUtil.formatPriceDrawer((int) couponDO.crossShopDiscount.cutPrice)
            String upperLimit = ""
            if (couponDO.crossShopDiscount.upperLimit && couponDO.crossShopDiscount.upperLimit < 1000000) {
                upperLimit = "，上限" +  NumUtil.formatPriceDrawer((int) couponDO.crossShopDiscount.upperLimit)
            }
            String desc1 = "·每满" + limitPrice + "减" + cutPrice + upperLimit

            String startDateStr = new SimpleDateFormat("M.d").format(new Date(couponDO.crossShopDiscount.startTime * 1000L))
            String endDateStr = new SimpleDateFormat("M.d").format(new Date(couponDO.crossShopDiscount.endTime * 1000L))
            String time = (startDateStr == endDateStr ? "·仅限" + startDateStr + "当日" : "·" + startDateStr + "-" + endDateStr)
            String terminal = couponDO.crossShopDiscount.terminal ? "，" + couponDO.crossShopDiscount.terminal : ""
            String desc2 = time + terminal + "，可跨店使用"

            String picWallTitle = "每满" + limitPrice + "减" + cutPrice + "，可跨店使用"
            String startTime = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(new Date(couponDO.crossShopDiscount.startTime * 1000L))
            String endTime = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(new Date(couponDO.crossShopDiscount.endTime * 1000L))
            String validTime = "使用时间：" + startTime + "-" + endTime
            String state = System.currentTimeSeconds() >= couponDO.crossShopDiscount.startTime ? "0" : "1" // 0正式 1预热
            String useUpLink = "mgj://coudanwaterfall?promotionCode=${couponDO?.crossShopDiscount?.promotionCode}&promotionId=${couponDO?.crossShopDiscount?.promotionId}&tip1=${picWallTitle}&wallTopDecorateTitle=${picWallTitle}&wallTopDecorateSubTitle=${validTime}&primeStatus=${state}"
            useUpLink += "&coudanStatus=${(state == "0") ? 1 : 0}" // 0预热 1正式，展示计价条
            useUpLink += "&_nchoice=1&fId=${itemBaseDO?.iid}"

            Map<String, Object> maitData = MaitUtil.getMaitData(134291)?.get(0)

            this.popovers?.add(new PopoverItemVO("allowance", "allowance", 1000, [
                    "desc1": desc1,
                    "desc2": desc2,
                    "count": myCouponDO.bonusCount && myCouponDO.bonusCount > 0 ? myCouponDO.bonusCount / 100.0 : 0,
                    "countDesc": "我的购物金：",
                    "bgImage": maitData?.get("crossShopDiscountBgImg1200") ?: "",
                    "getStr": couponDO.crossShopDiscountBtnText ?: "",
                    "activityCode": maitData?.get("activityCode") ?: "",
                    "canGetStr": maitData?.get("canGetStr") ?: "立即领取",
                    "useUpStr": maitData?.get("useUpStr") ?: "去逛逛",
                    "useUpLink": useUpLink
            ]))
            this.outers?.add(new OuterTagVO(text: "购物金每满" + limitPrice + "减" + cutPrice, sort: 1000))
        }

        void processCouponPackage(CouponDO couponDO) {

            this.popovers = this.popovers ?: []
            this.outers = this.outers ?: []

            this.popovers?.add(new PopoverItemVO("platPkg", "platPkg", 1001, [
                    "couponPkgTitle": couponDO.couponPkgTitle ?: "",
                    "couponPkgImg": couponDO.couponPkgImg ?: "",
                    "relationKey": couponDO.relationKey ?: ""
            ]))
            this.outers?.add(new OuterTagVO(text: "券包", sort: 1001))
        }

        void processPlatCoupons(CouponDO couponDO) {

            this.popovers = this.popovers ?: []
            this.outers = this.outers ?: []

            String newUserCouponOuterTitle
            String platformCouponOuterTitle
            for (PlatformCouponV2 coupon in couponDO.platformCouponV2) {

                if (coupon.forNewUser) {  // 是披着平台券外衣的新人券

                    if (!newUserCouponOuterTitle) { // 后端已经排序好了。只取第一个作为外部展示，准没错
                        newUserCouponOuterTitle = coupon.name + "满" + NumUtil.formatPriceDrawer((int) coupon.limitPrice) + "减" + NumUtil.formatPriceDrawer((int) coupon.cutPrice)
                    }

                    this.popoversRealtime?.add(new PopoverItemVO("plat", "newUser", 1002, [
                            "bgImage": "https://s10.mogucdn.com/mlcdn/c45406/200928_1g7ig8f9jfelh34kh1f8a87fgdiak_1035x225.png",
                            "effect": "¥" + NumUtil.formatPriceDrawer((int) coupon.cutPrice),
                            "couponDesc": "满" + NumUtil.formatPriceDrawer((int) coupon.limitPrice) + "元立减",
                            "expiryInfo": "App和小程序均可使用",
                            "isAlreadyGet": true
                    ]))

                } else {

                    if (!platformCouponOuterTitle) { // 后端已经排序好了。只取第一个作为外部展示，准没错
                        platformCouponOuterTitle = coupon.name + "满" + NumUtil.formatPriceDrawer((int) coupon.limitPrice) + "减" + NumUtil.formatPriceDrawer((int) coupon.cutPrice)
                    }

                    String effect, limitDesc, expiryInfo
                    if (coupon.discount != null && coupon.discount > 0) {
                        effect = NumUtil.formatPriceDrawer((int) coupon.discount) + "折"
                        limitDesc = "·折扣券，" + effect + "最高抵扣" + NumUtil.formatPriceDrawer((int) coupon.maxDecrease) + "元"
                    } else {
                        effect = "¥" + NumUtil.formatPriceDrawer((int) coupon.cutPrice)
                        limitDesc = "·满" + NumUtil.formatPriceDrawer((int) coupon.limitPrice) + "元立减"
                    }
                    if (coupon.startTime == null || coupon.endTime == null) {
                        expiryInfo = ""
                    } else {
                        String startDateStr = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(new Date(((long) coupon.startTime) * 1000))
                        String endDateStr = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(new Date(((long) coupon.endTime) * 1000))
                        expiryInfo = "·" + startDateStr + "-" + endDateStr
                    }

                    this.popovers?.add(new PopoverItemVO("plat", "platform", 1003, [
                            "effect": effect,
                            "limitDesc": limitDesc,
                            "expiryInfo": expiryInfo,
                            "couponDesc": coupon.terminal ?: "",
                            "campId": coupon.couponId ? IdConvertor.idToUrl(coupon.couponId) : "",
                            "bgImage": couponDO.couponBgImg ?: "",
                            "tagImage": couponDO.couponTagImg ?: "",
                            "isAlreadyGet": coupon.hasReceived
                    ]))
                }
            }

            if (newUserCouponOuterTitle) {
                this.outersRealtime?.add(new OuterTagVO(text: newUserCouponOuterTitle, textColor: "#FFFFFF", sort: 1002, bgImg: "https://s10.mogucdn.com/mlcdn/c45406/201030_2fhjfgkcc222ej2k367lb2e12bl8h_39x57.png"))
            }
            if (platformCouponOuterTitle) {
                this.outers?.add(new OuterTagVO(text: platformCouponOuterTitle, sort: 1003))
            }
        }

        void processModouCoupon(MyCouponDO myCouponDO) {

            List<PopoverItemVO> modouCouponList = myCouponDO.modouCoupons?.collect {

                int cutCent = it.cutCent?.intValue() ?: 0
                int limitCent = it.limitCent?.intValue() ?: 0
                limitCent = Math.max(limitCent, cutCent + 1)  // 无门槛券展示比优惠金额多0.01

                int status = 2
                if (it.getStatus() != null) {
                    switch (it.getStatus().code) {
                        case 1:
                            status = 0; break
                        case 2000:
                        case 2001:
                            status = 1; break
                        case 2002:
                            status = 2; break
                        default:
                            status = 2
                    }
                }

                String expiryInfo
                if (it.et != null && it.et > 0) {
                    int secondsOfDay = 24 * 60 * 60
                    expiryInfo = "领取后${it.et / secondsOfDay}天内有效"
                } else {
                    Date startDate = new Date(it.getStartTime() * 1000L)
                    Date endDate = new Date(it.getEndTime() * 1000L)
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm")
                    expiryInfo = "${formatter.format(startDate)}-${formatter.format(endDate)}"
                }

                return new PopoverItemVO("modou", "modou", 1004, [
                        "modouAmount": myCouponDO.balanceModouAmount ?: 0,
                        "effect": "${NumUtil.formatPriceDrawer(cutCent)}",
                        "expiryInfo": expiryInfo,
                        "limitDesc": "满${NumUtil.formatPriceDrawer(limitCent)}元可用",
                        "campId": it.getPkgId() ?: "",
                        "requireModou": it.costNum ?: 0,
                        "vipLevel": myCouponDO.userVipLevel ?: 0,
                        "status": status,  // 0 可兑换 1 已兑换 2 已抢完
                        "preview": "满${NumUtil.formatPriceDrawer(limitCent)}减${NumUtil.formatPriceDrawer(cutCent)}",
                        "cutCent": cutCent,
                        "limitCent": limitCent
                ])
            }

            if (modouCouponList?.size() > 0) {
                this.popoversRealtime?.addAll(modouCouponList)
                this.outersRealtime?.add(new OuterTagVO(text: "蘑豆兑券", sort: 1004))
            }
        }

        void processFreeInstallment(SkuDO skuDO) {
            if (skuDO.freePhases) {
                this.popoversRealtime?.add(new PopoverItemVO("discount", "freeInstallment", 9000, [
                        "couponInfo": "最高享" + skuDO.freePhases.toString() + "期免手续费",
                        "promotionIconText": "免手续费",
                ]))
                this.outersRealtime?.add(new OuterTagVO(text: "免手续费", sort: 9000))
            }
        }
    }

    @Override
    Object translate(ItemBaseDO itemBaseDO, SkuDO skuDO, CouponDO couponDO, PinTuanDO pintuanDO, MyCouponDO myCouponDO, LiveDO liveDO) {
        DetailPromotionVO vo = new DetailPromotionVO(itemBaseDO, skuDO, couponDO, pintuanDO, myCouponDO)

        if (groovy.mgj.app.vo.Tools.isLiveSource()) {
            vo.showShopPromotion = false
        }

        return vo
    }
}