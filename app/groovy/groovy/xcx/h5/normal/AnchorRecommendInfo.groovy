package groovy.xcx.h5.normal


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.live.domain.ExplainInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO

/**
 * Create by jinger on 2020/04/02 15:03
 */
@Translator(id = "anchorRecommendInfo", defaultValue = DefaultType.NULL)
class AnchorRecommendInfo implements ITwoDependTranslator<ExtraDO, LiveSimpleDO, AnchorRecommendVO> {

    static class AnchorRecommendVO {
        String avatar
        String name
        List<TagVO> tags
        String desc
    }

    static class TagVO {
        String text
    }

    @Override
    AnchorRecommendVO translate(ExtraDO extra, LiveSimpleDO liveSimple) {

        // Boolean isLiveInWallItem = ContextUtil?.isLiveInWallItem(DetailContextHolder.get())

        if (liveSimple?.pickedExplainInfo) {

            ExplainInfo pickedExplainInfo = liveSimple?.pickedExplainInfo

            List<TagVO> tags = pickedExplainInfo?.tags?.collect {
                TagVO tag = new TagVO(
                        text: it?.name
                )
                return tag
            }

            return new AnchorRecommendVO(
                    avatar: pickedExplainInfo?.avatar,
                    name: pickedExplainInfo?.actUserName,
                    tags: tags,
                    desc: getSaleText(extra?.sales)
            )

        }

        return new AnchorRecommendVO()
    }


    static String getSaleText(Long sales) {
        if (sales == null) return  null
        if (sales > 1000L) {
            return String.format("销量%.1f万", sales / 10000.0)
        }
        return String.format("销量 %d", sales)
    }
}