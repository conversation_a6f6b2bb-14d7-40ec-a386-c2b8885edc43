package groovy.xcx.h5.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.live.domain.LiveType
import com.mogujie.detail.module.pintuan.domain.PinTuanDO

/**
 * Created by changsheng on 03/01/2018.
 */
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.xcx.h5.base.*

@Translator(id = "bottomBar")
class BottomBar implements IEightDependTranslator<ItemBaseDO, ExtraDO, PresaleDO, LiveDO, PinTuanDO, SkuDO, ShopDO, LiveSimpleDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase, ExtraDO extra, PresaleDO presale, LiveDO live, PinTuanDO pinTuan, SkuDO skuDO, ShopDO shopDO, LiveSimpleDO liveSimpleDO) {
//        PintuanInfoVO pintuanInfo = new PintuanInfoVO(pinTuan, itemBase)
        Integer itemState = itemBase?.state
        Long onSaleTime = extra?.onSaleTime
        Long nowTime = System.currentTimeSeconds()
        Boolean isSaleStartTimeShow = itemState == ItemState.ITEM_WAIT_FOR_SALE && onSaleTime > nowTime
//        Boolean isPintuan = Util.isPintuanItem(itemBase, pinTuan, presale)
        Boolean isPintuan = false

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)
        itemPrice.setNowPriceByRange(itemBase)
        itemPrice.setOldPriceByRange(itemBase)
//        SkuInfoVO pintuanSkuInfo = pintuanInfo?.skuInfo
        String normalPrice = itemPrice.nowPrice
//        String pintuanPrice = pintuanSkuInfo?.lowNowPrice ?: itemPrice.nowPrice
        String currency = itemBase?.currency ?: "¥"
        // 加购抽奖
        Boolean addCartTips = itemBase?.addCartTips

        // 自动领券的信息
        AutoCouponVO autoCouponInfo = new AutoCouponVO(skuDO, liveSimpleDO)

        List<BottomBarButtonVO> buttons = new ArrayList<>()

        // 店铺
        // 有主播信息，替换为新版买手店入口（1480）
        if (liveSimpleDO?.actUserInfo) {
            /**
             * 买手店来源 - buyerShopFrom
             *
             * 0, 商详页底部入口
             * 1, 主播 DSR 入口 √
             *
             * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
             */
            buttons.add(new BottomBarButtonVO(
                    type: "icon",
                    name: "link",
                    iconName: "shop",
                    text: "买手店",
                    linkInfo: new LinkInfoVO(
                            h5Url: liveSimpleDO?.actUserInfo?.userId ? 'https://act.mogu.com/msd?buyerShopFrom=0&actorId=' + IdConvertor.idToUrl(liveSimpleDO?.actUserInfo?.userId)  : '',
                            url: liveSimpleDO?.actUserInfo?.userId ? "/pages/lookPersonal/index?uid=" + IdConvertor.idToUrl(liveSimpleDO?.actUserInfo?.userId)  : ''
                    )
            ))
        } else {
            buttons.add(new BottomBarButtonVO(
                    type: "icon",
                    name: "shop",
                    text: "店铺"
            ))
        }

        // ，非渠道拼团不展示
        if (!isPintuan) {
            buttons.add(new BottomBarButtonVO(
                    type: "im",
                    name: "im",
                    text: "客服",
            ))
        }

        // 收藏
        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "fav",
                text: "收藏"
        ))

        // 直播
        if (live?.liveType == LiveType.OLD_LIVE_PROMOTION) {
            buttons.add(new BottomBarButtonVO(
                    type: "live",
                    name: "live",
                    text: "直播中"
            ))
        }

        // 直播供应链
        if (live?.liveType == LiveType.LIVE_SUPPLY_CHAIN) {
            buttons.add(new BottomBarButtonVO(
                    type: "icon",
                    name: "link",
                    iconName: "person",
                    text: "主播"
            ))
        }

        // 加购按钮，非虚拟商品，非预售，展示加购
        if (itemBase?.virtualItemType == VirtualItemType.NORMAL && presale == null && !Tools.isMedicalBeautyItem() && !Tools.isVirtualCouponItem()) {
            BottomBarButtonPopUpVO popUp = null
            if (addCartTips) {
                /**
                 * 加购抽奖的Tips气泡
                 */
                List<Map<String, Object>> addCartMaitData = MaitUtil.getTargetedMaitData(89246)
                Map<String, Object> addCartData = addCartMaitData?.get(0)
                popUp = addCartData ? new BottomBarButtonPopUpVO(
                        text: addCartData?.get("addCartText"),
                        textColor: addCartData?.get("textColor"),
                        image: addCartData?.get("addCartBg"),
                        tipTimes: addCartData?.get("tipTimes") ? Integer.valueOf(addCartData?.get("tipTimes")?.toString()) : 0,
                        duration: 6000
                ) : null
            }
            buttons.add(new BottomBarButtonVO(
                    type: isPintuan ? "icon" : "button",
                    name: "cart",
                    text: isPintuan ? "加购" : "加入购物车",
                    // 已下架和库存不足
                    isDisabled: itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_OUT_OF_STOCK,
                    popup: popUp
            ))
        }

        // 待开售，需要设置提醒，不要加购
        if (isSaleStartTimeShow) {
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "alert",
                    text: "设置提醒",
                    startTime: extra?.onSaleTime
            ))
//        } else if (isPintuan) {
//            buttons.add(new BottomBarButtonVO(
//                    type: "button",
//                    name: "singleBuy",
//                    text: "${currency}${normalPrice}",
//                    nextText: "单独购买"
//            ))
//
//            buttons.add(new BottomBarButtonVO(
//                    type: "button",
//                    name: "buy",
//                    text: "${currency}${pintuanPrice}",
//                    nextText: "${pintuanInfo?.tuanNum}人拼团"
//            ))
        } else if (presale != null) {
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "buy",
                    text: "立即付定金",
                    nextText: presale?.expandMoney? "定金 ${presale?.deposit} 抵 ${presale?.expandMoney}" : "",
                    isDisabled: itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_OUT_OF_STOCK
            ))

        // 自动领券
        } else if (autoCouponInfo?.nextBtnText && itemState == ItemState.ITEM_ON_SALE) {
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "couponBuy",
                    text: autoCouponInfo?.btnText,
                    nextText: autoCouponInfo?.nextBtnText
            ))

        } else {
            buttons.add(new BottomBarButtonVO(
                    type: "button",
                    name: "buy",
                    text: getBuyText(itemState),
                    // 已下架和库存不足
                    isDisabled: itemState == ItemState.ITEM_OUT_OF_DATE || itemState == ItemState.ITEM_OUT_OF_STOCK
            ))
        }


        BottomBarVO bottomBar = new BottomBarVO(
                buttons: buttons
        )

        return bottomBar
    }

    static private String getBuyText(Integer itemState) {

        switch (itemState) {
            case 0: return "立即购买"
            case 1: return "卖光啦"
            case 2: return "卖光啦"
            default: return "立即购买"
        }
    }

}