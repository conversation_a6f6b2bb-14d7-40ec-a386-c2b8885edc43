package groovy.xcx.h5.normal


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceExtraDescVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.ActivityType
import groovy.xcx.h5.base.constant.CountdownMaitId
import groovy.xcx.h5.normal.utils.ActivityManager
import groovy.xcx.h5.normal.utils.NormalCountdownKey

import java.text.SimpleDateFormat

/**
 * Create by changsheng on 2018/9/13 12:33
 * Package groovy.xcx.h5.normal
 */

@Translator(id = "normalPriceV2")
class NormalPriceV2 implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimpleDO) {

        if (!itemBase) {
            return null
        }
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase, sku, extra)

        /**
         * 判断当前生效的活动
         * 根据活动取价格，如果是预热期间，要展示预热价格
         */
        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimpleDO)

        switch (activityType) {
            case ActivityType.LIVE_WALL_ITEM:

                List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(CountdownMaitId.LIVE_WALL_ITEM)
                Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    priceTags = [
                            new PriceTagVO(
                                    text: countdownInfoImgData?.get("priceDesc")
                            )
                    ]
                }
                break

            case ActivityType.PRESALE:
                itemPrice.with {
                    nowPrice = presale?.totalPrice?.replace("¥", "")
                    setOldPriceByRange(itemBase)
                    priceTags = [
                            new PriceTagVO(
                                    text: "预售价"
                            )
                    ]
                    eventPrice = presale?.deposit?.replace("¥", "")
                    eventPriceDesc = new PriceTagVO(
                            text: "定金"
                    )
                    if (presale?.expandMoney) {
                        eventTags = [
                                new PriceTagVO(
                                        text: "抵${presale?.expandMoney}".toString()
                                )
                        ]
                    }
                }
                break
            case ActivityType.DACU_IN:
            case ActivityType.DACU_PRE:
                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)
                    priceTags = activity?.priceDesc ? [
                            new PriceTagVO(
                                    text: activity?.priceDesc
                            )
                    ] : []
                    eventPrice = activity?.warmUpPrice?.price?.replace("¥", "")
                    eventPriceColor = activity?.warmUpPrice?.color
                    if (eventPrice) {
                        eventPriceDesc = new PriceTagVO(
                                text: activity?.warmUpPrice?.priceDesc,
                                bgColor: "#FFFFFF" //无背景色
                        )
                    }
                }
                break
            case ActivityType.XSBK_IN:
            case ActivityType.XSBK_PRE:
                itemPrice = getNormalCountdownActivityPrice(itemPrice,  NormalCountdownKey.XSBK, CountdownMaitId.XSBK, itemBase, normalCountdown)
                break
            case ActivityType.PPTM_IN:
            case ActivityType.PPTM_PRE:
                itemPrice = getNormalCountdownActivityPrice(itemPrice,  NormalCountdownKey.PPTM, CountdownMaitId.PPTM, itemBase, normalCountdown)
                break
            case ActivityType.XINPIN_IN:
            case ActivityType.XINPIN_PRE:
                itemPrice = getNormalCountdownActivityPrice(itemPrice,  NormalCountdownKey.XINPIN, CountdownMaitId.XINPIN, itemBase, normalCountdown)
                break
            case ActivityType.SHANGOU_IN:
            case ActivityType.SHANGOU_PRE:

                CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.SHANGOU)
                Long maitId = countdownInfo?.maitId1110
                itemPrice = getNormalCountdownActivityPrice(itemPrice,  NormalCountdownKey.SHANGOU, maitId, itemBase, normalCountdown)

                break
            case ActivityType.LIVESECKILL_IN:
            case ActivityType.LIVESECKILL_PRE:
                itemPrice = getNormalCountdownActivityPrice(itemPrice,  NormalCountdownKey.LIVESECKILL, CountdownMaitId.LIVESECKILL, itemBase, normalCountdown)
                break
            case ActivityType.TUANGOU_IN:
            case ActivityType.TUANGOU_PRE:
                itemPrice.with {
                    List<Map<String, Object>> maitData = MaitUtil.getMaitData(CountdownMaitId.TUANGOU)
                    Map<String, Object> countdownInfoImgData = maitData?.get(0)
                    String groupbuyingText = countdownInfoImgData?.get("priceTag")

                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    if (groupbuying.status == TuanStatus.IN) {
                        priceTags = [
                                new PriceTagVO(
                                        text: groupbuyingText
                                )
                        ]
                    } else if (groupbuying.status == TuanStatus.PRE) {
                        // priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                        eventPrice = groupbuying?.price?.replace("¥", "")
                        eventPriceDesc = new PriceTagVO(
                                text: groupbuyingText
                        )

                        // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                        if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                            eventPriceDesc.text = ""
                        }
                    }
                }
                break
            case ActivityType.PINTUAN_IN:
            case ActivityType.PINTUAN_PRE:
            case ActivityType.SALEONTIME:
            default:
                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)
                    priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                }
                break
        }

        // 有拼团就要展示拼团价
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            itemPrice.with {
                nowPrice = pinTuan?.skuInfo?.lowNowPrice
                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                oldPrice = getOldPriceForce(itemBase)
            }
        }

        itemPrice.with {
            // 统一的活动Tag
            if (eventTags == null) {
                eventTags = []
            }
            eventTags?.addAll(activity?.eventTags?.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }?: [])

            extraDesc = extra?.crossStoreDiscount

            // 前半小时
            extraDescs = new ArrayList<PriceExtraDescVO>()
            if (extra?.crossStoreDiscount) {
                extraDescs.add(new PriceExtraDescVO(
                        desc: extra?.crossStoreDiscount,
                        bgColor: "#FFF3E6",
                        textColor: "#FA6813"
                ))
            }

            // 待开售
            if (itemBase?.state == 3 && extra?.onSaleTime > 0) {
                Date date = new Date(extra?.onSaleTime * 1000)
                SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日HH:mm")
                String dateString = formatter.format(date)
                String desc = "${dateString}开售，请提前设置提醒"
                eventDesc = eventDesc ? "${eventDesc} ${desc}".toString() : desc.toString()
            }
        }

        if (itemPrice.nowPrice != null) {
            itemPrice.nowPriceNum = (itemPrice.nowPrice?.toBigDecimal() * 100).toInteger()
        }

        Util.setEsiDataForPrice(itemPrice)

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        itemPrice.isHideOldPrice(itemBase)

        return itemPrice
    }

    /**
     * 取拼团的priceTag
     * @param itemBase
     * @param pinTuan
     * @param presale
     * @return
     */
    static List<PriceTagVO> getDefaultPriceTags(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            return [
                    new PriceTagVO(
                            text: "拼团价"
                    )
            ]
        } else if (itemBase?.discountDesc) {
            return [
                    new PriceTagVO(
                            text: itemBase?.discountDesc
                    )
            ]
        } else {
            return []
        }
    }


    private static ItemPriceVO getNormalCountdownActivityPrice(ItemPriceVO itemPrice, String key, Long maitId, ItemBaseDO itemBase, NormalCountdownDO normalCountdown) {
        CountdownInfo countdownInfo = normalCountdown?.countdownInfoMap?.get(key)
        List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(maitId)
        Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

        itemPrice.with {
            setNowPriceByRange(itemBase)
            setOldPriceByRange(itemBase)

            // 预热期要取预热价
            if (countdownInfo?.state == CountdownState.WARM_UP) {
                eventPrice = NumUtil.formatNum(countdownInfo?.price / 100D)
                eventPriceDesc = new PriceTagVO(
                        text: countdownInfoImgData?.get("prePriceDesc")
                )
            } else if (countdownInfo?.state == CountdownState.IN_ACTIVITY) {
                priceTags = [
                        new PriceTagVO(
                                text: countdownInfoImgData?.get("priceDesc")
                        )
                ]
            }
        }

        return itemPrice
    }
}