package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.TopCountdownVO
import groovy.xcx.h5.base.Util

/**
 * Created by changsheng on 22/03/2017.
 * H5私有模块-普通详情页-活动氛围&倒计时
 */

@Translator(id = "normalCountdown")
class NormalCountdown implements IEightDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown) {
        List<Map<String, Object>> maitData = MaitUtil.getMaitData(43182)
        Map<String, Object> imgData = maitData?.get(0)
        Map<String, CountdownInfo> countdownInfoMap = normalCountdown?.countdownInfoMap
        CountdownInfo countdownInfo = countdownInfoMap?.find {
            it?.value != null && it?.value?.state == CountdownState.IN_ACTIVITY
        }?.value

        // 优先级：大促 > 团购
        if (activity?.countdown) {
            if (activity?.activityState == 1) {
                // 纯文案，取warmUpTitle
                return new TopCountdownVO(
                        text: activity?.warmUpTitle,
                        countdown: activity?.countdown,
                        image: activity?.activityPreImage,
                        isCountdomShow: false
                )
            } else {
                return new TopCountdownVO(
                        text: "距结束仅剩",
                        countdown: activity?.countdown,
                        image: activity?.activityInImage,
                        titleColor: activity?.activityInTitleColor
                )
            }
        } else if (countdownInfo) {
            // 限时爆款
            CountdownState state = countdownInfo?.state
            Long maitId = countdownInfo?.maitId
            List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(maitId)
            Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

            if (state == CountdownState.WARM_UP) {
                return new TopCountdownVO(
                        text: "距开始仅剩",
                        countdown: countdownInfo?.countdown,
                        image: countdownInfoImgData?.get("backgroundImage"),
                        titleColor: countdownInfoImgData?.get("fontColor")
                )
            } else if (state == CountdownState.IN_ACTIVITY) {
                return new TopCountdownVO(
                        text: "距结束仅剩",
                        countdown: countdownInfo?.countdown,
                        image: countdownInfoImgData?.get("backgroundImage"),
                        titleColor: countdownInfoImgData?.get("fontColor")
                )
            } else {
                return new TopCountdownVO()
            }
        } else if (groupbuying && groupbuying?.status == TuanStatus.IN && groupbuying?.endTime) {
            //读取团购倒计时背景麦田资源
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(groupbuying, imgData)

            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: groupbuying.endTime - System.currentTimeSeconds(),
                    image: ImageUtil.img(info?.image),
                    titleColor: info?.titleColor
            )
        } else if (groupbuying && groupbuying?.status == TuanStatus.PRE && groupbuying?.startTime) {
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(groupbuying, imgData);

            // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
            if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                return getPintuanCountdown(pinTuan)
            } else {
                return new TopCountdownVO(
                        text: "距开始仅剩",
                        countdown: groupbuying.startTime - System.currentTimeSeconds(),
                        image: ImageUtil.img(info?.image),
                        titleColor: info?.titleColor
                )
            }
        } else if (Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
            /**
             * 非渠道拼团-招商拼团
             */
            return getPintuanCountdown(pinTuan)
        } else {
            return new TopCountdownVO()
        }
    }

    /**
     * 判断团购类型，分别取不同资源位字段
     */
    static class GroupBuyImageAndTitleColorInfo {
        String image
        String titleColor

        public GroupBuyImageAndTitleColorInfo(GroupbuyingDO groupbuyingDO, Map<String, Object> imgData) {
            if (!imgData) {
                return
            }
            GroupBuyType groupBuyType = GroupBuyType.getGroupBuyType(groupbuyingDO)
            if (groupBuyType == GroupBuyType.UZHI) {
                image = imgData?.get("tuanUZhiImage")
                titleColor = imgData?.get("tuanUZhiTitleColor")
            } else if (groupBuyType == GroupBuyType.STORE) {

                image = imgData?.get("tuanInStoreImage")
                titleColor = imgData?.get("tuanInStoreTitleColor")
            } else if (groupBuyType == GroupBuyType.PINPAI) {
                image = imgData?.get("tuanPinpaiImage")
                titleColor = imgData?.get("tuanPinpaiTitleColor")
            } else if (groupBuyType == GroupBuyType.NORMAL) {
                image = imgData?.get("tuanNormalImage")
                titleColor = imgData?.get("tuanNormalTitleColor")
            }
        }
    }

    /**
     * 取拼团倒计时氛围，判断正式/预热
     * @param pinTuan
     * @return
     */
    static TopCountdownVO getPintuanCountdown(PinTuanDO pinTuan) {
        List<Map<String, Object>> pintuanMaitData = MaitUtil.getMaitData(56776)
        Map<String, Object> pintuanImgData = pintuanMaitData?.get(0)

        String pintusnBackgroundImg = pintuanImgData?.get("normalPintuanImage")
        String pintuanTitleColor = pintuanImgData?.get("normalPintuanTitleColor")

        Boolean isExpire = pinTuan?.isExpire
        Long now = System.currentTimeSeconds()
        Long startTime = pinTuan?.startTime
        Long remainTime = pinTuan?.remainTime

        if (!pinTuan) {
            return new TopCountdownVO()
        } else if (now < startTime) {
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - now,
                    image: pintusnBackgroundImg,
                    titleColor: pintuanTitleColor
            )
        } else if (now > startTime && !isExpire) {
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: remainTime,
                    image: pintusnBackgroundImg,
                    titleColor: pintuanTitleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }

    /**
     * 团购类型
     * NORMAL 普通团购
     * UZHI U质团
     * STORE 团购入仓
     * PINPAI 品牌团
     */
    static enum GroupBuyType {
        NORMAL,
        UZHI,
        STORE,
        PINPAI

        static public GroupBuyType getGroupBuyType(GroupbuyingDO groupbuyingDO) {
            if (groupbuyingDO.bizType == TuanBizType.UZHI) {
                return UZHI
            } else if (groupbuyingDO.type == TuanType.STORE) {
                return STORE
            } else if (groupbuyingDO.bizType == TuanBizType.PINPAI) {
                return PINPAI
            } else if (groupbuyingDO.bizType == TuanBizType.NORMAL) {
                return NORMAL
            }
        }
    }

}