package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.presale.domain.RuleDesc

/**
 * Created by ch<PERSON><PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-预售信息
 */

@Translator(id = "preSaleInfo", defaultValue = DefaultType.EMPTY_STRING)
class PreSaleInfo implements IOneDependTranslator<PresaleDO, Object> {

    static class PreSaleInfoVO {
        String totalPrice;
        String deposit;
        String titleIcon;
        String presaleDate;
        String presaleEndDate;
        String presaleDesc;
        String rule;
        RuleDesc ruleDesc;

        String countdownIcon;
        String deductionDesc;
        Long salesNum;
    }

    @Override
    Object translate(PresaleDO presale) {
        if (!presale) {
            return null
        }
        return new PreSaleInfoVO(
                totalPrice: presale.totalPrice,
                deposit: presale.deposit,
                titleIcon: presale.titleIcon,
                presaleDate: presale.presaleDate,
                presaleEndDate: presale.presaleEndDate,
                presaleDesc: presale.presaleDesc,
                rule: presale.rule,
                ruleDesc: presale.ruleDesc,
                countdownIcon: presale.countdownIcon,
                deductionDesc: presale.deductionDesc,
                salesNum: presale.salesNum,
        )
    }
}