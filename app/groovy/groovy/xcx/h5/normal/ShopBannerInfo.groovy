package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.constant.ActivityType
import groovy.xcx.h5.normal.utils.ActivityManager
import groovy.xcx.h5.normal.utils.NormalCountdownKey

/**
 * Created by jinger on 31/10/2018.
 * 品牌特卖 Banner
 */

@Translator(id = "shopBannerInfo")
class ShopBannerInfo implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO, ShopBannerVO> {

    static class ShopBannerVO {
        String img
        String link
    }

    @Override
    ShopBannerVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimpleDO) {

        // 是否有值
//        if (!normalCountdown?.countdownInfoMap?.get(NormalCountdownKey.PPTM)?.price) {
//            return new ShopBannerVO()
//        }
        if (!itemBase?.activityBanner?.img || !itemBase?.activityBanner?.link) {
            return new ShopBannerVO()
        }

        // 是否在品牌特卖期
        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountdown, liveSimpleDO)

        if(activityType != ActivityType.PPTM_IN) {
            return new ShopBannerVO()
        }

        return new ShopBannerVO(
                img: itemBase?.activityBanner?.img,
                link: itemBase?.activityBanner?.link
        )

    }
}
