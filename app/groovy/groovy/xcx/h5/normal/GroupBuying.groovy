package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.h5.base.ItemTagVO

/**
 * Created by changsheng on 22/03/2017.
 * H5私有模块-普通详情页-团购信息
 */

@Translator(id = "groupBuying")
class GroupBuying implements ITwoDependTranslator<GroupbuyingDO, ShopDO, GroupBuyingVO> {

    static class GroupBuyingVO {
        String icon;
        String backgroundColor;
        ItemTagVO[] services;
    }

    @Override
    GroupBuyingVO translate(GroupbuyingDO groupbuying, ShopDO shop) {
        if (!(groupbuying && (groupbuying.status == TuanStatus.PRE || groupbuying.status == TuanStatus.IN))) {
            return null;
        }
        def imageUrlMap = [:]
        imageUrlMap.put(TuanType.NORMAL, ImageUtil.img("/p1/160805/idid_ie3wimzwmyzwizrsmezdambqgayde_264x44.png"))
        imageUrlMap.put(TuanType.STORE, ImageUtil.img("/p1/160728/idid_ifrdcnrtgjqwcnzqmezdambqmeyde_268x44.png"))

        return new GroupBuyingVO(
                icon: imageUrlMap[groupbuying.type],
                backgroundColor: "#FFF9F9",
                services: shop?.services?.collect {
                    new ItemTagVO(
                            link: it.link,
                            name: it.name,
                            icon: it.icon
                    )
                }
        );
    }
}