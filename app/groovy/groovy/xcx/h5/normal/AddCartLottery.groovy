package groovy.xcx.h5.normal

/**
 * Created by changsheng on 14/09/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "addCartLottery")
class AddCartLottery implements IOneDependTranslator<ItemBaseDO, AddCartLotteryVO> {

    static class AddCartLotteryVO {
        AddCartTipVO addCartTip
        String activityId
        List<Map<String, Object>> lotteryResult

    }

    static class AddCartTipVO {
        String text
        String textColor
        String image
        Integer tipTimes
    }

    @Override
    AddCartLotteryVO translate(ItemBaseDO itemBase) {
        Boolean redPacketSwitch = itemBase?.redPacketSwitch
        Boolean addCartTips = itemBase?.addCartTips

        AddCartTipVO addCartTip = new AddCartTipVO()
        String activityId = ""
        List<Map<String, Object>> lotteryResult = []

        if (addCartTips) {
            /**
             * 加购抽奖的Tips气泡
             */
            List<Map<String, Object>> addCartMaitData = MaitUtil.getTargetedMaitData(89246)
            Map<String, Object> addCartData = addCartMaitData?.get(0)
            addCartTip = new AddCartTipVO(
                    text: addCartData?.addCartText,
                    textColor: addCartData?.textColor,
                    image: addCartData?.addCartBg,
                    tipTimes: addCartData?.tipTimes
            )
        }

        if (redPacketSwitch) {
            /**
             * 抽奖的活动ID
             */
            List<Map<String, Object>> activityMaitData = MaitUtil.getTargetedMaitData(89858)
            Map<String, Object> activityData = activityMaitData?.get(0)
            activityId = activityData?.addCartGetRedPackets

            /**
             * 抽奖结果列表
             */
            lotteryResult = MaitUtil.getTargetedMaitData(89859)

        }
        return new AddCartLotteryVO(
                addCartTip: addCartTip,
                activityId: activityId,
                lotteryResult: lotteryResult
        );
    }
}