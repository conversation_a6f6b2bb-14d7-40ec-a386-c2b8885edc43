package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceExtraDescVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.CountdownMaitId

import java.text.SimpleDateFormat

/**
 * Created by changsheng on 14/03/2017.
 * H5私有模块-普通详情页-价格
 */

@Translator(id = "normalPrice")
class NormalPrice implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, NormalCountdownDO, LiveSimpleDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, NormalCountdownDO normalCountdown, LiveSimpleDO liveSimpleDO) {
        if (!itemBase) {
            return null
        }
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase, sku, extra)
        Map<String, CountdownInfo> countdownInfoMap = normalCountdown?.countdownInfoMap
        CountdownInfo countdownInfo = countdownInfoMap?.find {
            it?.value != null && it?.value?.state == CountdownState.IN_ACTIVITY
        }?.value

        /**
         * 预售>大促>团购>普通
         */
        if (presale != null) {
            // 预售
            itemPrice.with {
                nowPrice = presale?.totalPrice?.replace("¥", "");
                setOldPriceByRange(itemBase);
                priceTags = [
                        new PriceTagVO(
                                text: "预售价"
                        )
                ];
                eventPrice = presale?.deposit;
                eventPriceDesc = new PriceTagVO(
                        text: "定金"
                )
            }
        } else if (activity != null && (activity.warmUpPrice || activity.inActivityItem)) {
            // 大促
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                priceTags = activity?.priceDesc ? [
                        new PriceTagVO(
                                text: activity?.priceDesc
                        )
                ] : []
                eventPrice = activity?.warmUpPrice?.price
                eventPriceColor = activity?.warmUpPrice?.color
                if (eventPrice) {
                    eventPriceDesc = new PriceTagVO(
                            text: activity?.warmUpPrice?.priceDesc,
                            bgColor: "#FFFFFF" //无背景色
                    )
                }
            }
        } else if (countdownInfo && countdownInfo?.state == CountdownState.IN_ACTIVITY) {
            // 限时爆款活动进行中
            Long maitId = countdownInfo?.maitId
            List<Map<String, Object>> countdownInfoMaitData = MaitUtil.getMaitData(maitId)
            Map<String, Object> countdownInfoImgData = countdownInfoMaitData?.get(0)

            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: countdownInfoImgData?.get("priceDesc")
                        )
                ]

            }
        } else if (groupbuying != null && (groupbuying.status == TuanStatus.IN || groupbuying.status == TuanStatus.PRE)) {
            // 团购
            // 都展示为团购价
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(CountdownMaitId.TUANGOU)
            Map<String, Object> countdownInfoImgData = maitData?.get(0)
            String groupbuyingText = countdownInfoImgData?.get("priceTag")
//            if (groupbuying.bizType == TuanBizType.UZHI) {
//                groupbuyingText = "U质团"
//            } else if (groupbuying.bizType == TuanBizType.PINPAI) {
//                groupbuyingText = "品牌团"
//            }

            if (groupbuying.status == TuanStatus.IN) {
                // 团购正式
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    } else {
                        nowPrice = groupbuying?.price;
                    }

                    priceTags = [
                            new PriceTagVO(
                                    text: groupbuyingText
                            )
                    ]
                }

            } else if (groupbuying.status == TuanStatus.PRE) {
                // 团购预热
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    }
                    // priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                    eventPrice = groupbuying?.price;
                    eventPriceDesc = new PriceTagVO(
                            text: groupbuyingText
                    )

                    // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                    if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                        eventPriceDesc.text = ""
                    }
                }
            }
        } else {
            // 普通
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
            }
        }

        // 有拼团就要展示拼团价
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            itemPrice.with {
                nowPrice = pinTuan?.skuInfo?.lowNowPrice
                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                oldPrice = getOldPriceForce(itemBase)
            }
        }


        itemPrice.with {
            // 统一的活动Tag
            eventTags = activity?.eventTags?.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }

            // 手机专享价
            mobilePrice = itemBase?.extra?.mobilePrice
            mobileDownloadLink = itemBase?.extra?.mobileDownloadLink

            extraDesc = extra?.crossStoreDiscount

            // 前半小时
            extraDescs = new ArrayList<PriceExtraDescVO>()
            if (extra?.crossStoreDiscount) {
                extraDescs.add(new PriceExtraDescVO(
                        desc: extra?.crossStoreDiscount,
                        bgColor: "#FFF3E6",
                        textColor: "#FA6813"
                ))
            }

            // 待开售
            if (itemBase?.state == 3 && extra?.onSaleTime > 0) {
                Date date = new Date(extra?.onSaleTime * 1000)
                SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日HH:mm")
                String dateString = formatter.format(date)
                extraDescs.add(new PriceExtraDescVO(
                        desc: "${dateString}开售，请提前设置提醒",
                        bgColor: "#e8f7e1",
                        textColor: "#5eca2a"
                ))

//                String desc = "${dateString}开售，请提前设置提醒"

//                eventDesc = eventDesc? "${eventDesc} ${desc}".toString() : desc.toString()
            }
        }

        if (itemPrice.nowPrice != null) {
            itemPrice.nowPriceNum = (itemPrice.nowPrice?.toBigDecimal() * 100).toInteger()
        }

        Util.setEsiDataForPrice(itemPrice)

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        itemPrice.isHideOldPrice(itemBase)

        return itemPrice
    }

    /**
     * 取拼团的priceTag
     * @param itemBase
     * @param pinTuan
     * @param presale
     * @return
     */
    static List<PriceTagVO> getDefaultPriceTags(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            return [
                    new PriceTagVO(
                            text: "拼团价"
                    )
            ]
        } else if (itemBase?.discountDesc) {
            return [
                    new PriceTagVO(
                            text: itemBase?.discountDesc
                    )
            ]
        } else {
            return []
        }

    }

}