package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.ExplainInfo
import com.mogujie.detail.module.live.domain.LiveAnchorInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.xcx.h5.base.LinkInfoVO
import groovy.xcx.h5.base.Util

import java.text.DecimalFormat

/**
 * Create by jinger on 2019/09/24 14:12
 * 直播视频条：直播中的信息条 > 直播讲解视频
 */
@Translator(id = "liveExplainInfo", defaultValue = DefaultType.EMPTY_MAP)
class LiveExplainInfo implements ITwoDependTranslator<ItemBaseDO, LiveSimpleDO, LiveExplainVO> {

    static class LiveExplainVO {
        String title
        String desc
        String avatar
        LinkInfoVO linkInfo
        List<TagVO> tags
        TagVO activityTag // 秒杀中
        String acm

        // 直播中 or 商品讲解
        LiveType type
        String rightText
        String rightTextColor       = "#333333"
        String rightTextBgColor     = defaultRightTextBgColor
        String rightIcon

        String bgImage
        String titleColor           = "#FFFFFF"
        String descColor            = "#FFFFFF"

        static final String defaultRightTextBgColor = Util.formatColor("#77FFFFFF")

    }

    enum LiveType {
        LIVE,
        EXPLAIN,
    }


    static class TagVO {
        String text
        String bgColor = defaultBgColor
        String textColor = defaultTextColor

        static final String defaultBgColor = "#F0F1F7"
        static final String defaultTextColor = "#677AA2"
    }

    @Override
    LiveExplainVO translate(ItemBaseDO itemBase,LiveSimpleDO liveSimple) {

        // 如果链路上有liveparams，展示里面的主播的信息；不然就展示所有当前在直播这个商品的主播中销量最高的主播
        if (liveSimple?.actUserInfo?.isLiving()) {

            Map<String, Object> maitData = MaitUtil.getMaitData(140584)?.get(0)
            LiveAnchorInfo anchorInfo = liveSimple?.actUserInfo

            String linkUrl = anchorInfo?.xcxLiveUrl
                   linkUrl = Util.addAcmToUrl(linkUrl, maitData?.get("acm"))

            String h5LinkUrl = anchorInfo?.h5LiveUrl
                   h5LinkUrl = Util.addAcmToUrl(h5LinkUrl, maitData?.get("acm"))

            List<TagVO> tags = anchorInfo?.tags?.collect {
                TagVO tag = new TagVO(
                        text: it?.name,
                        bgColor: Util.formatColor(maitData?.get("tagBgColor")),
                        textColor: Util.formatColor(maitData?.get("tagColor"))
                )
                return tag
            }

            tags = tags ? tags.subList(0, Math.min(1, tags.size())) : null


            return new LiveExplainVO(
                    avatar: anchorInfo?.avatar,
                    title: formatAnchorName(anchorInfo?.name),
                    desc: getAnchorDesc(anchorInfo?.height, anchorInfo?.weight),
                    acm: maitData?.get("acm"),
                    type: LiveType.LIVE,
                    linkInfo: new LinkInfoVO(
                            url: linkUrl,
                            h5Url: h5LinkUrl
//                            appId: 'wx21c17841db9593cd',
//                            errMsg: '您可以搜索“蘑菇街超级购物台”获取更多服务'
                    ),
                    activityTag: new TagVO(
                            text: "秒杀中",
                            textColor: "#FFFFFF",
                            bgColor: "background-image: linear-gradient(90deg, #EEBF55, #E2AF4B);"
                    ),
                    tags: tags,
                    titleColor: Util.formatColor(maitData?.get("hostNameColor")),
                    descColor: Util.formatColor(maitData?.get("hostPhysiqueColor")),
                    rightText: "直播中",
                    rightTextBgColor: Util.formatColor(maitData?.get("guideBgColor")),
                    rightTextColor: Util.formatColor(maitData?.get("guideTextColor")),
                    rightIcon: maitData?.get("live_icon"),
                    bgImage: maitData?.get("background")

            )
        }

        // 视频讲解
        if (liveSimple?.explainInfo) {

            Map<String, Object> maitData = MaitUtil.getMaitData(140706)?.get(0)
            ExplainInfo anchorInfo = liveSimple?.explainInfo
            // explainInfo.itemId 影子商品
            String linkUrl = "https://h5.mogu.com/live-goods-explain/goods-explain.html?itemId=${anchorInfo.itemId}&mainItemId=${itemBase.iid}&actorId=${anchorInfo.actUserId}&source=detail"
            linkUrl = Util.addAcmToUrl(linkUrl, maitData?.get("acm"))

            List<TagVO> tags = anchorInfo?.tags?.collect {
                TagVO tag = new TagVO(
                        text: it?.name,
                        bgColor: Util.formatColor(maitData?.get("tagBgColor")),
                        textColor: Util.formatColor(maitData?.get("tagColor"))
                )
                return tag
            }

            tags = tags ? tags.subList(0, Math.min(1, tags.size())) : null

            return new LiveExplainVO(
                    avatar: anchorInfo.avatar,
                    title: formatAnchorName(anchorInfo.actUserName),
                    desc: getAnchorDesc(anchorInfo?.actHeight, anchorInfo?.actWeight),
                    acm: maitData?.get("acm"),
                    type: LiveType.EXPLAIN,
                    linkInfo: new LinkInfoVO(
                            url: "/pages/web/index?src=${URLEncoder.encode(linkUrl, "UTF-8")}",
                            h5Url: linkUrl
//                            appId: 'wx21c17841db9593cd',
//                            errMsg: '您可以搜索“蘑菇街超级购物台”获取更多服务'
                    ),
                    tags: tags,
                    titleColor: Util.formatColor(maitData?.get("hostNameColor")),
                    descColor: Util.formatColor(maitData?.get("hostPhysiqueColor")),
                    rightText: "商品讲解",
                    rightTextBgColor: Util.formatColor(maitData?.get("guideBgColor")),
                    rightTextColor: Util.formatColor(maitData?.get("guideTextColor")),
                    rightIcon: maitData?.get("explain_icon"),
                    bgImage: maitData?.get("background")
            )
        }
        return null
    }

    static String getAnchorDesc(Double height, Double weight) {
        DecimalFormat decimalFormat = new DecimalFormat("###.##")
        String heightDesc = height ? "<${decimalFormat.format(height)}>cm&nbsp;" : ""
        String weightDesc = weight ? "<${decimalFormat.format(weight)}>kg" : ""
        return "${heightDesc}${weightDesc}"
    }

    static String formatAnchorName(String hostName) {
        hostName = hostName ?: ""
        int maxCharNum = 12 // 象形文字算1个单位，其他算0.5个单位
        int count = 0
        int index = 0
        for (; index < hostName.length() && count < maxCharNum; index++) {
            if (Character.isIdeographic(hostName.codePointAt(index))) {
                count += 2
            } else {
                count += 1
            }
        }
        String suffix = hostName.length() > index ? "..." : ""
        hostName = hostName.substring(0, index) + suffix
        return hostName
    }
}