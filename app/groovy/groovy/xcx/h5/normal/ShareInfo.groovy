package groovy.xcx.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ShareInfoVO

/**
 * Created by chang<PERSON><PERSON> on 23/03/2017.
 */

@Translator(id = "shareInfo")
class ShareInfo implements ITwoDependTranslator<ItemBaseDO, ExtraDO, ShareInfoVO> {


    @Override
    ShareInfoVO translate(ItemBaseDO itemBase, ExtraDO extra) {
        if (!itemBase) {
            return null
        }
        Integer itemState = itemBase?.state
        // 商品可购买，并且排除掉虚拟商品
        Boolean isNeedShareIntegral = (itemState == 0 || (itemState == 3 && extra?.onSaleTime > 0)) && itemBase?.virtualItemType == VirtualItemType.NORMAL

        return new ShareInfoVO(isNeedShareIntegral)
    }
}