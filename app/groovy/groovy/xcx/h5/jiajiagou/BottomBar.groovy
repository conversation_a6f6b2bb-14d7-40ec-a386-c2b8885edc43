package groovy.xcx.h5.jiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO
import groovy.xcx.h5.base.ItemState

@Translator(id = "bottomBar")
class BottomBar implements IOneDependTranslator<ItemBaseDO, BottomBarVO> {

    @Override
    BottomBarVO translate(ItemBaseDO itemBase) {
        List<BottomBarButtonVO> buttons = new ArrayList<>()

        buttons.add(new BottomBarButtonVO(
                type: "icon",
                name: "shop",
                text: "店铺"
        ))

        buttons.add(new BottomBarButtonVO(
                type: "im",
                name: "im",
                text: "客服",
        ))

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: "buy",
                text: getBuyText(itemBase?.state),
                isDisabled: itemBase?.priceChannel != 2018 || itemBase?.state != ItemState.ITEM_ON_SALE
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }

    static private String getBuyText(Integer itemState) {
        switch (itemState) {
            case ItemState.ITEM_ON_SALE: return "立即购买"
            case ItemState.ITEM_OUT_OF_DATE: return "已下架"
            case ItemState.ITEM_OUT_OF_STOCK: return "卖光啦"
            default: return "立即购买"
        }
    }
}