package groovy.xcx.h5.jiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.PageInfoVO

@Translator(id = "jiajiagouPageInfo")
class JiajiagouPageInfo implements IOneDependTranslator<ItemBaseDO, PageInfoVO> {

    @Override
    PageInfoVO translate(ItemBaseDO itemBase) {
        if (itemBase?.priceChannel != 2018) {
            return new PageInfoVO(
                    readyTips: "您还没有获得换购资格哦"
            )
        } else {
            return new PageInfoVO()
        }
    }
}