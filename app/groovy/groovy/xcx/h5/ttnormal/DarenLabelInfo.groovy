package groovy.xcx.h5.ttnormal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.daren.domain.DarenDO

/**
 * Create by jinger on 2019/07/16 21:50
 */
@Translator(id = "darenLabelInfo", defaultValue = DefaultType.NULL)
class DarenLabelInfo implements IOneDependTranslator<DarenDO, DarenInfoVO> {

    static class DarenInfoVO {
        String avatar
        String title
        String desc
    }

    @Override
    DarenInfoVO translate(DarenDO daren) {
        if (daren?.name) {
            return new DarenInfoVO(
                    avatar: daren?.avatar,
                    title: daren?.name,
                    desc: "来自TA的推荐"
            )
        }

        return new DarenInfoVO()
    }
}