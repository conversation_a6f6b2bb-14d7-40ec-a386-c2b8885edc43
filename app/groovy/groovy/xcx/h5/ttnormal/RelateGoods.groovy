package groovy.xcx.h5.ttnormal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.daren.domain.DarenDO
import com.mogujie.detail.module.daren.domain.RelatedGoods
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Create by jinger on 2019/08/29 09:56
 * 抖音小程序关联商品
 */
@Translator(id = "relateGoods", defaultValue = DefaultType.NULL)
class RelateGoods implements IOneDependTranslator<DarenDO, RelateGoodsVO> {

    static class RelateGoodsVO {
        String title
        List<RelatedGoods> list
    }

    @Override
    RelateGoodsVO translate(DarenDO daren) {
        if (daren?.relatedGoods && daren.relatedGoods.size() > 0) {
            return new RelateGoodsVO(
                    title: "视频中其他商品",
                    list: daren.relatedGoods
            )
        }
        return new RelateGoodsVO()
    }
}