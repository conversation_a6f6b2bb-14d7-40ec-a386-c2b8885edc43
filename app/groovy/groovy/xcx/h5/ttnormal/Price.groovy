package groovy.xcx.h5.ttnormal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.daren.domain.DarenDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

/**
 * Created by xiaoque on 2019-07-11.
 */
@Translator(id = "price", defaultValue = DefaultType.NULL)
class Price implements ITwoDependTranslator<ItemBaseDO, DarenDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, DarenDO daren) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                if (daren?.isDaren && daren?.earn) {
                    priceTopTags = [
                            new PriceTagVO(
                                    "bgColor": "linear-gradient(90deg, #FFA25E 0%, #FF4466 100%)",
                                    "textColor": "#FFFFFF",
                                    "customStyle": "border-radius:4rpx;",
                                    "text": "预估收益:${daren?.earn}%"
                            )
                    ]
                }
            }

            Util.setEsiDataForPrice(itemPrice)

            // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
            itemPrice.isHideOldPrice(itemBase)

            return itemPrice
        }
    }
}