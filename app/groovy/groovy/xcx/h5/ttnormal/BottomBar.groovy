package groovy.xcx.h5.ttnormal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.daren.domain.DarenDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.xcx.h5.base.BottomBarButtonVO
import groovy.xcx.h5.base.BottomBarVO
import groovy.xcx.h5.base.ItemState

/**
 * Created by xiaoque on 2019-07-11.
 */
@Translator(id = "bottomBar", defaultValue = DefaultType.NULL)
class BottomBar implements IFourDependTranslator<ItemBaseDO, ExtraDO, PresaleDO, DarenDO, BottomBarVO> {


    @Override
    BottomBarVO translate(ItemBaseDO itemBase, ExtraDO extra, PresaleDO presale, DarenDO daren) {
        List<BottomBarButtonVO> buttons = new ArrayList<>()
        Boolean isDisabled = false
        Integer itemState = itemBase?.state

        Long onSaleTime = extra?.onSaleTime
        Long nowTime = System.currentTimeSeconds()
        Boolean isSaleStartTimeShow = itemState == ItemState.ITEM_WAIT_FOR_SALE && onSaleTime > nowTime

        // 如果当前用户是达人则展示
        if (daren?.isDaren) {
            buttons.add(new BottomBarButtonVO(
                    type: "icon",
                    name: "fav",
                    text: "收藏"
            ))
        }

        // 如果是预售或者待开售，按钮置灰
        if (isSaleStartTimeShow || presale != null) {
            isDisabled = true
        }

        buttons.add(new BottomBarButtonVO(
                type: "button",
                name: "buy",
                text: getBuyText(itemBase?.state),
                isDisabled: isDisabled || itemBase?.state != ItemState.ITEM_ON_SALE
        ))

        return new BottomBarVO(
                buttons: buttons
        )
    }

    static private String getBuyText(Integer itemState) {
        switch (itemState) {
            case ItemState.ITEM_ON_SALE: return "立即购买"
            case ItemState.ITEM_OUT_OF_DATE: return "已下架"
            case ItemState.ITEM_OUT_OF_STOCK: return "卖光啦"
            default: return "立即购买"
        }
    }
}