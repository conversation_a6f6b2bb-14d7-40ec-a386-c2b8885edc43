package groovy.xcx.h5.ttnormal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.xcx.h5.base.ItemState
import groovy.xcx.h5.base.PageInfoVO

/**
 * Create by jinger on 2019/07/16 21:41
 */
@Translator(id = "pageInfo", defaultValue = DefaultType.NULL)
class PageInfo implements IThreeDependTranslator<ItemBaseDO, ExtraDO, PresaleDO, PageInfoVO> {

    @Override
    PageInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, PresaleDO presale) {

        if (!itemBase) {
            return new PageInfoVO()
        }

        Integer itemState = itemBase?.state
        Long onSaleTime = extra?.onSaleTime
        Long nowTime = System.currentTimeSeconds()
        Boolean isSaleStartTimeShow = itemState == ItemState.ITEM_WAIT_FOR_SALE && onSaleTime > nowTime

        // 待开售和预售不支持购买，一进入页面会有一个toast
        if (isSaleStartTimeShow || presale != null) {
            return new PageInfoVO(
                    readyTips: "暂不支持此商品的购买，您可前往蘑菇街APP下单～"
            )
        }

        return new PageInfoVO()
    }
}