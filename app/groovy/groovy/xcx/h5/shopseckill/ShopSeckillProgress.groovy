package groovy.xcx.h5.shopseckill

import com.mogujie.detail.core.annotation.Translator

/**
 * Created by changsheng on 21/12/2017.
 */
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shopSeckill.domain.ShopSeckillDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.ProgressVO

@Translator(id = "shopSeckillProgress")
class ShopSeckillProgress implements IThreeDependTranslator<ShopSeckillDO, SkuDO, ItemBaseDO, ProgressVO> {

    @Override
    ProgressVO translate(ShopSeckillDO shopSeckill, SkuDO sku, ItemBaseDO itemBase) {
        if (!shopSeckill) {
            return new ProgressVO()
        }

        Integer originTotalStock = shopSeckill?.originTotalStock
        Integer nowStock = sku?.totalStock

        String background = "linear-gradient(to right, #FFCA49, #FF9A49)"

        Integer progress = 0
        if (originTotalStock > 0) {
            progress = Math.ceil((originTotalStock - nowStock) * 100 / originTotalStock)
        }

        String leftTitle = ""
        String rightTitle = ""
        String leftTextColor = "#999999"
        String rightTextColor = "#FF5777"

        ShopSeckillState seckillState = new ShopSeckillState(itemBase, shopSeckill)

        switch (seckillState?.state) {
            case ShopSeckillState.ACTIVITY_NOT_STARTED:
            case ShopSeckillState.ACTIVITY_WARM_UP:
                //未开始
                if (originTotalStock > 0) {
                    leftTitle = "库存${originTotalStock}件"
                }
                break
            case ShopSeckillState.ACTIVITY_STARTED:
                //活动中
                if (originTotalStock > 0) {
                    leftTitle = "已抢购${originTotalStock - nowStock}件"
                }
                rightTitle = "仅剩${nowStock}件"
                break
            case ShopSeckillState.OUT_OF_STOCK:
                // 库存不足
                leftTitle = "已售罄"
                break
            case ShopSeckillState.ACTIVITY_ENDED:
                //已结束
                leftTitle = "已结束"
                break
            default:
                break
        }

        return new ProgressVO(
                leftTitle: leftTitle,
                rightTitle: rightTitle,
                progress: progress,
                leftTextColor: leftTextColor,
                rightTextColor: rightTextColor,
                background: background
        )
    }
}