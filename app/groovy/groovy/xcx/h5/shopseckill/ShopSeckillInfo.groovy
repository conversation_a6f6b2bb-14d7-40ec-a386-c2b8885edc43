package groovy.xcx.h5.shopseckill

import com.mogujie.detail.core.annotation.Translator

/**
 * Created by chang<PERSON><PERSON> on 21/12/2017.
 */
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shopSeckill.domain.ShopSeckillDO

@Translator(id = "shopSeckillInfo")
class ShopSeckillInfo implements ITwoDependTranslator<ItemBaseDO, ShopSeckillDO, ShopSeckillInfoVO> {

    static class ShopSeckillInfoVO {
        /**
         * 0. 未开始
         * 1. 活动中
         * 2. 活动中, 但是库存为0，并且没有未付款人数
         * 3. 活动结束 （这个状态不会使用）
         * 4. 活动中, 但是库存为0，并且有未付款人数，此时点击按钮也是可以刷新的
         * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
         * 6. 商品下架
         */
        Integer state
        Long startTime
        Long endTime
        Long activityId
    }

    @Override
    ShopSeckillInfoVO translate(ItemBaseDO itemBase, ShopSeckillDO shopSeckill) {

        ShopSeckillState seckillState = new ShopSeckillState(itemBase, shopSeckill)

        return new ShopSeckillInfoVO(
                state: seckillState?.getState(),
                startTime: shopSeckill?.startTime,
                endTime: shopSeckill?.endTime,
                activityId: shopSeckill?.activityId
        )
    }
}