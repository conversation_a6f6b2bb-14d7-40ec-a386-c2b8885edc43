package groovy.xcx.h5.shopseckill

import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shopSeckill.domain.ShopSeckillDO

/**
 * Created by chang<PERSON><PERSON> on 21/12/2017.
 */

/**
 * 0. 未开始
 * 1. 活动中
 * 2. 库存不足
 * 3. 活动结束
 * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
 * 6. 商品下架
 */
class ShopSeckillState {
    public static final int ACTIVITY_NOT_STARTED = 0
    public static final int ACTIVITY_STARTED = 1
    public static final int OUT_OF_STOCK = 2
    public static final int ACTIVITY_ENDED = 3
    public static final int ACTIVITY_WARM_UP = 5
    public static final int ITEM_OUT_OF_DATE = 6

    Integer state

    ShopSeckillState(ItemBaseDO itemBase, ShopSeckillDO shopSeckill) {
        Integer nowTime = System.currentTimeSeconds()
        Integer startTime = shopSeckill?.startTime
        Integer endTime = shopSeckill?.endTime
        Integer state
        Integer itemState = itemBase?.state

        if (itemState == 1 || itemState == 3) {
            // 商品下架
            state = ITEM_OUT_OF_DATE
        } else if (nowTime + 300 < startTime) {
            state = ACTIVITY_NOT_STARTED
        } else if (nowTime + 300 >= startTime && nowTime < startTime) {
            state = ACTIVITY_WARM_UP
        } else if (nowTime > startTime && nowTime < endTime) {
            if (itemState == 2) {
                state = OUT_OF_STOCK
            } else {
                state = ACTIVITY_STARTED
            }
        } else {
            state = ACTIVITY_ENDED
        }

        this.state = state
    }

    Integer getState() {
        return this.state
    }

}