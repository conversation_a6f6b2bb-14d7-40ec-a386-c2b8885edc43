package groovy.xcx.h5.shopseckill

/**
 * Created by ch<PERSON><PERSON><PERSON> on 21/12/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.xcx.h5.base.ItemPriceVO
import groovy.xcx.h5.base.PriceTagVO
import groovy.xcx.h5.base.Util

@Translator(id = "shopSeckillPrice")
class ShopSeckillPrice implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (itemBase) {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: "秒杀价",
                                bgColor: "rgba(255, 255, 255, 0.3)",
                                textColor: "#FFFFFF"
                        )
                ]
            }
        }

        Util.setEsiDataForPrice(itemPrice)

        return itemPrice
    }
}