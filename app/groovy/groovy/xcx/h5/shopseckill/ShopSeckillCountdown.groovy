package groovy.xcx.h5.shopseckill

/**
 * Created by chang<PERSON><PERSON> on 21/12/2017.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.shopSeckill.domain.ShopSeckillDO
import groovy.xcx.h5.base.TopCountdownVO

@Translator(id = "shopSeckillCountdown")
class ShopSeckillCountdown implements IOneDependTranslator<ShopSeckillDO, TopCountdownVO> {


    @Override
    TopCountdownVO translate(ShopSeckillDO shopSeckill) {
        if (!shopSeckill) {
            return new TopCountdownVO()
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776)
        Map<String, Object> imgData = maitData?.get(0)

        String backgroundImg = imgData?.get("shopSeckillImage")
        String titleColor = imgData?.get("shopSeckillTitleColor")

        Integer nowTime = System.currentTimeSeconds()
        Integer startTime = shopSeckill?.startTime
        Integer endTime = shopSeckill?.endTime

        if (nowTime < startTime) {
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: endTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }
}