package groovy.msd.app.vo

import com.mogujie.detail.module.itemParams.domain.ProductInfo

/**
 * Created by fufeng on 2017/5/22.
 */
public class ProductInfoVO {

    String[] images
    KeyValuePair[] set
    String desc
    String key

    ProductInfoVO(ProductInfo info) {
        images = info?.images
        desc = info?.desc
        key = info?.key

        def convertedSet = []
        info?.set?.each { k, v ->
            convertedSet << new KeyValuePair(
                    key: k,
                    value: v
            )
        }
        set = convertedSet
    }
}