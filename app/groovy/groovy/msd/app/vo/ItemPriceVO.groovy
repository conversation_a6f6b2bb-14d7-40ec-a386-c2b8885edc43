package groovy.msd.app.vo

import com.mogujie.detail.core.adt.DetailContextHolder

class ItemPriceVO {

    transient String highNowPrice
    transient String highPrice
    transient String lowNowPrice
    transient String lowPrice

    String price
    String oldPrice
    String memberPrice
    String originPrice
    Boolean isFastBuy

    def updatePrices() {
        (oldPrice, price) = ItemPriceCalculator.calculatePrices(this)
        return this
    }


    class ItemPriceCalculator {
        static def calculatePrices(ItemPriceVO priceVO) {
            String oldPrice
            String price

            //lowPrice
            if (priceVO.lowPrice == priceVO.lowNowPrice && priceVO.highPrice == priceVO.highNowPrice) {
                oldPrice = null
            } else {
                oldPrice = "¥" + priceVO.lowPrice
                if (priceVO.lowPrice != priceVO.highPrice) {
                    String osV = DetailContextHolder.get().getOsVersion();
                    oldPrice = oldPrice + "~¥" + priceVO.highPrice;
                }
            }

            //price
            if (priceVO.lowNowPrice != priceVO.highNowPrice) {
                price = "¥" + priceVO.lowNowPrice + "~¥" + priceVO.highNowPrice
            } else {
                price = "¥" + priceVO.lowNowPrice
            }

            return new Tuple2(oldPrice, price)
        }
    }
}