package groovy.msd.app.detailinfo

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

@Translator(id = "msdShopDecoration")
class ShopDecoration implements IOneDependTranslator<DetailDO, Object>{
    @Override
    Object translate(DetailDO input1) {
        return input1?.shopDecorate
    }
}
