package groovy.msd.app.detailinfo

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

@Translator(id = "msdDetailInfoTitle")
class Title implements IOneDependTranslator<DetailDO, Object>{

    class TitleVO{
        String text
    }

    @Override
    Object translate(DetailDO input1) {
        if (!input1?.desc){
            return null
        }
        return  new TitleVO(text: input1.desc)
    }
}