package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.vo.SKUDataVOActivityType
import groovy.msd.app.vo.MSDSKULinkVO

/**
 * Created by fufeng on 2017/2/15.
 */

@Translator(id = "msdSkuSelect", defaultValue = DefaultType.EMPTY_MAP)
class MSDSKUSelect implements IFiveDependTranslator<SkuDO, ItemBaseDO, SizeHelperDO, SeckillDO, PinTuanDO, Object> {

    @Override
    MSDSKULinkVO translate(SkuDO input1, ItemBaseDO input2, SizeHelperDO input3, SeckillDO seckillDO, PinTuanDO pintuanDO) {
        MSDSKULinkVO skuLinkVO = superTranslate(input1, input2, seckillDO)
        if (skuLinkVO == null) {
            return null
        }

        if (skuLinkVO?.defaultTitle) {
            //分期标
            if (input1?.canInstallment) {
                skuLinkVO.defaultTitle = skuLinkVO.defaultTitle + " 分期方案"
            }
        }
        if (seckillDO != null) {
            //秒杀优先
            skuLinkVO.activityType = SKUDataVOActivityType.SECKILL
            skuLinkVO.skuCommunicationType = SKUDataVOActivityType.DEFAULT
        } else if (Tools.isPintuan(input2, pintuanDO)) {
            //其次拼团
            skuLinkVO.activityType = SKUDataVOActivityType.PINTUAN
            skuLinkVO.skuCommunicationType = SKUDataVOActivityType.PINTUAN
        }

        //如果是虚拟商品且不是秒杀，则返回空对象
        if (seckillDO == null && input2?.virtualItemType != VirtualItemType.NORMAL) {
            //因为已经写了 defaultType 所以可以返回 null了，此时实际上给的是 {}
            return null
        }

        if (input3 == null || input1.sizeKey == null) {
            return skuLinkVO
        }

        return skuLinkVO
    }

    MSDSKULinkVO superTranslate(SkuDO input1, ItemBaseDO input2, SeckillDO seckillDO) {
        if (!input1) {
            return null
        }

        def defaultTitle = null
        if (shouldShowSKUSelect(input2) || seckillDO != null) {
            defaultTitle = "请选择 ${input1.styleKey ? input1.styleKey : ''} ${input1.sizeKey ? input1.sizeKey : ''}"
        }

        return (defaultTitle != null) ? new MSDSKULinkVO(defaultTitle: defaultTitle) : null

    }

    static def shouldShowSKUSelect(ItemBaseDO item) {
        //正常与待开售需要显示
        return item && (item.state == 0 || item.state == 3)
    }
}