package groovy.msd.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.rate.domain.DetailRate
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.rate.domain.RateUserInfo
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.service.rate.domain.tag.RateTag

@Translator(id = "msdRateNormal", defaultValue = DefaultType.NULL)
class RateNormalV2 implements IFiveDependTranslator<RateDO, ItemBaseDO, BuyerShowDO, ExtraDO, ShopDO, Object> {
    static class RateVO {
        String title
        String subTitle
        String rateUrl
        Integer imgTotal
        List<RateEntryVO> list
        RateTagVO[] rateTags
    }

    static class RateEntryVO {
        Boolean canExplain
        String content
        Long created
        String explain
        List<String> images
        Integer isAnonymous
        Boolean isEmpty
        String level
        String rateId
        String style
        RateUserInfo user

        Map<String, String> sizeInfo
        String buyerShowDesc
        String buyerShowEx
        String buyerShowUrl
        boolean isBuyerShow

        RateEntryVO() {}

        RateEntryVO(DetailRate rate) {
            this.canExplain = rate.canExplain
            this.content = rate.content
            this.created = rate.created
            this.explain = rate.explain
            this.images = rate.images
            this.isAnonymous = rate.isAnonymous
            this.isEmpty = rate.isEmpty
            this.level = rate.level
            this.rateId = rate.rateId
            this.style = rate.style
            this.user = rate.user
            this.sizeInfo = rate.sizeInfo
            this.isBuyerShow = rate.isBuyerShow()
        }
    }

    static class RateUserVO {
        String avatar
        String profileUrl
        String tagIndex
        String uid
        String uname
    }

    static class RateTagVO {
        String emotion
        Integer num
        String property
        String value
        String link

        RateTagVO(RateTag tag, String iid, String themeName) {
            if (!tag) {
                return
            }
            this.emotion = tag.emotion
            this.num = tag.num
            this.property = tag.property
            this.value = tag.value
//            InvokerHelper.setProperties(this, tag.properties)
            link = "mlb://ratelist?iid=$iid&property=$property&emotion=$emotion&themeName=${themeName}"
        }
    }

    @Override
    Object translate(RateDO input1, ItemBaseDO input2, BuyerShowDO input3, ExtraDO input4, ShopDO shopDO) {
        if (DetailContextHolder.get().isDyn()) {
            // 业务上可以返回NULL
            return null
        }

        if (input1 == null) {
            return new RateVO(
                    title: "暂无评价"
            )
        }
        MSDThemeName themeNameTranslator = new MSDThemeName()
        String themeName = themeNameTranslator.translate(input2, shopDO)

        if (input1?.rateTags && input1.rateTags.size() > 0 && input1.imgTotal && input1.imgTotal > 0) {
            RateTag imgTag = new RateTag();
            imgTag.setNum(input1.imgTotal);
            imgTag.setEmotion("positive");
            imgTag.setProperty("图片");
            imgTag.setValue("有图片");
            input1.rateTags.add(0, imgTag);
        }

        RateVO VO = new RateVO(
                title: "买家评价 ${input1?.CRate}",
                subTitle: "销量 ${input4?.sales}",
                rateUrl: "mlb://ratelist?iid=${input2?.iid}",
                rateTags: input1?.rateTags ? input1.rateTags.collect {
                    new RateTagVO(it, input2?.iid, themeName)
                } : null
        )
        VO.rateUrl = VO.rateUrl + "&themeName=${themeName}"

        if (input1?.list?.size() > 0) {
            DetailRate detailRateDO = (DetailRate) input1.list[0]
            def rateEntry = new RateEntryVO(detailRateDO)
//            InvokerHelper.setProperties(rateEntry, detailRateDO.properties)
            if (detailRateDO?.isBuyerShow()) {
                rateEntry.buyerShowDesc = "红人精选"
                rateEntry.buyerShowEx = "- Experience -"
                //这里原来是直接跳转买家秀的短链,产品要求在详情页里面统一用调到RateList的短链
                def url = VO.rateUrl
                rateEntry.buyerShowUrl = url
                rateEntry.isBuyerShow = detailRateDO.isBuyerShow()

            }
            VO.list = [rateEntry]
        }
        if (input1?.getCRate() == 0) {
            VO.rateUrl = null
            VO.title = "暂无评价"
        }

        if (input4 == null || input4?.sales == 0.longValue()) {
            VO.subTitle = "销量 0"
        }

        return VO
    }
}
