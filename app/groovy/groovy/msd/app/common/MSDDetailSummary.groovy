package groovy.msd.app.common

/**
 * Created by wuyi on 2018/6/1.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import groovy.msd.app.vo.PriceTagVO
import groovy.msd.app.vo.SummaryVO

/**
 * Created by fufeng on 2017/8/16.
 */
@Translator(id = "msdSummary")
class MSDDetailSummary implements IThreeDependTranslator<ItemBaseDO, BuyershopDO, NormalCountdownDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO, BuyershopDO buyershopDO, NormalCountdownDO normalCountdownDO) {
        if (!itemBaseDO) {
            //如果 ItemBase都是null了，就return null吧，至少可以保持有价格出现
            return null
        }
        def summary = new SummaryVO()

        //默认属性
        summary.priceColor = "#333333"
        summary.title = itemBaseDO.title
        summary.highNowPrice = itemBaseDO.highNowPrice
        summary.lowNowPrice = itemBaseDO.lowNowPrice
        summary.highPrice = itemBaseDO.highPrice
        summary.lowPrice = itemBaseDO.lowPrice
        summary.isMember = buyershopDO.isVipUser
        if (buyershopDO?.lowVipUserPrice > 0 && buyershopDO?.highVipUserPrice > 0) {
            summary.memberPriceIcon = "https://s10.mogucdn.com/mlcdn/c45406/180803_2i5dg6k75f484c3a63ad22b884h37_171x51.png"
            if (buyershopDO.lowVipUserPrice != buyershopDO.highVipUserPrice) {
                summary.memberPrice = "¥" + NumUtil.formatNum(buyershopDO.lowVipUserPrice / 100D) + "~¥" + NumUtil.formatNum(buyershopDO.highVipUserPrice / 100D)
            } else {
                summary.memberPrice = "¥" + NumUtil.formatNum(buyershopDO.lowVipUserPrice / 100D)
            }
        }

        //普通
        summary.updatePrices()

        if (summary.isMember) {
            if (buyershopDO?.lowNormalUserPrice > 0 && buyershopDO?.highNormalUserPrice > 0) {
                if (buyershopDO.lowNormalUserPrice != buyershopDO.highNormalUserPrice) {
                    summary.price = "¥" + NumUtil.formatNum(buyershopDO.lowNormalUserPrice / 100D) + "~¥" + NumUtil.formatNum(buyershopDO.highNormalUserPrice / 100D)
                } else {
                    summary.price = "¥" + NumUtil.formatNum(buyershopDO.lowNormalUserPrice / 100D)
                }
            }
            if (buyershopDO?.lowNormalUserPrice > buyershopDO?.lowVipUserPrice && buyershopDO?.highNormalUserPrice > buyershopDO?.highVipUserPrice && buyershopDO?.lowVipUserPrice > 0 && buyershopDO?.highVipUserPrice > 0) {
                if (buyershopDO.lowNormalUserPrice != buyershopDO.highNormalUserPrice) {
                    summary.memberPriceDesc = new PriceTagVO(
                            text: "为你节省" + NumUtil.formatPriceDrawer(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue()) + "~" + NumUtil.formatPriceDrawer(buyershopDO.highNormalUserPrice.intValue() - buyershopDO.highVipUserPrice.intValue()) + "元",
                            textColor: "#333333",
                            bgColor: "#FFFFFF"
                    )
                } else {
                    summary.memberPriceDesc = new PriceTagVO(
                            text: "为你节省" + NumUtil.formatPriceDrawer(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue()) + "元",
                            textColor: "#333333",
                            bgColor: "#FFFFFF"
                    )
                }
            }
        }

        if (buyershopDO?.seller && buyershopDO?.minCommission > 0) {
            summary.priceTag = new PriceTagVO(
                    text: buildCommission(buyershopDO.minCommission, buyershopDO.maxCommission),
                    textColor: "#D84F49",
                    bgColor: "#FFFFFF"
            )
        }

        // 为了覆盖esi
        if (summary.priceTag == null) {
            summary.priceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
        }

        summary.originPrice = "¥" + itemBaseDO.lowPrice
        CountdownInfo countdownInfo = normalCountdownDO?.getCountdownInfoMap()?.get("msdFlashSale")
        if (countdownInfo == null) {
            summary.isFastBuy = false
        } else {
            summary.isFastBuy = true
            if (countdownInfo.memberPriceMap != null && countdownInfo.priceMap != null) {
                List<Long> priceList = generatePrice(countdownInfo.priceMap)
                List<Long> memberPriceList = generatePrice(countdownInfo.memberPriceMap)
                if(priceList != null && memberPriceList != null && countdownInfo.memberPriceMap.size() >= 2 && countdownInfo.priceMap.size() >= 2) {
                    summary.price = "¥" + NumUtil.formatNum(priceList.get(0) / 100D) + "~¥" + NumUtil.formatNum(priceList.get(1) / 100D)
                    summary.memberPrice = "¥" + NumUtil.formatNum(memberPriceList.get(0) / 100D) + "~¥" + NumUtil.formatNum(memberPriceList.get(1) / 100D)
                    float priceStart = priceList.get(0) - memberPriceList.get(0)
                    float priceEnd = priceList.get(1) - memberPriceList.get(1)
                    summary.memberPriceDesc = new PriceTagVO(
                            text: "为你节省" + NumUtil.formatPriceDrawer(priceStart.intValue()) + "~" + NumUtil.formatPriceDrawer(priceEnd.intValue()) + "元",
                            textColor: "#333333",
                            bgColor: "#FFFFFF"
                    )
                    summary.originPrice = "¥" + itemBaseDO.lowPrice + "~¥" + itemBaseDO.highPrice
                } else {
                    summary.price = "¥" + NumUtil.formatNum(countdownInfo.price / 100D)
                    summary.memberPrice = "¥" + NumUtil.formatNum(countdownInfo.memberPrice / 100D)
                    summary.memberPriceDesc = new PriceTagVO(
                            text: "为你节省" + NumUtil.formatPriceDrawer((countdownInfo.price.intValue() - countdownInfo.memberPrice.intValue())) + "元",
                            textColor: "#333333",
                            bgColor: "#FFFFFF"
                    )
                }
            } else {
                summary.price = "¥" + NumUtil.formatNum(countdownInfo.price / 100D)
                if(countdownInfo.memberPrice != null) {
                    summary.memberPrice = "¥" + NumUtil.formatNum(countdownInfo.memberPrice / 100D)
                    summary.memberPriceDesc = new PriceTagVO(
                            text: "为你节省" + NumUtil.formatPriceDrawer((countdownInfo.price.intValue() - countdownInfo.memberPrice.intValue())) + "元",
                            textColor: "#333333",
                            bgColor: "#FFFFFF"
                    )
                }
            }
        }
        summary.oldPrice = summary.price

        return summary
    }

    private List<Long> generatePrice(Map<Long, Long> priceMap) {
        if(priceMap == null || priceMap.isEmpty()){
            return null
        }

        Long maxPrice = Long.MIN_VALUE
        Long minPrice = Long.MAX_VALUE
        priceMap.each {
            Long price = it.value

            if(price > maxPrice){
                maxPrice = price
            }
            if (price < minPrice) {
                minPrice = price
            }
        }
        if(minPrice == maxPrice){
            return null
        }
        List<Long> priceList = new ArrayList<>()
        priceList.add(minPrice)
        priceList.add(maxPrice)
        return priceList

    }


    private String buildCommission(int minCommission, int maxCommission) {
        if (maxCommission > minCommission) {
            return "赚" + formatPrice(minCommission) + "~" + formatPrice(maxCommission)
        } else {
            return "赚" + formatPrice(minCommission)
        }
    }

    private String formatPrice(int pennyPrice) {
        return String.format("￥%.2f", ((double) pennyPrice) / 100)
    }
}

