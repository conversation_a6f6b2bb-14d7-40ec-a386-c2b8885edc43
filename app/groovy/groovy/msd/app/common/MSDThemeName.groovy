package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO

/**
 * Created by wuy<PERSON> on 2018/6/1.
 */
@Translator(id = "themeName")
class MSDThemeName implements ITwoDependTranslator<ItemBaseDO, ShopDO, String> {
    @Override
    String translate(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        return "msd_normal_pink"
    }
}