package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.coupon.domain.PlatformCoupon
import groovy.msd.app.vo.MSDTextStyle

@Translator(id = 'msdPlatformCoupon', defaultValue = DefaultType.NULL)
class MSDDetailPlatformCoupon implements IOneDependTranslator<CouponDO, PlatformCouponVO> {

    class PlatformCouponVO{
        PlatformCouponItemVO[] platformCoupon
    }
    class PlatformCouponItemVO{
        String iconTitle
        String title
        String useTimeDesc
        String accessoryTitle
        String linkUrl
        String img
        MSDTextStyle iconTitleTextStyle

        PlatformCouponItemVO(PlatformCoupon coupon) {
            this.iconTitle = coupon.iconTitle
            this.title = coupon.title
            this.useTimeDesc = coupon.useTimeDesc
            this.accessoryTitle = coupon.accessoryTitle
            this.linkUrl = coupon.linkUrl
            this.img = coupon.img
//            InvokerHelper.setProperties(this, coupon.properties)
        }
    }

    @Override
    PlatformCouponVO translate(CouponDO input1) {
        if (!input1?.platformCoupon){
            // 业务上要求返回NULL，因为动态请求时不会有 CouponDO
            return null
        }

        //部分活动商品标 Tag 样式
        //麦田 18353
        def promotionTags = MaitUtil.getMaitData(18353)

        HashMap<String, MSDTextStyle> promotionTagMap = new HashMap<String, MSDTextStyle>()
        promotionTags?.each {
            String activityName = it.get("activityName")
            if (activityName) {
                promotionTagMap.put(activityName,new MSDTextStyle(textColor: it.get("tagTextColor"), backgroundColor: it.get("tagBgColor"), fontSize: 10))
            }
        }

        PlatformCouponVO platformCouponVO = new PlatformCouponVO()
        platformCouponVO.platformCoupon = input1?.platformCoupon?.collect {
            def platformCouponItemVO = new PlatformCouponItemVO(it)
            platformCouponItemVO.iconTitleTextStyle =
                    promotionTagMap.get(platformCouponItemVO.iconTitle, new MSDTextStyle(textColor: "#FFFFFF", backgroundColor: "#FF5577", fontSize: 10))
            return platformCouponItemVO
        }

        return platformCouponVO
    }
}