package groovy.msd.app.common

import com.mogujie.detail.core.translator.IFiveDependTranslator

/**
 * Created by fuf<PERSON> on 2017/4/1.
 */
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import groovy.msd.app.vo.CountdownVO

//这里展示不需要@translator,因为实际是在group里面返回的
class CountDownNormal implements IFiveDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, CountdownVO> {
    @Override
    CountdownVO translate(ActivityDO input1, GroupbuyingDO input2, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO) {
        // 目前买手店只有限时快抢
        CountdownInfo xskq = normalCountdownDO?.getCountdownInfoMap()?.get("msdFlashSale")
        // 限时快抢
        if (xskq && xskq.warmUpTime <= System.currentTimeSeconds() && xskq.startTime > System.currentTimeSeconds() && xskq.state == CountdownState.WARM_UP) {
            // 预热期
            def xskqMait = MaitUtil.getMaitData(xskq.maitId)?.get(0)
            def result = new CountdownVO(
                    countdownBgImg: xskqMait?.get("backgroundImage"),
                    countdownTitleColor: xskqMait?.get("fontColor"),
                    countdown: xskq.countdown,
                    countdownTitle: "距开始仅剩",
                    type: 0
            )
            return result
        } else if (xskq && xskq.startTime <= System.currentTimeSeconds() && xskq.endTime > System.currentTimeSeconds() && xskq.state == CountdownState.IN_ACTIVITY) {
            // 正式期
            def xsbkMait = MaitUtil.getMaitData(xskq.maitId)?.get(0)
            def result = new CountdownVO(
                    countdownBgImg: xsbkMait?.get("backgroundImage"),
                    countdownTitleColor: xsbkMait?.get("fontColor"),
                    countdown: xskq.countdown,
                    countdownTitle: "距结束仅剩",
                    type: 0
            )
            return result
        }

        return new CountdownVO()//保证esi能覆盖缓存数据
    }
}