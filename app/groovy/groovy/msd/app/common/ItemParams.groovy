package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.msd.app.vo.ItemParamsDetailVO
import groovy.msd.app.vo.ItemParamsVO

@Translator(id = "itemParamsApp", defaultValue = DefaultType.NULL)
class ItemParams implements IOneDependTranslator<ItemParamsDO, Object> {

    @Override
    Object translate(ItemParamsDO input1) {
        if (!input1) {
            // 业务上可以返回NULL
            return null
        }
        return new ItemParamsVO(itemParams: new ItemParamsDetailVO(input1))
    }
}
