package groovy.msd.app.common.sku.dataProvider

import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.vo.SKUDataVOActivityType
import groovy.msd.app.vo.TextTag

/**
 * Created by fufeng on 2017/12/14.
 */
class SKUExtensionDataProvider {
    static TextTag getInstallmentMaitData (SkuDO aSkuDO) {
        /**
         * 白付美分期免息
         */
        def installmentMait
        def sku = aSkuDO?.skus?.find {
            element -> element?.installment != null
        }
        boolean hasInstallment = sku ? true : false
        if (hasInstallment) {
            def title
            def promotionTags = MaitUtil.getMaitData(50418)
            def maitInfo =  promotionTags?.get(0)
            String textColor = maitInfo?.get("textColor") ?: "#FFFFFF"
            String bgColor = maitInfo?.get("bgColor") ?: "FF5777"
            if (aSkuDO?.freePhases) {
                String freePhasesText = maitInfo?.get("freePhasesText")
                if (freePhasesText) {
                    title = freePhasesText.replace("{phases}", aSkuDO.freePhases.toString())
                }
            }
            else {
                String text = maitInfo?.get("text")
                title = text
            }
            installmentMait = new TextTag(text: title?:"", textColor: textColor?:"", bgColor: bgColor?:"")
        }

        return installmentMait ?: new TextTag()
    }

    static int getActivityType(ItemBaseDO aItemBaseDO, PinTuanDO aPintuanDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO) {
        def activityType = SKUDataVOActivityType.DEFAULT
        if (Tools.isPintuan(aItemBaseDO, aPintuanDO)) {
            activityType = SKUDataVOActivityType.PINTUAN
        } else if (aPreSaleDO) {
            activityType = SKUDataVOActivityType.PRESALE
        } else if (aGroupBuyingDO?.status == TuanStatus.IN) {
            activityType = SKUDataVOActivityType.TUANGOU
        }
        return activityType
    }

    static def getSkuActionState(ItemBaseDO itemBaseDO, int activityType) {
        def stateDesc = ""
        def isFreezing = false
        def stateLockBill = false
        def stateLockCart = false
        switch (itemBaseDO?.getState()){
            case 1:
                stateDesc = "已下架"
                stateLockBill = true
                stateLockCart = true
                break
            case 2:
                stateDesc = "卖光了"
                stateLockBill = true
                stateLockCart = true
                break
            case 3:
                stateDesc = "待开售"
                stateLockBill = true
                // 可以加购
                stateLockCart = false
                break
        }

        /**
         * 不能加购，且需要冻结SKU的情况：
         * 1.医美虚拟商品
         * 2.预售
         * 3.虚拟商品
         * 4.快抢
         * 5.秒杀
         */
        boolean isVirtualItem = (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL)
        if (    Tools.isMedicalBeautyItem() ||
                isVirtualItem ||
                activityType == SKUDataVOActivityType.PRESALE ||
                activityType == SKUDataVOActivityType.FASTBUY ||
                activityType == SKUDataVOActivityType.SECKILL
        ) {

            isFreezing = true
            stateLockCart = true
        }

        /**
         * 不冻结SKU，但不能加购的情况：
         * 1.直播特卖渠道
         */
        if (activityType == SKUDataVOActivityType.LIVE) {
            stateLockCart = true
        }

        return new Tuple(stateDesc,stateLockBill,stateLockCart, isFreezing)
    }
}
