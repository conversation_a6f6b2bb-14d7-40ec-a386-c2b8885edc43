package groovy.msd.app.common.sku.translator

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType

/**
 * Created by fufeng on 2017/12/14.
 */
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.common.sku.dataProvider.SKUJDDataProvider
import groovy.msd.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.msd.app.common.sku.vo.SKUExtensionDataInfo
import groovy.msd.app.common.sku.vo.SKUExtensionDataVO
import groovy.msd.app.vo.SKUDataVOActivityType

/**
 * 应用于商品详情页中的SKU
 * 适用于普通商品
 */
@Translator(id = "msdSkuExtensionDetailNormalData", defaultValue = DefaultType.NULL)
class SKUExtensionDetailNormalData implements IFiveDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, BuyershopDO, SKUExtensionDataVO> {
    @Override
    SKUExtensionDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO, BuyershopDO buyershopDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionDataVO()
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUExtensionDataProvider.getActivityType(aItemBaseDO, null, aPreSaleDO, aGroupBuyingDO)
        /**
         * 京东商品无库存提示文案
         */
        vo.oosTips = Tools.isJdItem() ? "抱歉，所选地区内暂时无货" : ""
        /**
         * 配送地址信息
         */
        vo.addressInfo = SKUJDDataProvider.getSkuAddressInfo(aSkuDO)
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 商品售卖状态
         */
        def stateDesc,stateLockBill,stateLockCart,isFreezing
        (stateDesc, stateLockBill, stateLockCart,isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing

        /**
         * 礼包商品不能在客户单下单、加购
         */
        if (buyershopDO?.isGiftItem()) {
            vo.stateLockCart = true
            vo.stateLockBill = true
        }

        return vo
    }
}