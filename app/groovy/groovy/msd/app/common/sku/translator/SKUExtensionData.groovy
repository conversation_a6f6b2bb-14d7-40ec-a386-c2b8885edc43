package groovy.msd.app.common.sku.translator

/**
 * Created by fuf<PERSON> on 2017/12/13.
 */
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.common.sku.dataProvider.SKUJDDataProvider
import groovy.msd.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.msd.app.common.sku.vo.SKUExtensionDataInfo
import groovy.msd.app.common.sku.vo.SKUExtensionDataVO
import groovy.msd.app.vo.SKUDataVOActivityType

/**
 * 应用于单独SKU接口
 * 适用于普通商品/普通非渠道拼团商品
 */
@Translator(id = "skuExtensionData", defaultValue = DefaultType.NULL)
class SKUExtensionData implements ISixDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, BuyershopDO, PinTuanDO, SKUExtensionDataVO> {
    @Override
    SKUExtensionDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO, BuyershopDO buyershopDO, PinTuanDO aPintuanDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionDataVO()

        /**
         * 须解析caller的参数：
         * 如果caller == normal，则返回普通SKU
         * 否则，均返回拼团SKU（caller == pintuan 或者不给为空）
         */
        String caller = DetailContextHolder.get().getParam("caller")
        PinTuanDO realPintuanDO
        SkuDO realSkuDO = aSkuDO
        if ("normal".equals(caller)) {
            realPintuanDO = null
        } else if ("pintuan".equals(caller)) {
            realPintuanDO = aPintuanDO
            realSkuDO = aPintuanDO?.skuInfo
        } else {
            realPintuanDO = aPintuanDO
            realSkuDO = aPintuanDO?.skuInfo
        }
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, realPintuanDO)
        /**
         * 如果不是拼团商品，则取普通sku信息
         */
        realSkuDO = realSkuDO ? realSkuDO : aSkuDO
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(realSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUExtensionDataProvider.getActivityType(aItemBaseDO, aPintuanDO, aPreSaleDO, aGroupBuyingDO)
        /**
         * 京东商品无库存提示文案
         */
        vo.oosTips = Tools.isJdItem() ? "抱歉，所选地区内暂时无货" : ""
        /**
         * 配送地址信息
         */
        vo.addressInfo = SKUJDDataProvider.getSkuAddressInfo(realSkuDO)
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 商品售卖状态
         */
        def stateDesc,stateLockBill,stateLockCart,isFreezing
        (stateDesc, stateLockBill, stateLockCart,isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing

        /**
         * 礼包商品不能在客户单下单、加购
         */
        if (buyershopDO?.isGiftItem()) {
            vo.stateLockCart = true
            vo.stateLockBill = true
        }
        return vo
    }
}