package groovy.msd.app.common.sku.vo

import groovy.msd.app.vo.TextTag

/**
 * Created by fufeng on 2017/12/13.
 */
class SKUExtensionDataVO {
    SKUExtensionDataInfo data
    /**
     * 活动类型：预售、团购、快抢等
     * SKUDataVOActivityType
     * public static final int DEFAULT = 0//默认为普通商品
     * public static final int PRESALE = 1//预售
     * public static final int TUANGOU = 2//团购
     * public static final int FASTBUY = 3//快抢
     * public static final int SECKILL = 4//秒杀
     * public static final int PINTUAN = 5//拼团
     */
    int activityType

    String activityId

    /**
     * 分期免息Tag信息
     */
    TextTag installmentMait
    /**
     * 额外下单参数
     */
    Map orderBillParams

    int skuCommunicationType
    /**
     * 商品是否能加购
     */
    boolean stateLockCart
    /**
     * 商品是否能下单
     */
    boolean stateLockBill
    /**
     * 状态描述
     */
    String stateDesc
    /**
     * 是否冻结sku，虚拟、快抢、秒杀商品需要冻结
     */
    boolean isFreezing
    /**
     * 无库存提示文案
     * 目前只有京东开普勒商品在用
     */
    String oosTips
    /**
     * 配送地址信息
     */
    SKUAddressInfo addressInfo
}
