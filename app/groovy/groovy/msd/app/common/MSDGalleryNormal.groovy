package groovy.msd.app.common

/**
 * Created by wuy<PERSON> on 2018/6/1.
 */
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.CelebrityInfo
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "msdGalleryNormal", defaultValue = DefaultType.NULL)
class MSDGalleryNormal implements ITwoDependTranslator<ItemBaseDO, ExtraDO, Object>{

    static class GalleryNormalVO{
        String[] topImages
        CelebrityInfoVO celebrity
        VideoInfoVO video
        /**
         * 3D模型入口
         */
        ThreeDInfoVO threeDInfo
    }
    static class CelebrityInfoVO{
        String avatarImg
        String certTagImg
        String certTagName
        String profileUrl
        String uId
        String userName

        CelebrityInfoVO(CelebrityInfo info) {
            if (!info){
                return
            }
            this.avatarImg = info.avatarImg
            this.certTagImg = info.certTagImg
            this.certTagName = info.certTagName
            this.profileUrl = info.profileUrl
            this.uId = info.getUId()
            this.userName = info.userName
//            InvokerHelper.setProperties(this, info.properties)
        }
    }
    static class VideoInfoVO{
        String cover
        Long videoId

        Integer width

        Integer height
    }

    static class ThreeDInfoVO {
        /**
         * 需要展示在Gallery里面的图片
         */
        String img
        /**
         * 3D信息入口按钮中的Icon
         * http://s3.mogucdn.com/mlcdn/c45406/170629_7l6be2kl0ibe4ffi79ffid49253lh_22x22.png
         */
        String icon
        /**
         * 点击跳转到的实际3D展示页面
         */
        String url
        /**
         * 跳转按钮的文案
         * 点击查看3D
         */
        String text
    }

    @Override
    Object translate(ItemBaseDO input1, ExtraDO input2) {
        if (!input1?.topImages) {
            // 业务上可以返回NULL
            return null
        }
        def ret = new GalleryNormalVO(
                topImages: input1.topImages,
                celebrity: input2?.celebrityInfo ? new CelebrityInfoVO(input2?.celebrityInfo) : null,
                video:input1.video?new VideoInfoVO(
                        cover: input1.video?.cover,
                        videoId: input1.video?.videoId,
                        width: input1.video?.width,
                        height: input1.video?.height
                ):null
        )
        if (ret && input1?.threeDModel?.model && input1?.threeDModel?.snapshot) {
            ret.threeDInfo = new ThreeDInfoVO(
                    img: input1.threeDModel.snapshot,
                    url: input1.threeDModel.model,
                    icon: ImageUtil.img("mlcdn/c45406/170629_7l6be2kl0ibe4ffi79ffid49253lh_22x22.png"),
                    text: "点击查看3D"
            )
        }

        return ret
    }
}

