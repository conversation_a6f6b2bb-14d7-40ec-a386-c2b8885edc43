package groovy.msd.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.msd.app.vo.MSDItemServiceBasicVO
import groovy.msd.app.vo.MSDTagItemVO

/**
 * Created by fufeng on 2017/3/28.
 */
@Translator(id = "msdItemServiceNormal")
class MSDItemServiceNormal implements ITwoDependTranslator<ItemBaseDO, ShopDO, MSDItemServiceBasicVO> {

    @Override
    MSDItemServiceBasicVO translate(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        if (DetailContextHolder.get().isDyn()) {
            //业务上可以返回NULL
            return null
        }
        MSDItemServiceBasicVO vo = new MSDItemServiceBasicVO()
        MSDTagItemVO itm1 = new MSDTagItemVO()
        itm1.icon = ImageUtil.img("/mlcdn/c45406/180604_66fc33080hc5185cge3bg747je9d1_36x36.png")
        itm1.name = "买手精选"
        MSDTagItemVO itm2 = new MSDTagItemVO()
        itm2.icon = ImageUtil.img("/mlcdn/c45406/180604_66fc33080hc5185cge3bg747je9d1_36x36.png")
        itm2.name = "品质保证"
        MSDTagItemVO itm3 = new MSDTagItemVO()
        itm3.icon = ImageUtil.img("/mlcdn/c45406/180604_66fc33080hc5185cge3bg747je9d1_36x36.png")
        itm3.name = "售后放心"
        vo.list = [itm1, itm2, itm3]

        return vo
    }
}