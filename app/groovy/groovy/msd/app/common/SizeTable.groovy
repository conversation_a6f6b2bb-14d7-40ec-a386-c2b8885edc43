package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.msd.app.common.provider.SizeTableProvider

/**
 * Created by fufeng on 2017/5/22.
 */

@Translator(id = "msdSizeTable", defaultValue = DefaultType.NULL)
class SizeTable implements IOneDependTranslator<ItemParamsDO, Object> {
   @Override
    groovy.msd.app.vo.ItemParams translate(ItemParamsDO itemParamsDO) {
       if (itemParamsDO == null) {
           // 业务上可以返回NULL
           return null
       }
       def sizeTable = SizeTableProvider.getSizeTable(itemParamsDO)
       return sizeTable
   }
}