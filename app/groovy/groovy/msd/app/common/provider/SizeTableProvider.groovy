package groovy.msd.app.common.provider

import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.msd.app.vo.ItemParams

/**
 * Created by xvxvxxx on 2017/5/22.
 */

class SizeTableProvider {
    static public ItemParams getSizeTable(ItemParamsDO itemParamsDO) {
        if (itemParamsDO == null) {
            return null
        }
        def itemParam
        if (itemParamsDO != null) {
            itemParam = new ItemParams()
            List<List<String>> table = null;
            if (itemParamsDO.rule?.tables?.size() > 0) {
                table = itemParamsDO.rule?.tables?.get(0)
            }
            if (table != null && table.size() > 1 && table.get(0) != null && table.get(0).size() >= 2) {
                itemParam.footerTip = "* 以上由商家提供，仅供参考"
                itemParam.heads = table[0]
                List<List<String>> values = new ArrayList<>()
                List<String> unit = new ArrayList<>();
                unit.add("");
                for (int i = 1; i < itemParam.heads.size(); i++) {
                    if (i < 2) {
                        unit.add("")
                    } else {
                        unit.add("cm")
                    }
                }
                for (int i = 1; i < table.size(); i++) {
                    values.add(table.get(i))
                }
                itemParam.unit = unit
                itemParam.paramVals = values
            }
        }
        return itemParam
    }
}

