package groovy.msd.app.common.anchor

/**
 * Created by fufeng on 2017/3/29.
 * 客户端932版本开始使用
 * 模板 >=1.0.3
 */


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "msdTopBar", defaultValue = DefaultType.NULL)
class MSDTopBarNormal implements IOneDependTranslator<ItemBaseDO,Object>{

    static class TopBarNormalV2VO{
        String iid
        String reportUrl
        int state
        boolean canEdit
        List<String> anchors
    }

    @Override
    Object translate(ItemBaseDO input1) {
        if (!input1){
            //业务上可以return null
            return null
        }
        def vo = new TopBarNormalV2VO(
                iid: input1?.iid,
                reportUrl: "http://securityreport.mogujie.com/h5/complainreport?appKey=A506FAED68D921D8&modelId=11&reportId=${input1?.iid}&login=1",
                state: input1?.state,
                canEdit: false,
                anchors: ["商品"]
        )
        String[] tmpAnchors = AnchorDataProvider.getAnchorDataList().collect {
            return it.text
        }
        vo.anchors.addAll(tmpAnchors)
        return vo
    }
}