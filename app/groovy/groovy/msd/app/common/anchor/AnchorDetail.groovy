package groovy.msd.app.common.anchor

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import groovy.msd.app.vo.AnchorDataVO

/**
 * Created by fufeng on 2017/3/29.
 */
@Translator(id = "msdAnchorDetail")
class AnchorDetail implements IZeroDependTranslator<AnchorDataVO> {
    @Override
    AnchorDataVO translate() {
        int index = 1;
        return AnchorDataProvider.getAnchorDataAtIndex(index)
    }
}