package groovy.msd.app.common.anchor

import com.mogujie.detail.core.util.ImageUtil
import groovy.msd.app.vo.AnchorDataVO

/**
 * Created by fufeng on 2017/3/29.
 */

class AnchorDataProvider {
    static AnchorDataVO[] getAnchorDataList() {
        def list = [
                new AnchorDataVO(image: ImageUtil.img("/mlcdn/e5265e/170406_05b5880klf31gic5jc4h91517f24c_36x36.png"), text: "评价"),
                new AnchorDataVO(image: ImageUtil.img("/mlcdn/e5265e/170406_331hl0e8dg20ai33l98kjd72e4d45_36x36.png"), text: "详情"),
        ]
        return list
    }

    static AnchorDataVO getAnchorDataAtIndex(int index) {
        if (index < getAnchorDataList().size()) {
            return (getAnchorDataList())[index]
        }
        return null
    }
}