package groovy.msd.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.msd.app.common.provider.SizeTableProvider
import groovy.msd.app.vo.ItemParamsVO
import groovy.msd.app.vo.ProductInfoVO

/**
 * Created by fufeng on 2017/5/22.
 */
@Translator(id = "msdItemParamsApp", defaultValue = DefaultType.NULL)
class ItemParamsApp implements IOneDependTranslator<ItemParamsDO, Object> {
    static class ItemParamsVOV2 {
        String title = "尺码表 & 商品参数"
        ItemParamsDetailVOV2 itemParams
    }

    static class ItemParamsDetailVOV2 {
        ProductInfoVO info
        ItemParamsRuleV2 rule
    }

    static class ItemParamsRuleV2 {
        /**
         * 尺码说明的描述
         */
        String desc;
        /**
         * 尺码说明的表格
         */
        groovy.msd.app.vo.ItemParams sizeTable;
        /**
         * 图片
         */
        List<String> images;
    }

    @Override
    Object translate(ItemParamsDO itemParamsDO) {
        if (!itemParamsDO) {
            // 业务上可以返回NULL
            return null
        }
        def itemParamsTranslator = new ItemParams()
        ItemParamsVO itemParamsOriginalVO = itemParamsTranslator.translate(itemParamsDO)
        if (itemParamsOriginalVO == null) {
            // 业务上可以返回NULL
            return null
        }

        ItemParamsVOV2 itemParamsVOV2 = new ItemParamsVOV2()
        itemParamsVOV2.itemParams = new ItemParamsDetailVOV2()
        itemParamsVOV2.itemParams.info = itemParamsOriginalVO?.itemParams?.info

        if (itemParamsOriginalVO?.itemParams?.rule != null) {
            def newRule = new ItemParamsRuleV2()
            newRule.desc = itemParamsOriginalVO.itemParams.rule.desc
            newRule.images = itemParamsOriginalVO.itemParams.rule.images
            newRule.sizeTable = SizeTableProvider.getSizeTable(itemParamsDO)
            itemParamsVOV2.itemParams.rule = newRule
        }
        return itemParamsVOV2
    }
}