package groovy.msd.app.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.vo.ItemPriceVO
import groovy.msd.app.vo.ShareVO
import org.apache.commons.collections4.CollectionUtils

@Translator(id = "share", defaultValue = DefaultType.NULL)
class Share implements IThreeDependTranslator<ItemBaseDO, ShopDO, ActivityDO, Object> {
    @Override
    ShareVO translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3) {
        if (!input1 || !input2){
            //业务上可以return null
            return null
        }
        def res = MaitUtil.getMaitData(11754)
        def shareText = res?.get(0)?.get('sharetext') as String ?:'亲爱的，这件宝贝不错，进来看看有机会领现金券哦～ '

        def priceVO = new ItemPriceVO()
        priceVO.highNowPrice = input1.highNowPrice
        priceVO.lowNowPrice = input1.lowNowPrice
        priceVO.highPrice = input1.highPrice
        priceVO.lowPrice = input1.lowPrice
//        InvokerHelper.setProperties(priceVO, input1.properties)
        priceVO.updatePrices()
        def itemId = DetailContextHolder.get().getItemDO().getItemId()

        def shareVO = new ShareVO(
                itemTitle: input1.title,
                shareText: shareText,
                url: Tools.getH5Url(itemId),
                imUrl: (input1?.iid && input1?.shopId) ? "mgjim://share?iid=${input1.iid}&shopId=${input1.shopId}&type=1" : null,
                itemDesc: input1.desc,
                shopLogo: input2.shopLogo,
                shopId: input1.shopId,
                price: priceVO.price,
                eventPrice: input3?.warmUpPrice?.price,
                ownerName: input2.name,
                iid: input1.iid,
                miniProgramPath: input1?.iid ? "pages/detail/pages/normal/index?itemId=${input1.iid}" : null
        )
        //IM那边需求传给IM的短链中加上一下参数
        shareVO.imUrl = getUpdatedIMUrl(shareVO.imUrl, input1, shareVO.url)

        return shareVO
    }

    static String getUpdatedIMUrl (String originalUrl, ItemBaseDO itemBaseDO, String h5Url) {
        String ret = originalUrl
        if (ret) {
            if (itemBaseDO.lowNowPrice) {
                //这里不用传价格区间了，确定传最低价就OK
                ret = ret + "&price=${itemBaseDO.lowNowPrice}"
            }
            if (itemBaseDO.title) {
                def encodedTitle = URLEncoder.encode(itemBaseDO.title)
                ret = ret + "&title=${encodedTitle}"
            }
            if (CollectionUtils.isNotEmpty(itemBaseDO.topImages) && itemBaseDO.topImages?.first()) {
                def encodedImageUrl = URLEncoder.encode(itemBaseDO.topImages?.first())
                ret = ret + "&imgUrl=${encodedImageUrl}"
            }
            if (h5Url) {
                def encodedLinkURL = URLEncoder.encode(h5Url)
                ret = ret + "&linkUrl=${encodedLinkURL}"
            }
        }
        return ret
    }
}