package groovy.msd.app.normal.bottombar

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO

/**
 * Created by wuyi on 2018/6/1.
 */
@Translator(id = "msdBuyerBottomBar", defaultValue = DefaultType.EMPTY_MAP)
class MSDBuyerBottombar implements IThreeDependTranslator<ItemBaseDO, BuyershopDO,NormalCountdownDO ,Object> {

    @Override
    Object translate(ItemBaseDO itemBaseDO, BuyershopDO buyershopDO, NormalCountdownDO normalCountdownDO) {
        if (buyershopDO?.seller || buyershopDO?.isGiftItem()) {
            return new Object()
        }

        BottomBarVO vo = new BottomBarVO(
                imUrl: (itemBaseDO?.shopId && itemBaseDO?.iid && itemBaseDO?.userId) ? "mlb://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&login=1" : null,
                iid: itemBaseDO.iid,
                buyButtonTitle: "立即购买"
        )
        vo.buyButtonDisable = itemBaseDO?.saleType != 0 || itemBaseDO?.state != 0
        vo.addCartButtonDisable = itemBaseDO?.saleType != 0 || itemBaseDO?.state != 0
        if (buyershopDO?.isGiftItem()) {
            vo.buyButtonDisable = true
            vo.addCartButtonDisable = true
            vo.buyButtonTitle = "已下架"
        }

        CountdownInfo xskq = normalCountdownDO?.getCountdownInfoMap()?.get("msdFlashSale")
        if(xskq != null) {
            vo.fastbuyStartTime = xskq.startTime
            float lastTime = (xskq.startTime - System.currentTimeSeconds())/60/60
            if(lastTime < 48 && lastTime > 0) {
                vo.showSetRemind = true
                vo.addCartButtonDisable = true
            } else {
                vo.showSetRemind = false
            }
        }
        vo.title = itemBaseDO?.title
        vo.hasSetRemind = buyershopDO.hasSetRemind
        return vo
    }

    class BottomBarVO {
        String imUrl
        String iid
        String buyButtonTitle
        boolean addCartButtonDisable
        boolean buyButtonDisable
        //是否显示【设置提示】
        Boolean showSetRemind
        //是否设置过提示
        Boolean hasSetRemind
        //快抢开始时间
        String fastbuyStartTime
        //商品标题
        String title
    }
}
