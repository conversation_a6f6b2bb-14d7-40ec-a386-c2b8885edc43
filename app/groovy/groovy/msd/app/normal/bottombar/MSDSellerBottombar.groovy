package groovy.msd.app.normal.bottombar

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import org.apache.http.util.TextUtils

/**
 * Created by wuyi on 2018/6/1.
 */
@Translator(id = "msdSellerBottomBar", defaultValue = DefaultType.EMPTY_MAP)
class MSDSellerBottombar implements IThreeDependTranslator<ItemBaseDO, BuyershopDO,NormalCountdownDO, Object> {

    @Override
    Object translate(ItemBaseDO itemBaseDO, BuyershopDO buyershopDO, NormalCountdownDO normalCountdownDO) {
        // 不是买手不展示这个bottombar
        if (!buyershopDO?.seller || buyershopDO?.isGiftItem()) {
            return new Object()
        }


        BottomBarVO vo = new BottomBarVO(
                imUrl: (itemBaseDO?.shopId && itemBaseDO?.iid && itemBaseDO?.userId) ? "mlb://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&login=1" : null,
                iid: itemBaseDO?.iid,
                buyButtonTitle: "买",
                saleButtonTitle: "卖",
        )
        if (buyershopDO?.minCommission > 0) {
            if (buyershopDO?.lowVipUserPrice > 0 && buyershopDO?.highVipUserPrice > 0) {
                String tmpPriceDesc = ""
                if (buyershopDO.lowNormalUserPrice != buyershopDO.highNormalUserPrice) {
                    tmpPriceDesc = "¥" + NumUtil.formatNum((buyershopDO.lowNormalUserPrice - buyershopDO.lowVipUserPrice) / 100D) + "~¥" + NumUtil.formatNum((buyershopDO.highNormalUserPrice - buyershopDO.highVipUserPrice) / 100D)
                }
                else {
                    tmpPriceDesc = "¥" + NumUtil.formatNum((buyershopDO.lowNormalUserPrice - buyershopDO.lowVipUserPrice) / 100D)
                }


                vo.saleButtonSubTitle = "赚" + tmpPriceDesc
                if (buyershopDO.isVipUser) {
                    vo.buyButtonSubTitle = "为你省" + tmpPriceDesc
                } else {
                    vo.buyButtonSubTitle = "会员可省" + tmpPriceDesc
                }
            }
        }
        else {
            vo.buyButtonSubTitle = ""
            vo.saleButtonSubTitle = ""
        }
        vo.saleButtonDisable = itemBaseDO?.saleType != 0 || itemBaseDO?.state != 0
        vo.buyButtonDisable = itemBaseDO?.saleType != 0 || itemBaseDO?.state != 0
        vo.addCartButtonDisable = itemBaseDO?.saleType != 0 || itemBaseDO?.state != 0
        vo.isCollected = buyershopDO?.hasRecommendThisItem
        if (buyershopDO?.isGiftItem()) {
            vo.saleButtonDisable = true
            vo.buyButtonDisable = true
            vo.addCartButtonDisable = true
            vo.buyButtonTitle = "已下架"
            vo.saleButtonTitle = "已下架"
            vo.buyButtonSubTitle = ""
            vo.saleButtonSubTitle = ""
        }

        CountdownInfo xskq = normalCountdownDO?.getCountdownInfoMap()?.get("msdFlashSale")
        if(xskq != null) {
            vo.fastbuyStartTime = xskq.startTime;
            float lastTime = (xskq.startTime - System.currentTimeSeconds())/60/60
            if(lastTime < 48 && lastTime > 0) {
                vo.showSetRemind = true
                vo.addCartButtonDisable = true
            } else {
                vo.showSetRemind = false
            }
        }
        vo.title = itemBaseDO?.title
        vo.hasSetRemind = buyershopDO.hasSetRemind
        return vo
    }

    private String formatPrice(int pennyPrice) {
        return String.format("￥%.2f", ((double) pennyPrice) / 100)
    }

    class BottomBarVO {
        String imUrl
        String iid
        String buyButtonTitle
        String buyButtonSubTitle
        String saleButtonTitle
        String saleButtonSubTitle
        boolean addCartButtonDisable
        boolean buyButtonDisable
        boolean saleButtonDisable
        boolean isCollected
        //是否显示【设置提示】
        Boolean showSetRemind
        //是否设置过提示
        Boolean hasSetRemind
        //快抢开始时间
        Long fastbuyStartTime
        //商品标题
        String title
    }
}
