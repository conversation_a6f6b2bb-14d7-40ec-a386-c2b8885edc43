package groovy.msd.app.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.vo.ShareVO
import org.apache.commons.collections4.CollectionUtils

/**
 * Created by fufeng on 2017/8/24.
 */
@Translator(id = "shareV2")
class ShareV2 implements IFourDependTranslator<ItemBaseDO, ShopDO, ActivityDO, PinTuanDO, Object> {
    @Override
    ShareVO translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, PinTuanDO pintuanDO) {
        def superTranslator = new Share()
        def ret = superTranslator.translate(input1, input2, input3)
        if (!ret) {
            return ret
        }

        ret.imUrl = getIMUrl(input1, ret.url, pintuanDO)

        if (Tools.isPintuan(input1, pintuanDO)) {
            //拼团商品
            //price 现价
            if (pintuanDO?.skuInfo?.lowNowPrice?.toFloat() < input1?.lowNowPrice?.toFloat()) {
                if (pintuanDO.skuInfo.lowNowPrice != pintuanDO.skuInfo.highNowPrice){
                    ret.price = "¥" + pintuanDO.skuInfo.lowNowPrice + "~¥" + pintuanDO.skuInfo.highNowPrice
                }else{
                    ret.price = "¥" + pintuanDO.skuInfo.lowNowPrice
                }
            }

            return ret
        }
        else {
            //普通商品
            return ret
        }
    }

    static String getIMUrl (ItemBaseDO itemBaseDO, String h5Url, PinTuanDO pintuanDO) {
        String ret = "mgjim://share?iid=${itemBaseDO.iid}&shopId=${itemBaseDO.shopId}&type=1"

        String lowPrice = itemBaseDO?.lowNowPrice
        if (Tools.isPintuan(itemBaseDO, pintuanDO)) {
            lowPrice = pintuanDO?.skuInfo?.lowNowPrice
        }
        if (lowPrice) {
            //这里不用传价格区间了，确定传最低价就OK
            ret = ret + "&price=${lowPrice}"
        }
        if (itemBaseDO.title) {
            def encodedTitle = URLEncoder.encode(itemBaseDO.title)
            ret = ret + "&title=${encodedTitle}"
        }
        if (CollectionUtils.isNotEmpty(itemBaseDO.topImages) && itemBaseDO.topImages?.first()) {
            def encodedImageUrl = URLEncoder.encode(itemBaseDO.topImages?.first())
            ret = ret + "&imgUrl=${encodedImageUrl}"
        }
        if (h5Url) {
            def encodedLinkURL = URLEncoder.encode(h5Url)
            ret = ret + "&linkUrl=${encodedLinkURL}"
        }
        ret = ret.replaceAll("\\+", "%20")
        return ret
    }
}
