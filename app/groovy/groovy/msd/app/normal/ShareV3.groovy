package groovy.msd.app.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.app.vo.ShareVO
import org.apache.http.util.TextUtils

import java.text.SimpleDateFormat

/**
 * Created by fufeng on 2017/12/8.
 */
@Translator(id = "msdShare", defaultValue = DefaultType.NULL)
class ShareV3 implements ISixDependTranslator<ItemBaseDO, ShopDO, ActivityDO, PinTuanDO, BuyershopDO, NormalCountdownDO, Object> {
    @Override
    ShareVO translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, PinTuanDO pintuanDO, BuyershopDO buyershopDO, NormalCountdownDO normalCountdownDO) {
        ShareV2 superTranslator = new ShareV2()
        def ret = superTranslator.translate(input1, input2, input3, pintuanDO)
        if (!ret) {
            return ret
        }
        if (buyershopDO?.lowNormalUserPrice > buyershopDO?.lowVipUserPrice && buyershopDO?.highNormalUserPrice > buyershopDO?.highVipUserPrice && buyershopDO?.lowVipUserPrice > 0 && buyershopDO?.highVipUserPrice > 0) {
            if (buyershopDO.lowNormalUserPrice != buyershopDO.highNormalUserPrice) {
                // 买手店现在不要区间价
//                ret.memberPriceDesc = "会员更省" + NumUtil.formatPriceDrawer(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue()) + "~" + NumUtil.formatPriceDrawer(buyershopDO.highNormalUserPrice.intValue() - buyershopDO.highVipUserPrice.intValue()) + "元"
                ret.memberPriceDesc = "会员更省" + NumUtil.formatPriceDrawer(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue()) + "元"
            } else {
                ret.memberPriceDesc = "会员更省" + NumUtil.formatPriceDrawer(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue()) + "元"
            }
        }
        //拼团打点需要的businessId
        if (pintuanDO?.activityId) {
            ret.url = ret.url + "&businessId=${pintuanDO.activityId}"
            ret.businessId = pintuanDO.activityId
            ret.miniProgramPath = ret.miniProgramPath + "&businessId=${pintuanDO.activityId}"
        } else {
            ret.businessId = ""
        }

        if (buyershopDO?.seller && buyershopDO?.minCommission > 0) {
            if (buyershopDO?.lowVipUserPrice > 0 && buyershopDO?.highVipUserPrice > 0) {
                ret.sharePopWindowTitle = buildCommission(buyershopDO.lowNormalUserPrice.intValue() - buyershopDO.lowVipUserPrice.intValue(), buyershopDO.highNormalUserPrice.intValue() - buyershopDO.highVipUserPrice.intValue())
            }
        } else {
            ret.sharePopWindowTitle = ""
        }
        def maitData = MaitUtil.getMaitData(117177)
        if (maitData?.size() > 0) {
            ret.sharePopWindowSubTitle = maitData?.get(0)?.get("content")
        } else {
            ret.sharePopWindowSubTitle = ""
        }
        def lowNowPrice = input1.lowNowPrice
        def lowPrice = input1.lowPrice
        if (Tools.isPintuan(input1, pintuanDO)) {
            //price 现价
            lowNowPrice = pintuanDO.skuInfo.lowNowPrice
        }
        ret.price = "¥" + lowNowPrice
        if (!lowPrice.equals(lowNowPrice)) {
            ret.oldPrice = "¥" + lowPrice
        } else {
            ret.oldPrice = ""
        }

        // 美丽买手不要分享到小程序，私聊
        ret.miniProgramPath = ""
        ret.imUrl = ""
        def sid
        if (buyershopDO?.isSeller()) {
            sid = IdConvertor.idToUrl(DetailContextHolder.get().getLoginUserId() ?: 0L)
        } else {
            sid = IdConvertor.idToUrl(buyershopDO?.shareUserInfo?.userId ?: 0L)
        }
        // 域名替换
        ret.url = "http://h5.meilimaishou.com/mgj-detail/msd.html?itemId=" + input1.iid + "&shareId=" + sid

        CountdownInfo xskq = normalCountdownDO?.getCountdownInfoMap()?.get("msdFlashSale")
        // 限时快抢
        if (xskq && xskq.warmUpTime <= System.currentTimeSeconds() && xskq.startTime > System.currentTimeSeconds() && xskq.state == CountdownState.WARM_UP) {
            // 预热期
            def xskqMait = MaitUtil.getMaitData(xskq.maitId)?.get(0)
            ret.eventDesc = xskqMait?.get("prePriceDesc")
            ret.eventTitle = formatTime(xskq.startTime) + "开始"
            ret.inviteCode = ""
        } else if (xskq && xskq.startTime <= System.currentTimeSeconds() && xskq.endTime > System.currentTimeSeconds() && xskq.state == CountdownState.IN_ACTIVITY) {
            // 正式期
            def xskqMait = MaitUtil.getMaitData(xskq.maitId)?.get(0)
            ret.eventDesc = xskqMait?.get("priceDesc")
            ret.eventTitle = formatTime(xskq.endTime) + "结束"
            ret.eventIcon = ""
        } else {
            ret.eventDesc = ""
            ret.eventTitle = ""
            def shareMait = MaitUtil.getMaitData(119995)?.get(0)
            ret.eventIcon = shareMait?.get("eventIcon")
            ret.eventIcon = ret.eventIcon == null ? "" : ret.eventIcon
        }
        if (buyershopDO?.seller && !TextUtils.isEmpty(buyershopDO?.currentUserInfo?.inviteCode)) {
            ret.inviteCode = "邀请码：" + buyershopDO?.currentUserInfo?.inviteCode
            ret.ownerName = buyershopDO?.currentUserInfo?.name
            ret.shopLogo = buyershopDO?.currentUserInfo?.avatar
        } else {
            ret.inviteCode = ""
            ret.ownerName = ""
            ret.shopLogo = ""
        }

        // 展示原价，不要展示会员价
        // 买手店现在不要区间价
        if (buyershopDO?.isVipUser() && buyershopDO?.lowNormalUserPrice != null && buyershopDO?.lowNormalUserPrice > 0) {
            ret.price = "¥" + NumUtil.formatNum(buyershopDO?.lowNormalUserPrice / 100D)
        } else {
            ret.price = "¥" + input1?.lowNowPrice
        }

        return ret
    }

    private String formatTime(long second) {
        Date date = new Date(((long) second) * 1000)
        SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日HH点")
        return formatter.format(date)
    }

    private String buildCommission(int minCommission, int maxCommission) {
        if (maxCommission > minCommission) {
            return "分享赚" + formatPrice(minCommission) + "~" + formatPrice(maxCommission)
        } else {
            return "分享赚" + formatPrice(minCommission)
        }
    }

    private String formatPrice(int pennyPrice) {
        return String.format("￥%.2f", ((double) pennyPrice) / 100)
    }


    def calculatePrices(String lowPrice, String lowNowPrice, String highPrice, String highNowPrice) {
        String oldPrice
        String price

        //lowPrice
        if (lowPrice == lowNowPrice && highPrice == highNowPrice) {
            oldPrice = null
        } else {
            oldPrice = "¥" + lowPrice
            if (lowPrice != highPrice) {
                String osV = DetailContextHolder.get().getOsVersion();
                oldPrice = oldPrice + "~¥" + highPrice
            }
        }

        //price
        if (lowNowPrice != highNowPrice) {
            price = "¥" + lowNowPrice + "~¥" + highNowPrice
        } else {
            price = "¥" + lowNowPrice
        }

        return new Tuple2(oldPrice, price)
    }
}
