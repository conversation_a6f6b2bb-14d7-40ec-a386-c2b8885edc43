package groovy.msd.h5.base

import com.mogujie.detail.core.util.MetabaseTool

/**
 * Created by changsheng on 13/09/2017.
 */

class Util  {

    static void setEsiDataForPrice (ItemPriceVO itemPrice) {
        itemPrice.with {
            if (nowPrice == null) {
                nowPrice = ""
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (highNowPrice == null) {
                highNowPrice = ""
            }
            if (priceTags == null) {
                priceTags = []
            }
            if (prePriceTag == null) {
                prePriceTag = new PriceTagVO()
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (eventPriceDesc == null) {
                eventPriceDesc = new PriceTagVO()
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (mobilePrice == null) {
                mobilePrice = ""
            }
            if (mobileDownloadLink == null) {
                mobileDownloadLink = ""
            }
            if (extraDesc == null) {
                extraDesc = ""
            }
            if (eventDesc == null) {
                eventDesc = ""
            }
        }
    }

    static Boolean getSwitchConfig(String configName) {
        return "true".equalsIgnoreCase(MetabaseTool.getValue(configName))
    }

}