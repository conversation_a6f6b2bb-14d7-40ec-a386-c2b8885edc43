package groovy.msd.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator

import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO





/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-价格
 */
@Translator(id = "normalCountdownInfo")
class NormalCountdownInfo implements IOneDependTranslator<NormalCountdownDO, NormalCountdownDO>{
    @Override
    NormalCountdownDO translate(NormalCountdownDO normalCountdown) {
        if(!normalCountdown) {
            return null
        }
        return normalCountdown
    }
}