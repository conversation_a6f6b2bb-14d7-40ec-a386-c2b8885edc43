package groovy.msd.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.msd.h5.base.ItemPriceVO
import groovy.msd.h5.base.Util

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-价格
 */
@Translator(id = "priceInfo")
class PriceInfo implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        if (!itemBase) {
            return null
        }

        ItemPriceVO priceInfo = new ItemPriceVO(itemBase)

        priceInfo.with {
            setNowPriceByRange(itemBase)
            setOldPriceByRange(itemBase)
        }

        Util.setEsiDataForPrice(priceInfo)

        return priceInfo
    }
}
