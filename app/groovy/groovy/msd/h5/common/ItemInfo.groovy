package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.VideoInfo
import com.mogujie.detail.module.seo.domain.SeoDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.msd.h5.base.Util

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-商品信息
 */

@Translator(id = "itemInfo")
class ItemInfo implements IThreeDependTranslator<ItemBaseDO, ExtraDO, SeoDO, ItemInfoVO> {

    static class ItemInfoVO {
        String desc;
        String title;
        String itemId;
        Boolean isFaved;
        // 是否显示分享赚积分
        Boolean isShareIntegral;
        Integer cFav;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer state;
        /**
         * 开售时间，待开售商品
         */
        Long saleStartTime;
        /**
         * 商品类型，0为普通商品，1为预售商品
         */
        Integer saleType;

        Map<String, String> seo;

        Integer type

        VideoInfo video

        Integer priceChannel

        Integer virtualItemType

        // 是否为医美商品
        Boolean isMedicalBeautyItem

        Boolean isJdItem

        // 是否显示开通"白付美"引导条
        Boolean canApplyInstallment

        // 蘑豆折扣
        String modouDiscount

        /**
         * 暂时加上，之后去掉
         */
        String lowPrice;
        String highPrice;
        String lowNowPrice;
        String highNowPrice;

        ItemInfoVO(ItemBaseDO item, ExtraDO extra, SeoDO seo) {
            this.desc = item?.desc;
            this.title = item?.title;
            this.itemId = item?.iid;
            this.isFaved = item?.isFaved;
            this.isShareIntegral = Util.getSwitchConfig("shareIntegralSwitch");
            this.cFav = item?.cFav;
            this.state = item?.state;
            this.saleType = item?.saleType;
            this.saleStartTime = extra?.onSaleTime;
            this.seo = seo;
            this.type = item?.type;
            this.video = item?.video
            this.priceChannel = item?.priceChannel ?: 0
            this.virtualItemType = item?.virtualItemType?.getCode()
            this.canApplyInstallment = item?.canApplyInstallment
            this.modouDiscount = extra?.modouDiscount;

            this.lowPrice = item?.lowPrice;
            this.highPrice = item?.highPrice;
            this.lowNowPrice = item?.lowNowPrice;
            this.highNowPrice = item?.highNowPrice;

            this.isMedicalBeautyItem = Tools.isMedicalBeautyItem();
            this.isJdItem = Tools.isJdItem()
        }
    }

    @Override
    ItemInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, SeoDO seo) {
        if (!itemBase) {
            return null;
        }
        ItemInfoVO itemInfo = new ItemInfoVO(itemBase, extra, seo);
        return itemInfo;
    }
}