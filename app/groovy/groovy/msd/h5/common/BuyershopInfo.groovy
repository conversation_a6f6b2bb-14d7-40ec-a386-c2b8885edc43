package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.buyershop.domain.BuyershopDO
import com.mogujie.detail.module.buyershop.domain.ShareUserInfo

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "buyershopInfo", defaultValue = DefaultType.EMPTY_MAP)
class BuyershopInfo extends BuyershopDO implements IOneDependTranslator<BuyershopDO, BuyershopDO>{

    @Override
    BuyershopDO translate(BuyershopDO buyershop) {
        if (!buyershop) {
            return null
        }

        if (!buyershop.shareUserInfo) {
            buyershop.shareUserInfo = new ShareUserInfo()
        }

        return buyershop
    }

}
