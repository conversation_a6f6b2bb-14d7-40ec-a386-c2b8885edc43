package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-登录信息
 */

@Translator(id = "userInfo")
class UserInfo implements ITwoDependTranslator<ItemBaseDO, ShopDO, UserInfoVO> {

    static class UserInfoVO {
        String userId;
        Boolean isLogin;
        String loginUserId;
        Boolean isSelf;
        Boolean admin;
        String shopId;
        String sellerId;
        String loginUserAvatar;
        String loginUserNickname
    }

    @Override
    UserInfoVO translate(ItemBaseDO itemBase, ShopDO shop) {
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.with {
            isLogin = itemBase?.loginUserId ? true : false;
            loginUserId = itemBase?.loginUserId;
            isSelf = itemBase?.isSelf;
            userId = itemBase?.userId;
            shopId = itemBase?.shopId;
            admin = itemBase?.admin;
            sellerId = shop?.userId;
            loginUserAvatar = itemBase?.loginUserAvatar;
            loginUserNickname = itemBase?.loginUserNickname;
        }
        return userInfo
    }
}