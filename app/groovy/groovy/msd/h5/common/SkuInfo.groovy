package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.msd.h5.base.SkuInfoVO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "skuInfo")
public class SkuInfo implements ITwoDependTranslator<ItemBaseDO, SkuDO, SkuInfoVO> {

    @Override
    SkuInfoVO translate(ItemBaseDO itemBase, SkuDO sku) {
        SkuInfoVO skuInfo = new SkuInfoVO(sku)
        return skuInfo
    }
}