package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO
import com.mogujie.service.tangram.domain.entity.DetailModule

/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-图文详情
 */

@Translator(id = "detailInfo")
class DetailInfo implements IOneDependTranslator<DetailDO, DetailInfoVO> {

    static class DetailInfoVO {
        String desc;
        List<DetailModule> detailImage;
        ShopDecorateVO shopDecorate;
    }

    static class ShopDecorateVO {
        private String img
        private String link
    }

    @Override
    DetailInfoVO translate(DetailDO input1) {
        if (!input1) {
            return null;
        }

        def shopDecorate = null
        String link = input1?.shopDecorate?.link ?: ''

        // 不是h5类型的链接，都过滤掉（比如app短链）
        if (link && link.indexOf('http') == 0) {
            shopDecorate = new ShopDecorateVO(
                img: input1?.shopDecorate?.img,
                link: link
            )
        }

        return new DetailInfoVO(
                desc: input1?.desc,
                detailImage: input1?.detailImage,
                shopDecorate: shopDecorate
        );
    }
}