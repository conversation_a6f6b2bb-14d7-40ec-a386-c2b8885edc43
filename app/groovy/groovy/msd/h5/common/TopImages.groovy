package groovy.msd.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-顶部轮播图
 */

@Translator(id = "topImages")
public class TopImages implements IOneDependTranslator<ItemBaseDO, List<String>> {

    @Override
    List<String> translate(ItemBaseDO input1) {
        if (!input1) {
            return null;
        }
        return input1?.topImages;
    }
}