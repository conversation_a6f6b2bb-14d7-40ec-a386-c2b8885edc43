package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by pananping on 1/27/21.
 */
@Translator(id = "summarySeckillV2", defaultValue = DefaultType.NULL)
class SummarySeckillV2 implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        if (!input1) {
            return null
        }
        return [ "title": input1.title ]
    }
}