package groovy.mgj.app.seckill

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2018/1/10.
 * 秒杀重定向
 */
@Translator(id = "seckillLazyRedirector", defaultValue = DefaultType.NULL)
class SeckillLazyRedirector implements IOneDependTranslator<ItemBaseDO, RedirectorVO> {
    static class RedirectorVO {
        String toast
        int duration
        String url
        Boolean closeSelf
    }

    @Override
    RedirectorVO translate(ItemBaseDO itemBaseDO) {
        // 返回lazyRedirector客户端也不一定会重定向，需要另一个条件触发；这里一概都返回
        boolean shouldRedirectWhenSeckillEnd = true
        if (shouldRedirectWhenSeckillEnd) {
            return new RedirectorVO(
                    toast: "本场秒杀已结束，秒杀落幕抢购不停，正在为您跳转至新购买地址~",
                    duration: 2,
                    url: Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid)),
                    closeSelf: true
            )
        } else {
            return new RedirectorVO()
        }
    }

}
