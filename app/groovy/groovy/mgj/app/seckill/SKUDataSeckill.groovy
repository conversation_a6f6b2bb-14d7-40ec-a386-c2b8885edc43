package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.SKUDataV3
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUDataVOActivityType

/**
 * Created by fufeng on 2017/6/2.
 */

@Translator(id = "skuDataSeckill")
class SKUDataSeckill implements ISixDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, SeckillDO, Object> {

    private SKUDataV3 skuData;

    public SKUDataSeckill() {
        skuData = new SKUDataV3()
    }

    @Override
    SKUDataVO translate(SkuDO input1, ItemBaseDO input2, PresaleDO input3, GroupbuyingDO input4, SizeHelperDO input5, SeckillDO seckillDO) {

        def result = skuData.translate(input1, input2, input3, input4, input5)

        if (result != null && seckillDO != null) {
            result.activityType =  SKUDataVOActivityType.SECKILL
            result.secKillId = seckillDO.secKillId
        }
        return result
    }
}