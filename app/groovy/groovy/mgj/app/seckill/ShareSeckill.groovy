package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.normal.Share
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/6/5.
 */
@Translator(id = "shareSeckill")
class ShareSeckill implements IFiveDependTranslator<ItemBaseDO, ShopDO, ActivityDO, SeckillDO, ExtraDO, Object> {
    @Override
    Object translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, SeckillDO input4, ExtraDO extraDO) {
        def shareNormal = new Share()
        def result = shareNormal.translate(input1, input2, input3, extraDO)
        if (input4?.secKillId) {
            //秒杀的分享url不一样o
            def url = StrategyUpUtil.upUrl("http://h5.mogujie.com/detail/seckill.html?itemId=${input1?.iid}&activityId=${input4?.secKillId}")
            result?.url = url
            //秒杀不支持小程序
            result?.miniProgramPath = null
            //imUrl不一样
            String imUrlString = "mgjim://share?type=1&bid=${input1?.shopId}&iid=${input1?.iid}&shopId=${input1?.shopId}&activityId=${input4?.secKillId}&fromType=mgj_seckill"
            result?.imUrl = imUrlString
            //如果有秒杀，那么不要返回eventPrice
            result?.eventPrice = null
        }
        result?.imUrl = Share.getUpdatedIMUrl(result?.imUrl, input1, result?.url)
        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        result.mayHideOldPrice(input1)
        return result
    }
}