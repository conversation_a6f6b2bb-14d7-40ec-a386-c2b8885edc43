package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.vo.PriceBannerVOV3
import groovy.mgj.app.vo.PriceTagVO

/**
 * Created by pananping on 1/27/21.
 */
@Translator(id = "priceBannerSeckill", defaultValue = DefaultType.NULL)
class PriceBannerSeckill implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO itemBaseDO) {

        if (!itemBaseDO) {
            return null
        }

        PriceBannerVOV3 ret = new PriceBannerVOV3(
                type: 0,
                priceColor: "#333333"
        )

        ret.highNowPrice = itemBaseDO.highNowPrice
        ret.lowNowPrice = itemBaseDO.lowNowPrice
        ret.highPrice = itemBaseDO.highPrice
        ret.lowPrice = itemBaseDO.lowPrice

        // 1480 版本起，不展示区间价，改为 xx起 的格式
        if (groovy.mgj.app.vo.Tools.getAppVersion() >= 1480) {
            ret.updatePrices(true)
        } else {
            ret.updatePrices(false)
        }

        // 如果是虚拟优惠券商品
        PriceTagVO limitTag = new PriceTagVO(text: "每人限购1件")
        if (Tools.isVirtualCouponItem()) {
            limitTag.text += "，购买后自动发券，不支持退款"
        }
        ret.priceTags = [
                new PriceTagVO(text: "秒杀价"),
                limitTag
        ]

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        ret.mayHideOldPrice(itemBaseDO)

        return ret
    }
}