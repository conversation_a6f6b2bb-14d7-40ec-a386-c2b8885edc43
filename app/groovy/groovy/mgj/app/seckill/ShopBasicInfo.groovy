package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.shop.domain.ShopDO

/**
 * Created by fufeng on 2017/5/24.
 */

@Translator(id = "shopBasicInfo")
class ShopBasicInfo implements IOneDependTranslator<ShopDO, Object> {
    class ShopFullInfoVO{
        Integer level
        String name
        String shopId
        String shopLogo
        String shopUrl
        String tag
    }

    @Override
    Object translate(ShopDO input1) {
        if (!input1?.shopId) {
            return null
        }
        new ShopFullInfoVO(
                level: input1?.level,
                name: input1?.name,
                shopId: input1?.shopId,
                shopLogo: input1?.shopLogo,
                shopUrl: input1?.shopId ? ("mgj://shop?shopId=" + input1?.shopId) : null,
                tag: input1?.tag,
        )
    }
}