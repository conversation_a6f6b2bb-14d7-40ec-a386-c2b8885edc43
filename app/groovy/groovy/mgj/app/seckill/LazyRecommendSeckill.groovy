package groovy.mgj.app.seckill

/**
 * Created by fufeng on 2017/6/6.
 */

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by fufeng on 2017/4/4.
 */
@Translator(id = "lazyRecommendSeckill")
class LazyRecommendSeckill implements IOneDependTranslator<ItemBaseDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO) {
        //ESI请求没有必要返回
        if (DetailContextHolder?.get()?.isDyn()) {
            return null
        }
        if (itemBaseDO == null || itemBaseDO?.iid == null) {
            return null
        }
        Map<String ,String> innerMap = [:]
        innerMap["pid"] = "52389"
        innerMap["iidE"] = itemBaseDO?.iid
        innerMap["pageSize"] = "12"

        def vo = [:]
        vo.type = "MWP"
        vo.info = [:]
        vo.info.api = "mwp.darwin.recommend@4"
        vo.info.param = innerMap
        vo.from = "recommend"
        return vo
    }

}
