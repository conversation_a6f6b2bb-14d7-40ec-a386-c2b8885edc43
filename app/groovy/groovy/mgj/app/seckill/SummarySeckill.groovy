package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.vo.TextStyle
import org.codehaus.groovy.runtime.InvokerHelper

/**
 * Created by fufeng on 2017/5/24.
 */
@Translator(id = "summarySeckill")
class SummarySeckill implements IThreeDependTranslator<ItemBaseDO, SeckillDO, SkuDO, Object> {

    class SummaryVO extends ItemPriceVO {
        String title
        String priceColor
        PriceTagVO priceTag
        PriceTagVO eventPriceTag
        String shareUrl
        boolean isShowPrice
        String installment // 名字是历史原因
        TextStyle installmentStyle // 跟着上面
    }

    static String defaultTextColor = "#FF2255"
    static String defaultBgColor = "#FFE8EE"

    class PriceTagVO {
        String text
        String textColor = defaultTextColor
        String bgColor = defaultBgColor
    }

    @Override
    SummaryVO translate(ItemBaseDO input1, SeckillDO seckillDO, SkuDO skuDO) {
        if (!input1) {
            return null
        }
        def summary = new SummaryVO()
        InvokerHelper.setProperties(summary, input1.properties)
        summary.updatePrices()

        summary.priceColor = "#333333"
        summary.title = input1.title
        summary.priceTag = new PriceTagVO(
                text: '秒杀价'
        )
        summary.eventPriceTag = new PriceTagVO(
                text: '每人限购1件'
        )
        // 如果是虚拟优惠券商品
        if (Tools.isVirtualCouponItem()) {
            summary.eventPriceTag.text += "，购买后自动发券，不支持退款"
        }
        // 跟share组件里的url一样的逻辑
        def url = StrategyUpUtil.upUrl("http://h5.mogujie.com/detail/seckill.html?itemId=${input1?.iid}&activityId=${seckillDO?.secKillId}")
        summary.shareUrl = url
        summary.isShowPrice = true

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, false)
        summary.installment = hintWrapper?.hint
        summary.installmentStyle = hintWrapper?.textStyle

        return summary
    }
}