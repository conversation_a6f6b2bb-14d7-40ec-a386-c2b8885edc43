package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator

/**
 * Created by fufeng on 2017/5/24.
 */
@Translator(id = "seckillAddressLink")
class SeckillAddressLink implements IZeroDependTranslator<AddressLinkVO> {
    class AddressLinkVO {
        String bgColor
        String textColor
        String title
        String link
    }

    @Override
    AddressLinkVO translate() {
        new AddressLinkVO(
                title: "设置默认地址（秒杀商品自动发向默认地址）",
                link: "mgj://address?login=1",
                bgColor: "#FFFFFF",
                textColor: "#666666"
        )
    }
}