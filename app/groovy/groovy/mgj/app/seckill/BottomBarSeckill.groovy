package groovy.mgj.app.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import groovy.mgj.app.common.common.ShareIntegralProvider

import java.text.SimpleDateFormat

/**
 * Created by shaoyu on 26/05/2017.
 */
@Translator(id = "bottomBarSeckill")
class BottomBarSeckill implements ITwoDependTranslator<SeckillDO, ItemBaseDO, Object> {

    static class BottomBarSeckillVO {
        String imUrl
        String shopUrl
        String secKillId
        Integer status //秒杀不走详情esi，为了规避缓存问题，强制未开始状态
        SeckillTimeVO seckillTime//因为缓存问题，客户端不使用详情返回的此字段
        SeckillStageVO seckillState//因为缓存问题，客户端不使用详情返回的此字段
        String shareIconUrl // 分享按钮图标
        boolean showShare = false // 是否展示分享按钮
        boolean shareIntegralOpen // 分享积分是否开启
    }

    static class SeckillTimeVO {
        String bgColor
        float bgAlpha
        String textColor
        String waitDesc
        String comingPrefixDesc
        Integer comingInterval
        Integer startTime
        Integer endTime
        Integer remainTime//是否使用看客户端,距离下一个状态倒计时(秒)，end 状态之后为0
        Integer startCountDown//当前系统时间与startTime间隔，客户端采用不使用服务器时间计算方案，方便客户端倒计时
    }

    static class SeckillStageVO {
        SeckillStage stateNotStart
        SeckillStage stagePrepare
        SeckillStage stageIn
        SeckillStage stageEnd
        SeckillStage stageRefresh


    }

    static class SeckillStage {
        String textColor
        String text
    }

    @Override
    Object translate(SeckillDO seckillInput, ItemBaseDO itemInput) {
        if ((!seckillInput) || (!itemInput)) {
            return null
        }
        if (!seckillInput.secKillId) {
            return null
        }

        Date date = new Date(((long) seckillInput?.startTime) * 1000)
        SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日 HH:mm:ss")
        String dateString = formatter.format(date)

        String imUrlString = "mgjim://talk?bid=${itemInput.shopId}&goodsId=${itemInput.iid}&userId=${itemInput.userId}&shopid=${itemInput.shopId}&activityId=${seckillInput.secKillId}&fromType=mgj_seckill&login=1"

        int nowTimeValue = (int)(System.currentTimeMillis()/1000)
        int startCountDownValue = seckillInput.startTime - nowTimeValue

        new BottomBarSeckillVO(
                shareIntegralOpen: ShareIntegralProvider.canGainIntegral(itemInput),
                showShare: false,
                shareIconUrl: ShareIntegralProvider.getShareIconUrl(itemInput),
                imUrl: imUrlString,
                shopUrl: itemInput?.shopId ? ("mgj://shop?shopId=${itemInput.shopId}") : null,
                secKillId: seckillInput?.secKillId,
                status: 0 , //seckillInput?.status,
                seckillTime: seckillInput ? new SeckillTimeVO(

                        bgColor: "#000000",

                        bgAlpha: 0.6,

                        textColor: "#FFFFFF",

                        waitDesc: "${dateString} 开始秒杀",

                        comingPrefixDesc: "距离秒杀开始还有 ",

                        comingInterval: (5 * 60),

                        startTime: seckillInput?.startTime,

                        endTime: seckillInput?.endTime,

                        remainTime: seckillInput?.countdown,

                        startCountDown: startCountDownValue,

                ) : new SeckillTimeVO(),
                seckillState: seckillInput ? new SeckillStageVO(

                        stateNotStart: new SeckillStage(
                                textColor: "#FFFFFF",
                                text: "即将开始",
                        ),

                        stagePrepare: new SeckillStage(
                                textColor: "#FFFFFF",
                                text: "即将开抢 点击刷新",
                        ),

                        stageIn: new SeckillStage(
                                textColor: "#FFFFFF",
                                text: "立即秒杀",
                        ),

                        stageEnd: new SeckillStage(

                                textColor: "#FFFFFF",
                                text: "已结束",
                        ),

                        stageRefresh: new SeckillStage(
                                textColor: "#FFFFFF",
                                text: "刷新中",
                        ),
                ) : new SeckillStageVO(),
        )
    }
}