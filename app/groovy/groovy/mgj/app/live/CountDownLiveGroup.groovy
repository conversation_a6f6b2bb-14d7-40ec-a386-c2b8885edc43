package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.CountdownVO
import org.apache.commons.lang3.StringUtils

/**
 * Created by wuyi on 2018/1/26.
 */
@Translator(id = "countDownLiveGroup", defaultValue = DefaultType.EMPTY_MAP)
class CountDownLiveGroup implements ITwoDependTranslator<ChannelInfoDO, SkuDO, CountDownLiveGroupVO>{
    static class CountDownLiveGroupVO {
        CountdownVO countDownLive
    }

    @Override
    CountDownLiveGroupVO translate(ChannelInfoDO channelInfoDO, SkuDO skuDO) {
        CountDownLive translator = new CountDownLive()
        CountdownVO countdownVO = translator.translate(channelInfoDO, skuDO)
        if (valid(countdownVO)) {
            return new CountDownLiveGroupVO(
                    countDownLive: countdownVO
            )
        } else {
            return new CountDownLiveGroupVO()
        }
    }

    static boolean valid(CountdownVO vo) {
        return vo != null && !StringUtils.isEmpty(vo?.countdownBgImg)
    }
}
