package groovy.mgj.app.live

import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Created by wuy<PERSON> on 2018/1/20.
 */
class LiveState {
    /**
     * 活动未开始
     */
    public static int WAIT_FOR_START = 0

    /**
     * 活动中且未售完主播未改价
     */
    public static int IN_ACTIVITY_NO_CHANGE_PRICE = 1

    /**
     * 活动中且未售完且主播已改价
     */
    public static int IN_ACTIVITY_CHANGE_PRICE = 2

    /**
     * 活动中且主播已改价售罄
     */
    public static int IN_ACTIVITY_CHANGE_PRICE_OUT_OF_STOCK = 3

    /**
     * 活动中主播未改价售罄
     */
    public static int IN_ACTIVITY_NO_CHANGE_PRICE_OUT_OF_STOCK = 4

    /**
     * 活动结束
     */
    public static int END = 5

    static int getState(ChannelInfoDO channelInfoDO, SkuD<PERSON> skuDO) {
        // 异常情况当做活动结束处理
        if (!channelInfoDO || !skuDO) {
            return END
        }
        def nowTime = System.currentTimeSeconds()
        if (nowTime < channelInfoDO.startTime) {
            return WAIT_FOR_START
        } else if (nowTime > channelInfoDO.endTime) {
            return END
        } else {
            boolean outOfStock = skuDO.totalStock <= 0
            boolean changePrice = channelInfoDO.currentPriceIsChannelPrice
            if (outOfStock) {
                if (changePrice) {
                    return IN_ACTIVITY_CHANGE_PRICE_OUT_OF_STOCK
                } else {
                    return IN_ACTIVITY_NO_CHANGE_PRICE_OUT_OF_STOCK
                }
            } else {
                if (changePrice) {
                    return IN_ACTIVITY_CHANGE_PRICE
                } else {
                    return IN_ACTIVITY_NO_CHANGE_PRICE
                }
            }
        }
    }
}
