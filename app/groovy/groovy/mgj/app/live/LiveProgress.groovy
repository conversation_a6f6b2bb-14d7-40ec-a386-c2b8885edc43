package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Created by wuy<PERSON> on 2018/1/19.
 */
@Translator(id = "liveProgress", defaultValue = DefaultType.EMPTY_MAP)
class LiveProgress implements ITwoDependTranslator<ChannelInfoDO, SkuDO, LiveProgessVO> {
    class LiveProgessVO {
        String leftTitle
        String leftTextColor
        String rightTitle
        String rightTextColor
        Integer progress
    }

    @Override
    LiveProgessVO translate(ChannelInfoDO channelInfoDO, SkuDO skuDO) {
        // 库存倒计时要求是实时数据，不能展示缓存
        if (!channelInfoDO) {
            return new LiveProgessVO()
        }
        int originTotalStock = channelInfoDO.originTotalStock
        int remainStock = skuDO.totalStock
        int saledStock = originTotalStock - remainStock

        Integer progress = Math.ceil((originTotalStock - remainStock) * 100 / originTotalStock)
        String leftTitle, rightTitle
        String leftTextColor = "#999999"
        String rightTextColor = "#999999"

        int state = LiveState.getState(channelInfoDO, skuDO)
        switch (state) {
            case LiveState.IN_ACTIVITY_CHANGE_PRICE:
                leftTitle = "已抢购${saledStock}件"
                rightTitle = "仅剩${remainStock}件"
                return new LiveProgessVO (
                        leftTitle: leftTitle,
                        rightTitle: rightTitle,
                        progress: progress,
                        leftTextColor: leftTextColor,
                        rightTextColor: rightTextColor
                )
            case LiveState.IN_ACTIVITY_CHANGE_PRICE_OUT_OF_STOCK:
                leftTitle = "已抢购${saledStock}件"
                rightTitle = "已抢完"
                rightTextColor = "#FF5777"
                return new LiveProgessVO (
                        leftTitle: leftTitle,
                        rightTitle: rightTitle,
                        progress: progress,
                        leftTextColor: leftTextColor,
                        rightTextColor: rightTextColor
                )
            default:
                return new LiveProgessVO()
        }
    }
}
