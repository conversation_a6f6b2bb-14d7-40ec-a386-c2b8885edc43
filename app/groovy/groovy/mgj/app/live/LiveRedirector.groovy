package groovy.mgj.app.live

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.RedirectorVO
import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2018/1/20.
 */
@Translator(id = "liveRedirector", defaultValue = DefaultType.EMPTY_MAP)
class LiveRedirector implements IThreeDependTranslator<ItemBaseDO, ChannelInfoDO, SkuDO, RedirectorVO> {
    @Override
    RedirectorVO translate(ItemBaseDO itemBaseDO, ChannelInfoDO channelInfoDO, SkuDO skuDO) {
        int state = LiveState.getState(channelInfoDO, skuDO)

        //如果商品状态不是正常在售状态
        if (itemBaseDO?.state != 0 && itemBaseDO?.state != 2) {
            state = LiveState.END
        }

        boolean shouldRedirect = false

        switch (state) {
            case LiveState.END:
                shouldRedirect = true
                break
            default:
                break
        }

        if (shouldRedirect) {
            return new RedirectorVO (
                    toast: "本商品直播特卖已经结束，正在为您跳转至新购买地址~",
                    duration: 2,
                    url: Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid)),
                    closeSelf: true
            )
        } else {
            return new RedirectorVO()
        }
    }
}
