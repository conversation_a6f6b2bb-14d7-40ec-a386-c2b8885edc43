package groovy.mgj.app.live

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.VersionController
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.common.common.SummaryRateInfoVO
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

/**
 * Created by wuyi on 2018/1/17.
 */
@Translator(id = "summaryLive", defaultValue = DefaultType.NULL)
class SummaryLive implements IFiveDependTranslator<ItemBaseDO, RateDO, ShopDO, ChannelInfoDO,SkuDO, SummaryVO> {
    class SummaryVO extends ItemPriceVO {
        String title
        String priceColor
        PriceTagVO priceTag
        SummaryRateInfoVO rate
        PriceTagVO eventPriceTag
        String shareUrl
        boolean isShowPrice
        String titleIcon
        String installment
        TextStyle installmentStyle // 名字跟着上面
        List<String> titleIconsList
    }

    static String defaultTextColor = "#FF2255"
    static String defaultBgColor = "#FFE8EE"

    class PriceTagVO {
        String text
        String textColor = defaultTextColor
        String bgColor = defaultBgColor
    }

    @Override
    SummaryVO translate(ItemBaseDO itemBaseDO, RateDO rateDO, ShopDO shopDO, ChannelInfoDO channelInfoDO,SkuDO skuDO) {
        if (!itemBaseDO) {
            // 出问题了，返回null展示缓存，总不能让价格不展示了
            return null
        }
        def summary = new SummaryVO()
        summary.title = itemBaseDO.title
        summary.highNowPrice = itemBaseDO.highNowPrice
        summary.lowNowPrice = itemBaseDO.lowNowPrice
        summary.highPrice = itemBaseDO.highPrice
        summary.lowPrice = itemBaseDO.lowPrice
        summary.updatePrices()

        summary.priceColor = "#333333"
        summary.title = itemBaseDO.title
        if (channelInfoDO?.currentPriceIsChannelPrice) {
            String priceTag = channelInfoDO?.channelMetaInfo?.priceDesc ? channelInfoDO?.channelMetaInfo?.priceDesc : "直播秒杀价"
            summary.priceTag = new PriceTagVO(
                    text: priceTag,
            )
        }
        if (Tools.isVirtualCouponItem()) {
            summary.eventPriceTag = new PriceTagVO(
                    text: "购买后自动发券，不支持退款"
            )
        }

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        summary.installment = hintWrapper?.hint
        summary.installmentStyle = hintWrapper?.textStyle

        summary.isShowPrice = false
        Map<String, Object> maitData = MaitUtil.getMaitData(123205)?.get(0)
        summary.titleIcon=maitData?.get("titleIcon")
        // 标的优先级 自营 > 渠道 > 跨境
        summary.titleIconsList = []
        // 自营，1160后上移到头图组件里展示
        if (Tools.isSelfEmployedItem() && !VersionController.needTranslateSelfIcon()) {
            Map<String, Object> iconMaitData = MaitUtil.getMaitData(127253)?.get(0)
            String icon = iconMaitData?.get("selfIcon")
            if (icon) {
                summary.titleIconsList.add(icon)
            }
        }
        // 上面处理过的促销/渠道标
        if (!TextUtils.isEmpty(summary.titleIcon)) {
            summary.titleIconsList.add(summary.titleIcon)
        }
        // 跨境
        if (itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM
                || itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
            Map<String, Object> iconMaitData = MaitUtil.getMaitData(127253)?.get(0)
            String icon = iconMaitData?.get("overseaIcon")
            if (icon) {
                summary.titleIconsList.add(icon)
            }
        }

        // 跟share组件里的url一样的逻辑
        def activityId = DetailContextHolder.get()?.getParam("activityId")
        if (activityId) {
            def url = StrategyUpUtil.upUrl("http://h5.mogujie.com/detail-normal/live/index.html?itemId=${itemBaseDO?.iid}&activityId=${activityId}")
            summary.shareUrl = url
        }

        // rate使用缓存
        if (!DetailContextHolder.get().isDyn()) {
            // DSR信息
            if (rateDO != null && itemBaseDO != null) {
                SummaryRateInfoVO summaryRateInfoVO = new SummaryRateInfoVO(itemBaseDO, rateDO, shopDO)
                summary.rate = summaryRateInfoVO
            }
        }

        if (summary.oldPrice == null) {
            summary.oldPrice = ""
        }
        if (summary.priceTag == null) {
            summary.priceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
        }
        if (summary.eventPriceTag == null) {
            summary.eventPriceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
        }
        if (summary.titleIconsList == null) {
            summary.titleIconsList = []
        }

        return summary
    }
}
