package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.MaitUtil

@Translator(id = "liveAtmosphereBanner")
class LiveAtmosphereBanner implements IZeroDependTranslator<AtmosphereBannerVO> {

    static class AtmosphereBannerVO {
        String background
        String link
        String acm
    }

    @Override
    AtmosphereBannerVO translate(){
        def result = new AtmosphereBannerVO()
        Map<String, Object> maitData = MaitUtil.getMaitData(123205)?.get(0)
        result.background=maitData?.get("activityBanner")
        return result
    }

}

