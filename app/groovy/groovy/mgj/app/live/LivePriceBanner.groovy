package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.PriceBannerVO

@Translator(id = "livePriceBanner")

class LivePriceBanner implements ITwoDependTranslator<ChannelInfoDO,ItemBaseDO,PriceBannerVO > {

    @Override
    PriceBannerVO translate(ChannelInfoDO channelInfoDO,ItemBaseDO itemBase) {
        PriceBannerVO result = new PriceBannerVO()
        Map<String, Object> maitData = MaitUtil.getMaitData(123205)?.get(0)
        result.coverBg = maitData?.get("coverBg")
        result.priceColor = maitData?.get("priceColor")

        if (channelInfoDO?.currentPriceIsChannelPrice) {
            result.priceTag = channelInfoDO?.channelMetaInfo?.priceDesc ? channelInfoDO?.channelMetaInfo?.priceDesc : "直播秒杀价"
        }

        result.highNowPrice = itemBase?.highNowPrice
        result.lowNowPrice = itemBase?.lowNowPrice
        result.highPrice = itemBase?.highPrice
        result.lowPrice = itemBase?.lowPrice
        result.updatePrices()

        return result
    }
}
