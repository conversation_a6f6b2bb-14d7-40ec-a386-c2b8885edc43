package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.ShareIntegralProvider
import groovy.mgj.app.live.common.LiveUrlGenerator
import groovy.mgj.app.vo.BottomBarItemData
import groovy.mgj.app.vo.NormalBottombarVO
import org.apache.http.util.TextUtils

import static groovy.mgj.app.vo.NormalBottombarVO.*

/**
 * Created by wuyi on 2018/1/20.
 */
@Translator(id = "bottomBarLiveV2")
class BottombarLiveV2 implements IThreeDependTranslator<ItemBaseDO, ChannelInfoDO, SkuD<PERSON>, NormalBottombarVO> {

    @Override
    NormalBottombarVO translate(ItemBaseDO itemBaseDO, ChannelInfoDO channelInfoDO, SkuDO skuDO) {
        NormalBottombarVO VO = new NormalBottombarVO(
                imUrl: LiveUrlGenerator.generateMGJImUrl(itemBaseDO),
                shopUrl: itemBaseDO?.shopId ? "mgj://shop?shopId=${itemBaseDO?.shopId}" : null,
                shareIntegralOpen: ShareIntegralProvider.canGainIntegral(itemBaseDO)
        )

        int state = LiveState.getState(channelInfoDO, skuDO)
        //如果商品状态不是正常在售状态
        if (itemBaseDO?.state != 0 && itemBaseDO?.state != 2) {
            state = LiveState.END
        }

        def rightTypeList
        String buyButtonText
        switch (state) {
            case LiveState.WAIT_FOR_START:
                rightTypeList = composeRightList([new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "立即抢购",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
            case LiveState.IN_ACTIVITY_NO_CHANGE_PRICE:
                rightTypeList = composeRightList([new BottomBarItemData(
                        type: RIGHT_BUY
                )])
                buyButtonText = "立即抢购"
                break
            case LiveState.IN_ACTIVITY_CHANGE_PRICE:
                rightTypeList = composeRightList([new BottomBarItemData(
                        type: RIGHT_BUY
                )])
                buyButtonText = "立即抢购"
                break
            case LiveState.IN_ACTIVITY_CHANGE_PRICE_OUT_OF_STOCK:
            case LiveState.IN_ACTIVITY_NO_CHANGE_PRICE_OUT_OF_STOCK:
                rightTypeList = composeRightList([new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "已抢完",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
            case LiveState.END:
            default:
                rightTypeList = composeRightList([new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "立即抢购",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
        }

        // 按钮列表
        VO.leftButtonList = composeLeftList([LEFT_SHOP, LEFT_IM])
        VO.rightButtonList = composeRightList(rightTypeList)
        List<BottomBarItemData> floatButtonList = new ArrayList<>()
        VO.floatButtonList = floatButtonList
        // 分享按钮
        String icon = ShareIntegralProvider.getShareIconUrl(itemBaseDO)
        if (!TextUtils.isEmpty(icon)) {
            BottomBarItemData shareItemData = new BottomBarItemData(
                    type: FLOAT_SHARE,
                    icon: icon)
            floatButtonList.add(shareItemData)
        }
        // 购买按钮上的文案
        if (!TextUtils.isEmpty(buyButtonText)) {
            VO.buyButtonText = buyButtonText
        }

        return VO
    }
}
