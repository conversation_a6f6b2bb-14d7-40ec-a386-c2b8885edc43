package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.CountdownVO

/**
 * Created by wuyi on 2018/1/17.
 */
// 被包在group里，没有dataid
class CountDownLive implements ITwoDependTranslator<ChannelInfoDO, SkuDO, CountdownVO> {
    /** 氛围的麦田资源位 */
    final long MCE_ID = 43182

    @Override
    CountdownVO translate(ChannelInfoDO channelInfoDO, SkuDO skuDO) {
        // 渠道不需要关心大促
        if (!channelInfoDO) {
            // 这里用实时数据
            return new CountdownVO()
        } else {
            int state = LiveState.getState(channelInfoDO, skuDO)
            switch (state) {
                case LiveState.IN_ACTIVITY_CHANGE_PRICE:
                case LiveState.IN_ACTIVITY_CHANGE_PRICE_OUT_OF_STOCK:
                    Map<String, Object> maitData = MaitUtil.getMaitData(MCE_ID)?.get(0)
                    def backgroundImg = maitData?.get("liveImage")
                    // 是否能渠道价购买是主播主动改价决定的，所以没有固定的时间，没有倒计时
                    return new CountdownVO(
                            countdownBgImg:backgroundImg
                    )
                default:
                    // 不能展示缓存
                    return new CountdownVO()
            }
        }
    }
}
