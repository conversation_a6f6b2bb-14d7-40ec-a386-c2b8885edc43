package groovy.mgj.app.live.common

import com.mogujie.commons.utils.IdConvertor

/**
 * Created by wuyi on 2018/1/22.
 */
class LiveIdConvertor {
    static String convertActivityId(String activityId) {
        if (!activityId) {
            return null
        }
        if (!activityId.isNumber()) {
            return activityId
        }
        Long activityLong = Long.parseLong(activityId)
        return activityLong ? IdConvertor.idToUrl(activityLong) : activityId
    }
}
