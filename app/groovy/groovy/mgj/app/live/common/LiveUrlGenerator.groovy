package groovy.mgj.app.live.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor

/**
 * Created by wuyi on 2018/1/23.
 */
class LiveUrlGenerator {
    static final String channelId = "channel_live"
    static final String fromType = "mgj_live"

    static String generateMGJImUrl(ItemBaseDO itemBaseDO) {
        String activityId = DetailContextHolder.get().getParam("activityId")
        def activityIdEsc = LiveIdConvertor.convertActivityId(activityId)
        return "mgjim://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&activityId=${activityIdEsc}&channelId=${channelId}&fromType=${fromType}&login=1"
    }

    static generateMGJShareImUrl(ItemBaseDO itemBaseDO) {
        String activityId = DetailContextHolder.get().getParam("activityId")
        def activityIdEsc = LiveIdConvertor.convertActivityId(activityId)
        return "mgjim://share?iid=${itemBaseDO?.iid}&shopId=${itemBaseDO?.shopId}&type=1&activityId=${activityIdEsc}&channelId=${channelId}&fromType=${fromType}"
    }
}
