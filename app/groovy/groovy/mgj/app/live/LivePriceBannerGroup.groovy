package groovy.mgj.app.live

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.common.PriceBanner
import groovy.mgj.app.vo.PriceBannerVO

@Translator(id = "livePriceBannerGroup")

class LivePriceBannerGroup implements ITwoDependTranslator<ChannelInfoDO,ItemBaseDO,LivePriceBannerGroupVO > {
    static class LivePriceBannerGroupVO {
        PriceBannerVO priceBanner
    }
    @Override
    LivePriceBannerGroupVO translate(ChannelInfoDO channelInfoDO,ItemBaseDO itemBase) {
        LivePriceBanner translator = new LivePriceBanner()
        PriceBannerVO priceBannerVO = translator.translate(channelInfoDO,itemBase)
        if (priceBannerVO == null) {
            return new LivePriceBannerGroupVO()
        }
        else {
            return new LivePriceBannerGroupVO(
                    priceBanner: priceBannerVO
            )
        }
    }

}
