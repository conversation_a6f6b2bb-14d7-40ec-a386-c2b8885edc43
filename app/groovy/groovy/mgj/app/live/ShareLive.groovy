package groovy.mgj.app.live

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.live.common.LiveUrlGenerator
import groovy.mgj.app.normal.Share

/**
 * Created by wuyi on 2018/1/22.
 */
@Translator(id = "shareLive")
class ShareLive implements IThreeDependTranslator<ItemBaseDO, ShopDO, ExtraDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO, ShopDO shopDO, ExtraDO extraDO) {
        def shareNormal = new Share()
        def result = shareNormal.translate(itemBaseDO, shopDO, null, extraDO)
        // 渠道的分享url跟普通商品不一样
        // activityId直接从参数中取
        def activityId = DetailContextHolder.get()?.getParam("activityId")
        if (activityId) {
            def url = StrategyUpUtil.upUrl("http://h5.mogujie.com/detail-normal/live/index.html?itemId=${itemBaseDO?.iid}&activityId=${activityId}")
            result?.url = url

            // 渠道的小程序url跟普通商品不一样
            def miniProgramPath = "/pages/detail/index?pageName=live&itemId=${itemBaseDO?.iid}&activityId=${activityId}"
            result?.miniProgramPath = miniProgramPath

            // 渠道的私聊链接跟普通商品不一样
            def imUrl = LiveUrlGenerator.generateMGJShareImUrl(itemBaseDO)
            result?.imUrl = imUrl

            result?.eventPrice = null
        }

        result?.imUrl = Share.getUpdatedIMUrl(result?.imUrl, itemBaseDO, result?.url)

        return result
    }
}
