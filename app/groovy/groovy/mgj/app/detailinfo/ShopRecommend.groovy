package groovy.mgj.app.detailinfo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendDO
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendItem
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/4/25.
 */
@Translator(id = "shopRecommend")
class ShopRecommend implements IThreeDependTranslator<ItemBaseDO, ShopRecommendDO, PinTuanDO , ShopRecommendVO> {

    static class ShopRecommendVO {
        Map<String, String> sectionTitle
        Map<String, ArrayList<ShopRecommendVOItem>> shopRecommendData

    }

    static class ShopRecommendVOItem {
        String price
        String priceTag
        String image
        String link
        String title
    }

    @Override
    ShopRecommendVO translate(ItemBaseDO itemBaseDO,ShopRecommendDO shopRecommendDO, PinTuanDO pinTuanDO) {
        if (shopRecommendDO?.recommendItemList?.size() > 0) {
            def vo = new ShopRecommendVO(
                    sectionTitle: ["key": "店铺推荐"]
            )
            def list = []
            shopRecommendDO.recommendItemList.each {
                def item = new ShopRecommendVOItem(
                        price: it.price,
                        priceTag: it.isPintuan ? ImageUtil.img("/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png") : "",
                        image: it.img,
                        title: it.title,
                        link: "mgj://detail?iid=${it.iid}"
                )
                list << item
            }
            vo.shopRecommendData = ["list":list]
            return vo
        }
        else {
            return null
        }


    }

}