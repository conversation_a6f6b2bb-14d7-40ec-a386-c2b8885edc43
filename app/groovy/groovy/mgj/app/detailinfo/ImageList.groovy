package groovy.mgj.app.detailinfo

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

/*
输出结构是这样
- section title
- image
- image
- image
- image
- section title
- image
- image
- image
 */

@Translator(id = "imageList")
class ImageList implements IOneDependTranslator<DetailDO, ArrayList> {

    class SectionTitleVO {
        String key
        String desc
    }

    class DetailImageVO {
        String url
        int margin
    }

    @Override
    ArrayList translate(DetailDO input1) {
        return input1?.detailImage?.inject([]) { sum, value ->
            sum << [sectionTitle: new SectionTitleVO(
                    key: value.key,
                    desc: value.desc
            )]
            def imageList = value.list.collect {
                return [image: new DetailImageVO(
                        url: it,
                        margin: input1?.splitDetailImage ? 8 : 0)]
            }
            sum.addAll(imageList)
            return sum
        }
    }


}

