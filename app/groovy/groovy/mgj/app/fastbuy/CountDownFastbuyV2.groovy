package groovy.mgj.app.fastbuy

/**
 * Created by fufeng on 2017/4/1.
 */
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.mgj.app.vo.CountdownVO

//这里展示不需要@translator,因为实在group里面返回的
class CountDownFastbuyV2 implements IOneDependTranslator<FastbuyDO, CountdownVO>{

    @Override
    CountdownVO translate(FastbuyDO input1) {
        if (!input1){
            return new CountdownVO()
        }
        def started = isStarted(input1)
        Map<String, Object> maitData = MaitUtil.getMaitData(43182)?.get(0)
        def backgroundImg = maitData?.get("fastBuyImage")
        def titleColor = maitData?.get("fastBuyTitleColor")
        if (!started){
            return new CountdownVO(
                    countdownBgImg:backgroundImg,
                    countdownTitle: "距开始仅剩",
                    countdown:input1.startTime - System.currentTimeSeconds(),
                    countdownTitleColor:titleColor
            )
        }
        else {
            return new CountdownVO(
                    countdownBgImg:backgroundImg,
                    countdownTitle: "距结束仅剩",
                    countdown: input1.endTime - System.currentTimeSeconds(),
                    countdownTitleColor:titleColor
            )
        }
    }

    static def isStarted(FastbuyDO fastbuy){
        if (!fastbuy){
            return false
        }
        def nowTime = System.currentTimeSeconds()
        return fastbuy.startTime < nowTime && nowTime < fastbuy.endTime
    }
}
