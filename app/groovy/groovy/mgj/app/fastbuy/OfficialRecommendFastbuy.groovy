package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.vo.Tools
import org.apache.http.util.TextUtils

/**
 * Created by wuyi on 2019/1/30.
 */
@Translator(id = "officialRecommendFastbuy")
class OfficialRecommendFastbuy implements IThreeDependTranslator<ItemBaseDO, SkuDO, FastbuyDO, Object> {
    class OfficialRecommendVO {

        String title
        String desc
        String highlightDesc
        List<String> tags
    }

    @Override
    Object translate(ItemBaseDO itemBaseDO, SkuDO skuDO, FastbuyDO fastbuyDO) {
        if (!shouldShow(itemBaseDO, fastbuyDO)) {
            return new OfficialRecommendVO()
        }

        def tags = []
        // 官方推荐标
        OfficialRecommend or = itemBaseDO.getOfficialRecommend()
        def tag = MaitUtil.getMaitData(127253)?.get(0)?.get("officialRecommendIcon")
        if (tag) tags << tag

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        String highlightDesc = hintWrapper?.hint

        highlightDesc = highlightDesc ?: ""

        return new OfficialRecommendVO(
                title: or.title ?: "",
                desc: or.desc ?: "",
                highlightDesc: highlightDesc ?: "",
                tags: tags)
    }

    static hasValidOfficialRecommend(ItemBaseDO itemBaseDO) {
        if (itemBaseDO == null || itemBaseDO.getOfficialRecommend() == null) {
            return false
        }

        OfficialRecommend or = itemBaseDO.getOfficialRecommend()
        return !TextUtils.isEmpty(or.title) || !TextUtils.isEmpty(or.desc)
    }

    static shouldShow(ItemBaseDO itemBaseDO, FastbuyDO fastbuyDO) {
        // 判断快抢的类型 limitedReduction是限量立减 http://pre.console.metabase.mogujie.org/#/detail/detailConf/fastbuy_type_mait_mapping
        return "limitedReduction" == fastbuyDO?.getExtra()?.get("bizType") && hasValidOfficialRecommend(itemBaseDO) && Tools.hasDataId("officialRecommendFastbuy")
    }
}
