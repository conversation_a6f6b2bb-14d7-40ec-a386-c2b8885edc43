package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.Switcher
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.fastbuy.common.FastbuyImUrlGenerator
import groovy.mgj.app.normal.Share
import groovy.mgj.app.normal.ShareCssDataProducer
import groovy.mgj.app.normal.ShareCssDataProducers
import groovy.mgj.app.normal.ShareCssDataWrap
import groovy.mgj.app.vo.Tools
import org.apache.http.util.TextUtils

@Translator(id = "shareFastbuy")
class ShareFastbuy implements IFiveDependTranslator<ItemBaseDO, ShopDO, ActivityDO, FastbuyDO, ExtraDO, Object> {
    @Override
    Object translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, FastbuyDO input4, ExtraDO extraDO) {
        def shareNormal = new Share()
        def result = shareNormal.translate(input1, input2, input3, extraDO)

        if (!result) return result

        if (input4?.activityId) {

            //快抢的分享url不一样
            def fastbuyId = FastbuyIdConvertor.convertFastbuyId(input4?.activityId)
            def url = "http://h5.mogu.com/detail/fastbuy.html?itemId=${input1?.iid}&activityId=${fastbuyId}"
            result.url = url
            //分享小程序路径不一样
            def miniProgramPath = "pages/detail/pages/rush/index?itemId=${input1?.iid}&activityId=${fastbuyId}"
            result.miniProgramPath = miniProgramPath

            //imUrl不一样
            def imUrl = FastbuyImUrlGenerator.generateMGJShareImUrl(input1, input4)
            result.imUrl = imUrl
            //如果有快抢，那么不要返回eventPrice
            result.eventPrice = null
        }
        result.imUrl = Share.getUpdatedIMUrl(result.imUrl, input1, result.url)

        // 如果有官方推荐的标题，用官方推荐的标题
        if (OfficialRecommendFastbuy.shouldShow(input1, input4)
                && !TextUtils.isEmpty(input1?.getOfficialRecommend()?.title)) {
            result.itemTitle = input1?.getOfficialRecommend()?.title
        }

        boolean csslayoutShareOpen = Switcher.csslayoutMiniCardShare()
        ShareCssDataWrap cssDataWrap = null
        // 处理csslayout的相关数据
        if (csslayoutShareOpen) {
            List<ShareCssDataProducer> producers = ShareCssDataProducers.FOR_FASTBUY
            for (ShareCssDataProducer producer : producers) {
                cssDataWrap = producer.produce(input1, extraDO, null, result)
                if (cssDataWrap != null) {
                    break
                }
            }
        }
        if (cssDataWrap != null) {
            result.miniCardTempMaitID = cssDataWrap.maitId
            result.miniCardData = cssDataWrap.data
            result.miniCardTempMaitParams = ["iid": "" + DetailContextHolder.get()?.getItemId()]
            result.miniCardType = cssDataWrap.cardtype
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        result.mayHideOldPrice(input1)

        return result
    }
}