package groovy.mgj.app.fastbuy

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.fastbuy.common.FastbuyState
import groovy.mgj.app.vo.RedirectorVO
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/11/1.
 * 客户端重定向配置
 */
@Translator(id = "fastbuyRedirector", defaultValue = DefaultType.EMPTY_MAP)
class FastbuyRedirector implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, RedirectorVO> {
    @Override
    RedirectorVO translate(ItemBaseDO itemBaseDO, FastbuyDO fastbuyDO) {

        boolean shouldRedirect = false

        if (fastbuyDO == null) {
            shouldRedirect = true
        } else {
            Integer rushState = fastbuyDO.getState()
            //如果商品状态不是正常在售状态
            if (itemBaseDO?.state != 0 && itemBaseDO?.state != 2) {
                rushState = 3
            }
            if (rushState == FastbuyState.OUT_OF_STOCK || rushState == FastbuyState.ACTIVITY_ENDED) {
                shouldRedirect = true
            }
        }

        if (shouldRedirect) {
            return new RedirectorVO(
                    toast: '本场快抢已结束，快抢落幕抢购不停，正在为您跳转至新购买地址~',
                    duration: 2,
                    url: Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid)),
                    closeSelf: true
            )
        } else {
            return new RedirectorVO()
        }
    }
}
