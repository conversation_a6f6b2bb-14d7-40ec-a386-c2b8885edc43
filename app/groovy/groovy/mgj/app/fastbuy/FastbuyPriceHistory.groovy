package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "fastbuyPriceHistory")
class FastbuyPriceHistory implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, FastbuyPriceHistoryVO> {

    class FastbuyPriceHistoryVO {
        String imgUrl
    }

    @Override
    FastbuyPriceHistoryVO translate(FastbuyDO fastbuyDO, ItemBaseDO itemBaseDO) {
        boolean needImg = true
        // 如果是新品快时尚类型 OR 没有价格证明，不展示价格趋势图
        if ("newFashionBuy" == fastbuyDO?.getExtra()?.get("bizType")
                || (itemBaseDO != null && !itemBaseDO.canShowStrikethroughPrice)) {
            needImg = false
        }
        if (needImg && fastbuyDO?.extra?.get("kimg")) {
            return new FastbuyPriceHistoryVO(
                    "imgUrl": fastbuyDO?.extra?.get("kimg") ?: ""
            )
        }
        return new FastbuyPriceHistoryVO()
    }
}
