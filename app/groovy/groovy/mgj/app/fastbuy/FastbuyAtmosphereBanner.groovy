package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO


@Translator(id = "fastbuyAtmosphereBanner")
class FastbuyAtmosphereBanner implements IOneDependTranslator<FastbuyDO,AtmosphereBannerVO> {

    static class AtmosphereBannerVO {
        String background
        String link
        String acm
    }

    @Override
    AtmosphereBannerVO translate(FastbuyDO fastbuyDO) {
        AtmosphereBannerVO result = new AtmosphereBannerVO()
        Map<String, Object> maitData = MaitUtil.getTargetedMaitData(fastbuyDO?.maitId)?.get(0)
        result.background = maitData?.get("appActivityBanner")
        if (!result.background) {
            result.background = maitData?.get("activityBanner")
        }
        result.link = maitData?.get("activityBannerLink") ?: ""

        return result
    }

}
