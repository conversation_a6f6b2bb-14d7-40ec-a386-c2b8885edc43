package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.mgj.app.vo.CountdownVO

@Translator(id = "countdownFastbuy")
class CountDownFastbuy implements IOneDependTranslator<FastbuyDO, CountdownVO>{

    @Override
    CountdownVO translate(FastbuyDO input1) {
        if (!input1){
            return new CountdownVO()
        }
        def started = isStarted(input1)
        def backgroundImg = ImageUtil.img("/p1/161130/idid_ifrgcnrvgu2wiyrymmzdambqmeyde_750x100.png")
        if (!started){
            return new CountdownVO(
                    countdownBgImg:backgroundImg,
                    countdownTitle: "距快抢开始仅剩：",
                    countdown:input1.startTime - System.currentTimeSeconds()
            )
        }
        else {
            return new CountdownVO(
                    countdownBgImg:backgroundImg,
                    countdownTitle: "距快抢结束仅剩：",
                    countdown: input1.endTime - System.currentTimeSeconds()
            )
        }
    }

    static def isStarted(FastbuyDO fastbuy){
        if (!fastbuy){
            return false
        }
        def nowTime = System.currentTimeSeconds()
        return fastbuy.startTime < nowTime && nowTime < fastbuy.endTime
    }
}
