package groovy.mgj.app.fastbuy.common

import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

static generateMGJImUrl(ItemBaseDO item, FastbuyDO fastbuy) {
    if (!fastbuy) {
        return "mgjim://talk?bid=${item.shopId}&goodsId=${item.iid}&userId=${item.userId}&shopid=${item.shopId}&login=1"
    } else {
        def fastbuyId = FastbuyIdConvertor.convertFastbuyId(fastbuy?.activityId)
        return "mgjim://talk?bid=${item.shopId}&goodsId=${item.iid}&userId=${item.userId}&shopid=${item.shopId}&activityId=${fastbuyId}&channelId=channel_kuaiqiang&fromType=mgj_rush&login=1"
    }
}

static generateMGJShareImUrl(ItemBaseDO item, FastbuyDO fastbuy) {
    if (!fastbuy) {
        return (item?.iid && item?.shopId) ? "mgjim://share?iid=${item.iid}&shopId=${item.shopId}&type=1" : null
    } else {
        def fastbuyId = FastbuyIdConvertor.convertFastbuyId(fastbuy?.activityId)
        return "mgjim://share?iid=${item.iid}&shopId=${item.shopId}&type=1&activityId=${fastbuyId}&channelId=channel_kuaiqiang&fromType=mgj_rush"
    }
}
