package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.mgj.app.vo.CountdownVO

/**
 * Created by fufeng on 2017/4/1.
 */
@Translator(id = "countDownFastbuyV2Group")
class CountDownFastbuyV2Group implements IOneDependTranslator<FastbuyDO, CountDownFastBuyV2GroupVO> {
    static class CountDownFastBuyV2GroupVO {
        CountdownVO countDownFastbuyV2
    }
    @Override
    CountDownFastBuyV2GroupVO translate(FastbuyDO input1) {
        CountDownFastbuyV2 translator = new CountDownFastbuyV2()
        CountdownVO countdownVO = translator.translate(input1)
        if (countdownVO == null || countdownVO?.countdown == null || countdownVO?.countdownBgImg == null) {
            return new CountDownFastBuyV2GroupVO()
        }
        else {
            return new CountDownFastBuyV2GroupVO(
                    countDownFastbuyV2: countdownVO
            )
        }
    }
}