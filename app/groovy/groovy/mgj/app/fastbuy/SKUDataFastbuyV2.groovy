package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.common.SKUDataV2
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUDataVOActivityType

@Translator(id = "skuDataFastbuyV2")
class SKUDataFastbuyV2 implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, Object> {

    private SKUDataV2 skuData;

    public SKUDataFastbuyV2() {
        skuData = new SKUDataV2();
    }

    @Override
    SKUDataVO translate(SkuDO input1, Item<PERSON>aseD<PERSON> input2, <PERSON><PERSON><PERSON>D<PERSON> input3) {

        def result = skuData.translate(input1, input2, null,null)
        result.maxFreePhases = input1?.freePhases?.intValue() ?: 0
        if (input3) {
            //加入快抢下单参数
            result.channel = "channel_kuaiqiang"
            result.extensions = [
                    "activityId"  : FastbuyIdConvertor.convertFastbuyId(input3.activityId),
                    "activityType": "1",
            ]
            result.activityType = SKUDataVOActivityType.FASTBUY
        }
        return result
    }
}
