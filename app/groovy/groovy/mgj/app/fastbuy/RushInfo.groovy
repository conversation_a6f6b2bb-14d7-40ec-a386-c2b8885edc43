package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.ImageUtil

@Translator(id = "fastbuyInfo")
class RushInfo implements IZeroDependTranslator<RushInfoVO> {

    class RushInfoVO {
        String topImage = ImageUtil.img('/p1/161128/idid_ifrggmlcgqytkmrymmzdambqmeyde_96x132.png')
        String bgColor = '#F7F2FF'
        String link = 'http://qiang.mogujie.com/fastbuy/indexh5'
        String[] list = [
                "限时限量 疯狂快抢",
                "全国包邮 超值底价 正品保障",
        ]
    }

    @Override
    RushInfoVO translate() {
        new RushInfoVO()
    }
}
