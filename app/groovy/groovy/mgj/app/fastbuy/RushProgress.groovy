package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.mgj.app.fastbuy.common.FastbuyState

@Translator(id = "fastbuyProgress")
class RushProgress implements IOneDependTranslator<FastbuyDO, RushProgessVO> {

    class RushProgessVO {
        String leftTitle
        String leftTextColor
        String rightTitle
        String rightTextColor
        Integer progress
    }

    @Override
    RushProgessVO translate(FastbuyDO input1) {
        if (!input1) {
            return new RushProgessVO()
        }
        Integer progress = input1.allStock != 0 ? Math.ceil((input1.allStock - input1.totalStock) * 100 / input1.allStock) : 0
        String leftTitle = "", rightTitle = ""
        String leftTextColor = "#999999"
        String rightTextColor = "#999999"
        switch (input1.state) {
            case FastbuyState.ACTIVITY_NOT_STARTED:
            case FastbuyState.ACTIVITY_WARM_UP:
                //未开始
                leftTitle = "库存${input1.allStock}件"
                break
            case FastbuyState.ACTIVITY_STARTED:
                //活动中
                leftTitle = "已抢购${input1.allStock - input1.totalStock}件"
                rightTitle = "仅剩${input1.totalStock}件"
                rightTextColor = "#FF5777"
                break
            case FastbuyState.OUT_OF_STOCK:
                leftTitle = "已售罄"
                break
            case FastbuyState.ACTIVITY_ENDED:
                //抢购完或已结束
                leftTitle = "已售罄"
                break
            case FastbuyState.WAIT_FOR_STOCK:
                //抢购完，还有人未付款
                leftTitle = "已抢购${input1.allStock}件"
                rightTitle = "${input1.leftUser}人未付款"
                rightTextColor = "#FF5777"
                break
            default:
                break
        }
        return new RushProgessVO(
                leftTitle: leftTitle,
                rightTitle: rightTitle,
                progress: progress,
                leftTextColor: leftTextColor,
                rightTextColor: rightTextColor
        )
    }
}
