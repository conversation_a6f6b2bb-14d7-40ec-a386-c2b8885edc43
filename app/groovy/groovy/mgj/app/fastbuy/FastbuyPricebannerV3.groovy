package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.vo.PriceCalculationDO
import groovy.mgj.app.vo.Tools

import java.text.SimpleDateFormat

/**
 * Created by yanze on 2020/10/28.
 */
@Translator(id = "fastbuyPriceBannerV3")
class FastbuyPricebannerV3 implements IFourDependTranslator<SkuDO, FastbuyDO, ItemBaseDO, ExtraDO, FastBuyPriceBannerVO> {

    // 倒计时从具体日期进入倒计时样式的阈值
    final static int COUNTDOWN_THRESHOLD_IN_HOURS = 25

    static class FastBuyCountdown {
        String countdownTitle
        String countdownText
        String countdownFgColor = "#000000"
        Long countdown
        Integer threshold
    }

    static class FastBuyStockProgress {
        String title
        String titleColor = "#333333"
        Integer progress
        String progressBgColor = "#FFDFD1"
        String progressValueColor = "#FFC43A"
        String progressValueToColor = "#FF951F"
    }

    static class FastBuyPriceBannerVO extends ItemPriceVO {
        String coverBg
        String priceColor = "#000000"
        FastBuyCountdown countdown
        FastBuyStockProgress stockProgress

        String noticeNum
        String noticeNumBgColor
        String salesText
        Boolean started
        String coreAreaInsets = "0.16 0.152 0.16 0.032"

        String discountPrice

        PriceCalculationDO calculateDiscount
    }


    @Override
    FastBuyPriceBannerVO translate(SkuDO skuDO, FastbuyDO fastbuyDO, ItemBaseDO itemBaseDO, ExtraDO extraDO) {
        if (!fastbuyDO) {
            FastBuyPriceBannerVO ret = new FastBuyPriceBannerVO()
            ret.countdown = new FastBuyCountdown()
            ret.stockProgress = new FastBuyStockProgress()
            ret.highNowPrice = itemBaseDO?.highNowPrice
            ret.lowNowPrice = itemBaseDO?.lowNowPrice
            ret.highPrice = itemBaseDO?.highPrice
            ret.lowPrice = itemBaseDO?.lowPrice
            ret.updatePrices(true)
            ret.calculateDiscount = new PriceCalculationDO()
            return ret
        }

        FastBuyPriceBannerVO result = new FastBuyPriceBannerVO()
        Map<String, Object> maitData = MaitUtil.getMaitData(fastbuyDO?.maitId)?.get(0)
        result.coverBg = maitData?.get("newCoverBg")
        result.priceColor = maitData?.get("newPriceColor")

        def started = isStarted(fastbuyDO)
        result.started = started

        FastBuyCountdown countdown = new FastBuyCountdown()
        countdown.countdownFgColor = maitData?.get("newCountdownColor")
        countdown.threshold = COUNTDOWN_THRESHOLD_IN_HOURS
        result.countdown = countdown

        if (extraDO?.sales) {
            result.salesText = "已售" + formatNumber((int) extraDO.sales)
        }

        if (!started) {

            countdown.countdown = fastbuyDO.startTime - System.currentTimeSeconds()
            countdown.countdownTitle = "距开始："

            // 计算出当前剩余时间
            long startTime = fastbuyDO?.startTime
            Date date = new Date(startTime * 1000)
            String dateStr = new SimpleDateFormat("M月d日HH:mm开抢").format(date)
            countdown.countdownText = dateStr

            // 未开始展示关注人数，为0不展示
            if (fastbuyDO?.noticeNum > 0) {
                result.noticeNum = formatNoticeNum(fastbuyDO?.noticeNum)
            } else {
                result.noticeNum = ""
            }
            result.noticeNumBgColor = maitData?.get("noticeNumBgColor") ?: "#FF556E";

        } else {

            countdown.countdownTitle = "距结束："
            countdown.countdown = fastbuyDO.endTime - System.currentTimeSeconds()

            // 计算出当前剩余时间
            long days = countdown.countdown / (60 * 60 * 24)
            long hours = (countdown.countdown % (60 * 60 * 24)) / (60 * 60)
            countdown.countdownText = String.format("%d天%d时后结束", days, hours)

            FastBuyStockProgress stockProgress = new FastBuyStockProgress()
            stockProgress.title = "仅剩${fastbuyDO.totalStock}件"
            Integer progress = fastbuyDO.allStock == 0 ? 0 : Math.ceil(fastbuyDO.progressBar * 100)
            stockProgress.progress = progress
            stockProgress.titleColor = maitData?.get("newProgressTitleColor")
            stockProgress.progressBgColor = maitData?.get("newProgressBgColor")
            stockProgress.progressValueColor = maitData?.get("newProgressValueColor")
            stockProgress.progressValueToColor = maitData?.get("newProgressValueToColor")
            result.stockProgress = stockProgress

        }

        result.highNowPrice = itemBaseDO?.highNowPrice
        result.lowNowPrice = itemBaseDO?.lowNowPrice
        result.highPrice = itemBaseDO?.highPrice
        result.lowPrice = itemBaseDO?.lowPrice

        // 1480 版本起，不展示区间价，改为 xx起 的格式
        if (Tools.getAppVersion() >= 1480) {
            result.updatePrices(true)
        } else {
            result.updatePrices(false)
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        result.mayHideOldPrice(itemBaseDO)

        // 券后价
        def discountPrice = skuDO?.promotionPrice
        if (discountPrice && discountPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal) {
            result.discountPrice = "券后¥${NumUtil.formatPriceDrawer(discountPrice.intValue())}"
            result.calculateDiscount = new PriceCalculationDO(itemBaseDO, skuDO)
        } else {
            result.discountPrice = ""
            result.calculateDiscount = new PriceCalculationDO()
        }

        return result
    }

    static String formatNoticeNum(int count) {
        String str = "" + count
        if (count > 9999) {
            int tenThousand = count / 10000
            int thousand = ((int) (count % 10000)) / 1000
            str = tenThousand + "." + thousand + "w"
        }
        return str + "人关注"
    }

    static def isStarted(FastbuyDO fastbuy) {
        if (!fastbuy) {
            return false
        }
        def nowTime = System.currentTimeSeconds()
        return fastbuy.startTime < nowTime && nowTime < fastbuy.endTime
    }

    static def formatNumber(int num) {
        if (num >= 10000) {
            return String.format("%.1fw", num / 10000.0)
        } else {
            return "" + num
        }
    }
}
