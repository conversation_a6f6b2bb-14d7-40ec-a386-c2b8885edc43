package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.CountdownVOV2
import groovy.mgj.app.vo.PriceBannerVOV2
import groovy.mgj.app.vo.StockProgressVO
import groovy.mgj.app.vo.Tools

import java.text.SimpleDateFormat

/**
 * Created by wuyi on 2018/9/27.
 */
@Translator(id = "fastbuyPriceBannerV2")
class FastbuyPricebannerV2 implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, PriceBannerVOV2> {
    // 倒计时从具体日期进入倒计时样式的阈值
    final static int COUNTDOWNTHRESHOLD = 72

    @Override
    PriceBannerVOV2 translate(FastbuyDO fastbuyDO, ItemBaseDO itemBaseDO) {
        if(!fastbuyDO){
            PriceBannerVOV2 ret = new PriceBannerVOV2()
            ret.countdown = new CountdownVOV2()
            ret.stockProgress = new StockProgressVO()
            ret.priceTag = "快抢价"
            ret.highNowPrice = itemBaseDO?.highNowPrice
            ret.lowNowPrice = itemBaseDO?.lowNowPrice
            ret.highPrice = itemBaseDO?.highPrice
            ret.lowPrice = itemBaseDO?.lowPrice
            ret.type = 1
            ret.updatePrices()
            return ret
        }

        PriceBannerVOV2 result = new PriceBannerVOV2()
        Map<String, Object> maitData = MaitUtil.getTargetedMaitData(fastbuyDO?.maitId)?.get(0)
        result.coverBg = maitData?.get("coverBg")
        result.priceColor = maitData?.get("priceColor")

        def started = isStarted(fastbuyDO)

        CountdownVOV2 countdown = new CountdownVOV2()
        countdown.countdownTitleColor = maitData?.get("countdownColor")
        countdown.threshold = COUNTDOWNTHRESHOLD

        if (!started) {
            result.type = 1

            countdown.countdown = fastbuyDO.startTime - System.currentTimeSeconds()
            countdown.countdownTitle = countdown.countdown > countdown.threshold * 60 * 60 ? "开始时间" : "距开始"
            countdown.type = 0
            // 计算出当前剩余时间
            long startTime = fastbuyDO?.startTime
            Date date = new Date(startTime * 1000)
            String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
            countdown.countdownText = dateStr
            // 未开始展示关注人数，为0不展示
            if (fastbuyDO?.noticeNum > 0) {
                result.noticeNum = formatNoticeNum(fastbuyDO?.noticeNum)
            } else {
                result.noticeNum = ""
            }
        }
        else {
            result.type = 1

            countdown.countdownTitle = "距结束"
            countdown.countdown = fastbuyDO.endTime - System.currentTimeSeconds()
            countdown.type = 1
            // 计算出当前剩余时间
            long days = countdown.countdown / (60 * 60 * 24)
            long hours = (countdown.countdown % (60 * 60 * 24)) / (60 * 60)
            countdown.countdownText = String.format("%d天%d时", days, hours)

            if(maitData?.get("isHiddenProgress") != 1) {
                StockProgressVO stockProgress = new StockProgressVO()
                stockProgress.title = "仅剩${fastbuyDO.totalStock}件"
                Integer progress = Math.ceil(fastbuyDO.progressBar * 100)
                stockProgress.progress = progress
                stockProgress.progressBgColor = maitData?.get("progressBgColor")
                stockProgress.progressValueColor = maitData?.get("progressValueColor")
                result.stockProgress = stockProgress
            }
            else {
                result.stockProgress = new StockProgressVO()
            }


            // 开始之后不展示关注人数
            result.noticeNum = ""
        }
        result.countdown = countdown

        result.priceTag =  maitData?.get("priceDesc") ? maitData?.get("priceDesc") : "快抢价"

        if (Tools.limitedDiscountState(itemBaseDO) != 0) {
            result.priceTag = result.priceTag.replace("\${stock}", "${itemBaseDO?.limitDiscountInfo?.limitCount}")
            result.priceTag = result.priceTag.replace("\${discount}", "${NumUtil.formatPriceDrawer(itemBaseDO?.limitDiscountInfo?.dicountPirce ?: 0)}")
        }
        result.highNowPrice = itemBaseDO?.highNowPrice
        result.lowNowPrice = itemBaseDO?.lowNowPrice
        result.highPrice = itemBaseDO?.highPrice
        result.lowPrice = itemBaseDO?.lowPrice

        // 如果是限量立减的话，原价的位置展示大促价
        if (Tools.limitedDiscountState(itemBaseDO) != 0 && itemBaseDO?.activityPrice) {
            result.lowPrice = NumUtil.formatNum(itemBaseDO.activityPrice / 100)
            result.highPrice = NumUtil.formatNum(itemBaseDO.activityPrice / 100)
        }

        result.updatePrices()

        return result
    }

    String formatNoticeNum(int count) {
        String str = "" + count
        if (count > 9999) {
            int tenThousand = count / 10000
            int thousand = ((int) (count % 10000)) / 1000
            str = tenThousand + "." + thousand + "w"
        }
        return str + "人关注"
    }

    static def isStarted(FastbuyDO fastbuy){
        if (!fastbuy){
            return false
        }
        def nowTime = System.currentTimeSeconds()
        return fastbuy.startTime < nowTime && nowTime < fastbuy.endTime
    }
}
