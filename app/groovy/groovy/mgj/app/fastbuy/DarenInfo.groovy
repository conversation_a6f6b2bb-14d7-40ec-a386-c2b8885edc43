package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import scala.util.parsing.combinator.testing.Str

/**
 * Created by wuyi on 2019-07-24.
 */
@Translator(id = "darenInfo", defaultValue = DefaultType.EMPTY_MAP)
class DarenInfo implements ITwoDependTranslator<ExtraDO, FastbuyDO, VO> {
    public static final long maitId = 138701L

    @Override
    VO translate(ExtraDO extraDO, FastbuyDO fastbuyDO) {
        // 新品快时尚的快抢才返回这个
        if (extraDO?.getDarenInfo() && "newFashionBuy" == fastbuyDO?.getExtra()?.get("bizType")) {
            com.mogujie.detail.module.extra.domain.DarenInfo darenInfo = extraDO?.getDarenInfo()
            List<Map<String, Object>> list = MaitUtil.getMaitData(maitId)
            if (list) {
                Map<String, Object> foundDaren = null
                for (Map<String, Object> map : list) {
                    long maitTag
                    try {
                        maitTag = Long.parseLong(String.valueOf(map?.get("userid")))
                    } catch(Throwable ignore) {
                        maitTag = darenInfo?.id - 1
                    }
                    if (maitTag == darenInfo?.id) {
                        foundDaren = map
                        break
                    }
                }
                if (foundDaren) {
                    String subtitle
                    if (foundDaren?.get("uesrAttestation")) {
                        subtitle = "${foundDaren?.get("uesrAttestation")} | 种草力${darenInfo?.power}"
                    } else {
                        subtitle = "种草力${darenInfo?.power}"
                    }
                    return new VO(
                            avatar: darenInfo?.avatar,
                            name: darenInfo?.name,
                            tag: foundDaren?.get("userTitle"),
                            tagColor: foundDaren?.get("titleColor"),
                            subTitle: subtitle,
                            subTitleColor: foundDaren?.get("attestationColor"),
                            icon: foundDaren?.get("attestationIcon"),
                            background: foundDaren?.get("background"),
                            link: foundDaren?.get("link")
                    )
                }
            }
        }
        return VO.EMPTY
    }

    static class VO {
        String avatar
        String name
        String tag
        String icon
        String subTitle
        String background
        String link
        String tagColor
        String subTitleColor

        public static final VO EMPTY = new VO()
    }
}
