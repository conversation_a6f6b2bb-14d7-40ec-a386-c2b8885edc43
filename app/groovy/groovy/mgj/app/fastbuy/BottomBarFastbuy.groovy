package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.fastbuy.common.FastbuyState
import groovy.mgj.app.fastbuy.common.FastbuyImUrlGenerator

@Translator(id = "bottomBarFastbuy")
class BottomBarFastbuy implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, BottomBarRushVO> {


    class BottomBarRushVO {
        String imUrl
        String buttonTitle
        Integer rushState
        String shopUrl
        String bgColor
        String textColor = "#FFFFFF"
    }

    /**
     * rushState
     * 0. 未开始,距离开始时间大于5分钟
     * 1. 活动中
     * 2. 活动中, 但是库存为0，并且没有未付款人数
     * 3. 活动结束 （这个状态不会使用）
     * 4. 活动中, 但是库存为0，并且有未付款人数
     * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
     */

    static def buttonTitleMap = [
            (FastbuyState.ACTIVITY_NOT_STARTED): "即将开抢",
            (FastbuyState.ACTIVITY_STARTED)    : "立即抢购",
            (FastbuyState.OUT_OF_STOCK)        : "已抢完",
            (FastbuyState.ACTIVITY_ENDED)      : "抢购结束",
            (FastbuyState.WAIT_FOR_STOCK)      : "还有机会 点击刷新",
            (FastbuyState.ACTIVITY_WARM_UP)    : "马上开抢 立即刷新",
    ]

    static def buttonBGColorMap = [
            (FastbuyState.ACTIVITY_NOT_STARTED) : "#D2D2D2",
            (FastbuyState.ACTIVITY_STARTED): "#FF5777",
            (FastbuyState.OUT_OF_STOCK): "#D2D2D2",
            (FastbuyState.ACTIVITY_ENDED): "#D2D2D2",
            (FastbuyState.WAIT_FOR_STOCK): "#FF5777",
            (FastbuyState.ACTIVITY_WARM_UP): "#FF5777",
    ]

    @Override
    BottomBarRushVO translate(ItemBaseDO input1, FastbuyDO input2) {
        Integer rushState = input2 ? input2.getState() : 3
        //如果商品状态不是正常在售状态
        if (input1?.state != 0 && input1?.state != 2) {
            rushState = 3
        }
        new BottomBarRushVO(
                imUrl: FastbuyImUrlGenerator.generateMGJImUrl(input1, input2),
                rushState: rushState,
                shopUrl: "mgj://shop?shopId=${input1?.shopId}",
                buttonTitle: buttonTitleMap[rushState],
                bgColor: buttonBGColorMap[rushState]
        )
    }
}