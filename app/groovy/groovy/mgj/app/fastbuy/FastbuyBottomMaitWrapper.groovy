package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.MaitUtil

@Translator(id = "fastbuyBottomMaitWrapper")
class FastbuyBottomMaitWrapper implements IZeroDependTranslator<FastbuyBottomMaitWrapperVO> {
    class FastbuyBottomMaitWrapperVO {
        Map<String, String> fastbuyBottomMaitLeft
        Map<String, String> fastbuyBottomMaitRight
    }
    @Override
    FastbuyBottomMaitWrapperVO translate() {
        def left = MaitUtil.getMaitData(106135)?.get(0)
        def right = MaitUtil.getMaitData(106135)?.get(1)
        def vo = new FastbuyBottomMaitWrapperVO()
        if (left?.get("image")) {
            vo.fastbuyBottomMaitLeft = ["imgUrl":left.get("image"), "link":left.get("link")?:""]
        }
        if (right?.get("image")) {
            vo.fastbuyBottomMaitRight = ["imgUrl":right.get("image"), "link":right.get("link")?:""]
        }
        return vo
    }
}
