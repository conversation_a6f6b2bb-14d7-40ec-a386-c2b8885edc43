package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.SKULinkVO
import groovy.mgj.app.fastbuy.common.FastbuyState
import groovy.mgj.app.common.SKUSelect

@Translator(id = "skuSelectFastbuy")
class SKULinkFastbuy implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, Object> {
    @Override
    SKULinkVO translate(SkuDO input1, ItemBaseDO input2, FastbuyDO input3) {
        if (input3) {
            //快抢要细分几种状态，除了可以抢购的状态其他都要隐藏SKU
            if (input3.state != FastbuyState.ACTIVITY_STARTED) {
                return new SKULinkVO()
            }
        }
        return new SKUSelect().translate(input1, input2)
    }
}
