package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.ShareIntegralProvider
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.fastbuy.common.FastbuyImUrlGenerator
import groovy.mgj.app.fastbuy.common.FastbuyState
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/11/1.
 */
@Translator(id = "bottomBarFastbuyV2", defaultValue = DefaultType.EMPTY_MAP)
class BottomBarFastbuyV2 implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, BottomBarFastbuyV2VO> {
    static class BottomBarFastbuyV2VO {
        /**
         * 跳转到IM的短链
         */
        String imUrl
        /**
         * 当前的按钮文案
         */
        String buttonTitle
        /**
         * 1580新增的券后价
         */
        String leftButtonTitle;
        /**
         * 快抢状态
         */
        Integer rushState
        /**
         * 跳转到小店的短链
         */
        String shopUrl
        /**
         * 按钮背景色
         */
        String bgColor
        /**
         * 文案颜色
         */
        String textColor = "#FFFFFF"
        /**
         * 快抢活动ID
         */
        String activityId
        /**
         * 是否设置提醒，0-未设置提醒  1-已经设置提醒
         */
        int remindState
        /**
         * 设置过提醒的样式
         */
        ButtonStyleDTO remindedStyle
        /**
         * 未设置提醒的样式
         */
        ButtonStyleDTO unRemindStyle
        /**
         * 写入日历的设置提醒文案
         */
        RemindInfoDTO remindInfo
        /**
         * 分享按钮图标
         */
        String shareIconUrl
        /**
         * 是否展示分享按钮
         */
        boolean showShare = false
        /**
         * 是否开启分享积分
         */
        boolean shareIntegralOpen
    }

    static class ButtonStyleDTO {
        /**
         * 文案颜色
         */
        String textColor
        /**
         * 背景色
         */
        String bgColor
        /**
         * 按钮文案
         */
        String buttonTitle

        ButtonStyleDTO (int remindState) {
            if (Tools.getAppVersion() >= 1580) {
                if (remindState == 1) {
                    this.textColor = "#FF5777"
                    this.bgColor = "#FFE6E8"
                    this.buttonTitle = '取消提醒'
                } else {
                    this.textColor = '#FFFFFF'
                    this.bgColor = ""
                    this.buttonTitle = '设置提醒'
                }
            } else {
                if (remindState == 1) {
                    this.textColor = '#FF5777'
                    this.bgColor = '#FFE6E8'
                    this.buttonTitle = '取消提醒'
                } else {
                    this.textColor = '#FFFFFF'
                    this.bgColor = '#FF5777'
                    this.buttonTitle = '设置提醒'
                }
            }
        }
    }

    static class RemindInfoDTO {
        /**
         * 设置提醒的标题
         */
        String remindTitle
        /**
         * 设置提醒的内容
         */
        String remindContent
        /**
         * 开始时间戳
         */
        Long startTime
        /**
         * 提示时间戳
         */
        Long alarmTime
        /**
         * 设置提醒文案
         */
        String remindSuccessToast
        /**
         * 取消提醒文案
         */
        String unRemindSuccessToast
        /**
         * 短链链接
         */
        String itemUrl
        /**
         * iid
         */
        String iid
        /**
         * 结束时间戳
         */
        Long endTime
    }


    /**
     * rushState
     * 0. 未开始,距离开始时间大于5分钟。注意此时有两个可能的样式：设置提醒/取消提醒
     * 1. 活动中
     * 2. 活动中, 但是库存为0，并且没有未付款人数
     * 3. 活动结束 （这个状态不会使用）
     * 4. 活动中, 但是库存为0，并且有未付款人数
     * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
     */

    static def buttonTitleMap = [
            (FastbuyState.ACTIVITY_NOT_STARTED): "即将开抢",
            (FastbuyState.ACTIVITY_STARTED)    : "立即抢购",
            (FastbuyState.OUT_OF_STOCK)        : "已抢完",
            (FastbuyState.ACTIVITY_ENDED)      : "抢购结束",
            (FastbuyState.WAIT_FOR_STOCK)      : "还有机会 点击刷新",
            (FastbuyState.ACTIVITY_WARM_UP)    : "马上开抢 立即刷新",
    ]

    static def buttonBGColorMap = [
            (FastbuyState.ACTIVITY_NOT_STARTED) : "#D2D2D2",
            (FastbuyState.ACTIVITY_STARTED): "#FF5777",
            (FastbuyState.OUT_OF_STOCK): "#D2D2D2",
            (FastbuyState.ACTIVITY_ENDED): "#D2D2D2",
            (FastbuyState.WAIT_FOR_STOCK): "#FF5777",
            (FastbuyState.ACTIVITY_WARM_UP): "#FF5777",
    ]

    @Override
    BottomBarFastbuyV2VO translate(SkuDO skuDO, ItemBaseDO itemBaseDO, FastbuyDO fastbuyDO) {
        Integer rushState = fastbuyDO ? fastbuyDO.getState() : 3
        //如果商品状态不是正常在售状态
        if (itemBaseDO?.state != 0 && itemBaseDO?.state != 2) {
            rushState = 3
        }
        BottomBarFastbuyV2VO vo = new BottomBarFastbuyV2VO(
                showShare: false,
                shareIconUrl: ShareIntegralProvider.getShareIconUrl(itemBaseDO),
                imUrl: FastbuyImUrlGenerator.generateMGJImUrl(itemBaseDO, fastbuyDO),
                rushState: rushState,
                shopUrl: "mgj://shop?shopId=${itemBaseDO?.shopId}",
                buttonTitle: buttonTitleMap[rushState],
                bgColor: buttonBGColorMap[rushState],
                activityId: fastbuyDO?.activityId ? fastbuyDO?.activityId : "", //String类型，内容是数字
                shareIntegralOpen: ShareIntegralProvider.canGainIntegral(itemBaseDO)
        )
        // 如果有券后价，那么加上按钮左边的文案
        def discountPrice = skuDO?.promotionPrice
        if (discountPrice && discountPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal) {
            vo.leftButtonTitle = "券后¥${NumUtil.formatPriceDrawer(discountPrice.intValue())}"
        } else {
            vo.leftButtonTitle = ""
        }


        //当非快抢/快抢已结束的时候，也要 return 一个，防止后面的代码异常出现 return {} 导致客户端bottomBar不展示的问题
        if (!fastbuyDO) {
            return vo
        }

        //获取String类型的fastbuyId
        def fastbuyId = FastbuyIdConvertor.convertFastbuyId(fastbuyDO?.activityId)
        def nativeURL = URLEncoder.encode("mgj://detail?detailType=FastBuy&iid=${itemBaseDO?.iid}&fastbuyId=${fastbuyId}", "UTF-8")
        def itemUrl = StrategyUpUtil.upUrl("mogujie://open?url=${nativeURL}")
        def alarmDiff = 3 //客户端要求给整数分钟
        vo.remindInfo = new RemindInfoDTO(
                remindTitle:"快抢提醒：${itemBaseDO.title}",
                remindContent:"【蘑菇街快抢提醒】：${itemBaseDO.title} ${itemUrl}",
                startTime:fastbuyDO?.startTime,
                alarmTime:fastbuyDO?.startTime - alarmDiff*60,
                remindSuccessToast: "设置成功，提前${alarmDiff}分钟开售提醒",
                unRemindSuccessToast: "取消提醒成功",
                itemUrl:itemUrl,
                iid: itemBaseDO?.iid,
                endTime: fastbuyDO?.endTime
        )
        vo.remindState = fastbuyDO?.isFollowed() ? 1 : 0 //是否设置提醒，0-未设置提醒  1-已经设置提醒

        vo.remindedStyle = new ButtonStyleDTO(1)
        vo.unRemindStyle = new ButtonStyleDTO(0)

        if (vo.rushState == FastbuyState.ACTIVITY_NOT_STARTED) {
            ButtonStyleDTO style = vo.remindState ? vo.remindedStyle : vo.unRemindStyle
            vo.buttonTitle = style.buttonTitle
            vo.bgColor = style.bgColor
            vo.textColor = style.textColor
        }

        return vo
    }
}
