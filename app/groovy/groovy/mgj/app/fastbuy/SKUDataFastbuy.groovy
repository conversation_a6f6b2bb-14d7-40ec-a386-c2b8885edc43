package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.common.SKUData

@Translator(id = "skuDataFastbuy")
class SKUDataFastbuy implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, Object> {

    private SKUData skuData;

    public SKUDataFastbuy() {
        skuData = new SKUData()
    }

    @Override
    SKUDataVO translate(SkuDO input1, ItemBaseDO input2, FastbuyDO input3) {

        def result = skuData.translate(input1, input2, null)
        if (input3) {
            //加入快抢下单参数
            result.channel = "channel_kuaiqiang"
            result.extensions = [
                    "activityId"  : FastbuyIdConvertor.convertFastbuyId(input3.activityId),
                    "activityType": "1",
            ]
        }
        return result
    }
}
