package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.VersionController
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.common.common.SummaryRateInfoVO
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

@Translator(id = "summaryFastbuy")
class SummaryFastbuy implements IFiveDependTranslator<ItemBaseDO, RateDO, ShopDO, FastbuyDO, SkuDO,Object> {

    class SummaryVO extends ItemPriceVO {
        String title
        String priceColor
        PriceTagVO priceTag
        ShareInfo shareInfo
        SummaryRateInfoVO rate
        PriceTagVO eventPriceTag
        String shareUrl
        Boolean isShowPrice
        String titleIcon
        String installment
        TextStyle installmentStyle // 名字跟着上面
        List<String> titleIconsList
    }

    static String defaultTextColor = "#FF2255"
    static String defaultBgColor = "#FFE8EE"

    class PriceTagVO {
        String text
        String textColor = defaultTextColor
        String bgColor = defaultBgColor
    }

    class ShareInfo {
        String shareIcon
        String shareText
    }

    @Override
    SummaryVO translate(ItemBaseDO input1, RateDO rateDO, ShopDO shopDO, FastbuyDO fastbuyDO,SkuDO skuDO) {
        if (OfficialRecommendFastbuy.shouldShow(input1, fastbuyDO)) {
            return new SummaryVO()
        }

        if (!input1) {
            //业务上可以return null
            return null
        }
        def summary = new SummaryVO()
        summary.title = input1.title
        summary.highNowPrice = input1.highNowPrice
        summary.lowNowPrice = input1.lowNowPrice
        summary.highPrice = input1.highPrice
        summary.lowPrice = input1.lowPrice
        summary.updatePrices()

        summary.priceColor = "#333333"
        summary.title = input1.title
        summary.priceTag = new PriceTagVO(
                text: '快抢价',
        )

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        summary.installment = hintWrapper?.hint
        summary.installmentStyle = hintWrapper?.textStyle

        summary.installment = summary.installment ?: ""

        summary.isShowPrice = false
        Map<String, Object> maitData = MaitUtil.getTargetedMaitData(fastbuyDO?.maitId)?.get(0)
        summary.titleIcon=maitData?.get("titleIcon")
        summary.titleIconsList = []
        // 只有快抢标
        if (!TextUtils.isEmpty(summary.titleIcon)) {
            summary.titleIconsList.add(summary.titleIcon)
        }

        if (Tools.isVirtualCouponItem()) {
            summary.eventPriceTag = new PriceTagVO(
                    text: "购买后自动发券，不支持退款"
            )
        }

        //去麦田获取分享Icon和Text
        def shareMaitInfo = MaitUtil.getMaitData(38873)
        if (shareMaitInfo?.get(0)) {
            summary.shareInfo = new ShareInfo(
                    shareIcon : shareMaitInfo.get(0)?.get("shareIcon"),
                    shareText : shareMaitInfo.get(0)?.get("shareText")
            );
        }
        //非ESI时才返回
        if (!DetailContextHolder.get().isDyn()) {
            //DSR信息
            if (rateDO != null && input1 != null) {
                SummaryRateInfoVO summaryRateInfoVO = new SummaryRateInfoVO(input1, rateDO, shopDO)
                summary.rate = summaryRateInfoVO
            }
        }

        if (summary.eventPriceTag == null) {
            summary.eventPriceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
        }

        // 跟share组件里的url一样的逻辑
        def fastbuyId = FastbuyIdConvertor.convertFastbuyId(fastbuyDO?.activityId)
        def url = StrategyUpUtil.upUrl("http://m.mogujie.com/rush/seckill/${fastbuyId}?itemId=${input1?.iid}")
        summary.shareUrl = url

        // for esi merge
        summary.with {
            if (titleIconsList == null) {
                titleIconsList = []
            }
        }

        return summary
    }
}
