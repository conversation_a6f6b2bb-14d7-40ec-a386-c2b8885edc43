package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

import groovy.mgj.app.vo.PriceBannerVO

@Translator(id = "fastbuyPriceBannerGroup")
class FastbuyPriceBannerGroup implements ITwoDependTranslator<FastbuyDO,ItemBaseDO, FastbuyPriceBannerGroupVO> {

    static class FastbuyPriceBannerGroupVO {
        PriceBannerVO priceBanner
    }
    @Override
    FastbuyPriceBannerGroupVO translate(FastbuyDO input1,ItemBaseDO itemBase) {
        FastbuyPriceBanner translator = new FastbuyPriceBanner()
        PriceBannerVO priceBannerVO = translator.translate(input1,itemBase)
        if (priceBannerVO == null) {
            return new FastbuyPriceBannerGroupVO()
        }
        else {
            return new FastbuyPriceBannerGroupVO(
                    priceBanner: priceBannerVO
            )
        }
    }

}
