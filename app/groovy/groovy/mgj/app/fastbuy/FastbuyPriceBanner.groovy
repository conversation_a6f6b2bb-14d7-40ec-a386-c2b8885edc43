package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.CountdownVO
import groovy.mgj.app.vo.PriceBannerVO
import groovy.mgj.app.vo.StockProgressVO

@Translator(id = "fastbuyPriceBanner")
class FastbuyPriceBanner implements ITwoDependTranslator<FastbuyDO,ItemBaseDO, PriceBannerVO> {

    @Override
    PriceBannerVO translate(FastbuyDO input1,ItemBaseDO itemBase) {
        if(!input1){
            PriceBannerVO ret = new PriceBannerVO()
            ret.countdown = new CountdownVO()
            ret.stockProgress = new StockProgressVO()
            return ret
        }

        PriceBannerVO result = new PriceBannerVO()
        Map<String, Object> maitData = MaitUtil.getTargetedMaitData(input1?.maitId)?.get(0)
        result.coverBg = maitData?.get("coverBg")
        result.priceColor = maitData?.get("priceColor")

        def started = isStarted(input1)

        CountdownVO countdown = new CountdownVO()
        countdown.countdownTitleColor = maitData?.get("countdownColor")

        if (!started) {
            countdown.countdownTitle = "距开始仅剩"
            countdown.countdown = input1.startTime - System.currentTimeSeconds()
            // 未开始展示关注人数，为0不展示
            if (input1?.noticeNum > 0) {
                result.noticeNum = formatNoticeNum(input1?.noticeNum)
            } else {
                result.noticeNum = ""
            }
        }
        else {
            countdown.countdownTitle = "距结束仅剩"
            countdown.countdown = input1.endTime - System.currentTimeSeconds()

            if(maitData?.get("isHiddenProgress") != 1) {
                StockProgressVO stockProgress = new StockProgressVO()
                stockProgress.title = "仅剩${input1.totalStock}件"
                Integer progress = input1.allStock == 0 ? 0 : Math.ceil((input1.allStock - input1.totalStock) * 100 / input1.allStock)
                stockProgress.progress = progress
                stockProgress.progressBgColor = maitData?.get("progressBgColor")
                stockProgress.progressValueColor = maitData?.get("progressValueColor")
                result.stockProgress = stockProgress
            }
            else {
                result.stockProgress = new StockProgressVO()
            }

            // 开始之后不展示关注人数
            result.noticeNum = ""
        }
        result.countdown = countdown

        result.priceTag =  maitData?.get("priceDesc") ? maitData?.get("priceDesc") : "快抢价"
        result.highNowPrice = itemBase?.highNowPrice
        result.lowNowPrice = itemBase?.lowNowPrice
        result.highPrice = itemBase?.highPrice
        result.lowPrice = itemBase?.lowPrice
        result.updatePrices()

        return result
    }

    String formatNoticeNum(int count) {
        String str = "" + count
        if (count > 9999) {
            str = count / 10000 + ""
            str += "." + (count % 10000) / 1000
            str += "w"
        }
        return str + "人关注"
    }

    static def isStarted(FastbuyDO fastbuy){
        if (!fastbuy){
            return false
        }
        def nowTime = System.currentTimeSeconds()
        return fastbuy.startTime < nowTime && nowTime < fastbuy.endTime
    }
}