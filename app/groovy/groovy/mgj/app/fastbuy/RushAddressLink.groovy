package groovy.mgj.app.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator

@Translator(id = "fastbuyAddress")
class RushAddressLink implements IZeroDependTranslator<AddressLinkVO> {
    class AddressLinkVO {
        String bgColor
        String textColor
        String title
        String link
    }

    @Override
    AddressLinkVO translate() {
        new AddressLinkVO(
                title: "提前设置默认地址，抢货更快",
                link: "mgj://address?login=1",
                bgColor: "#F7F2FF",
                textColor: "#6731BD"
        )
    }
}