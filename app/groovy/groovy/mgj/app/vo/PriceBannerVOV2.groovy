package groovy.mgj.app.vo

/**
 * Created by wuy<PERSON> on 2018/9/9.
 */
class PriceBannerVOV2 extends ItemPriceVO {
    String coverBg
    String priceColor
    String priceTag
    CountdownVOV2 countdown

    // 1120后添加部分
    int type  // 0 normal 1 活动正式期  2 预热  3 比价
    String eventPrice
    List<PriceTagVO> eventTags
    WarmUpInfo warmUpInfo

    BiJiaVO biJia // when type == 3

    // 表示当前是什么活动，以便其他组件需要知道当前是什么活动。实际还不全面
    ActivityType activityType = ActivityType.NORMAL

    // 券后价相关
    String discountPrice
    String discountPriceColor
    String discountPriceBgColor

    // FastbuyPriceBannerV2 在使用，没办法得留着
    String noticeNum
    StockProgressVO stockProgress
}
