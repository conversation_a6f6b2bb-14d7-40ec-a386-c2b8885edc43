package groovy.mgj.app.vo

import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO

/**
 * Created by fuf<PERSON> on 2017/6/29.
 * 团购类型
 */
enum GroupBuyType {
    NORMAL,
    UZHI,
    STORE,
    PINPAI

    static GroupBuyType getGroupBuyType(GroupbuyingDO groupbuyingDO) {
        if (groupbuyingDO.bizType == TuanBizType.UZHI) {
            return UZHI
        }
        else if (groupbuyingDO.type == TuanType.STORE) {
            return STORE
        }
        else if (groupbuyingDO.bizType == TuanBizType.PINPAI) {
            return PINPAI
        }
        else if (groupbuyingDO.bizType == TuanBizType.NORMAL){
            return NORMAL
        }
    }
}