package groovy.mgj.app.vo

class NormalBottombarVO {
    /**
     * ===========================左边按钮
     * 1001  —  收藏
     * 1002  —  小店
     * 1003  —  加购(拼团)
     * 1004  —  直播（已废弃）
     * 1005  —  IM
     * 1006  —  主播（已废弃）
     * 1080  —  左边通用按钮
     * ===========================右边按钮
     * 2001  —  加购(普通)
     * 2002  —  立即购买
     * 2003  —  单独购买（拼团）
     * 2004  —  拼团购买
     * 2005  —  预售
     * 2006  —  待售
     * 2007  -  新人价购买
     * 2080  —  右侧通用按钮
     * ===========================悬浮按钮
     * 3001  —  私聊
     * 3080  —  悬浮通用按钮
     */
    public static final int LEFT_FAVED = 1001
    public static final int LEFT_SHOP = 1002
    public static final int LEFT_ADDCART = 1003
//    public static final int LEFT_LIVE = 1004 已废弃
    public static final int LEFT_IM = 1005
//    public static final int LEFT_ANCHOR = 1006 已废弃
    public static final int LEFT_BUYER_SHOP = 1007
    public static final int LEFT_COMMON = 1080
    public static final int RIGHT_NORMAL_ADDCART = 2001
    public static final int RIGHT_BUY = 2002
    public static final int RIGHT_NORMAL_BUY = 2003
    public static final int RIGHT_PINTUAN_BUY = 2004
    public static final int RIGHT_PRE_SALE = 2005
    public static final int RIGHT_WAIT_FOR_SALE = 2006
    public static final int RIGHT_NEW_COMER_BUY = 2007
    public static final int RIGHT_COMMON = 2080
    public static final int FLOAT_IM = 3001
    public static final int FLOAT_SHARE = 3002
    public static final int FLOAT_COMMON = 3080
    public static final int TYPE_INVALID = -1

    static List<BottomBarItemData> composeRightList(List itemList) {
        List<BottomBarItemData> rightButtonList = new ArrayList<>()
        for (Object item : itemList) {
            if (item instanceof BottomBarItemData) {
                if (isRightButton(item.type)) {
                    rightButtonList?.add(item)
                } else {
                    // invalid type
                }
            } else if (item instanceof Integer) {
                int type = (int) item
                if (isRightButton(type)) {
                    rightButtonList?.add(new BottomBarItemData(type: type))
                } else {
                    // invalid type
                }
            } else {
                // invalid param
            }
        }
        return rightButtonList
    }

    static List<BottomBarItemData> composeLeftList(List itemList) {
        List<BottomBarItemData> leftButtonList = new ArrayList<>()
        for (Object item : itemList) {
            if (item instanceof BottomBarItemData) {
                if (isLeftButton(item.type)) {
                    leftButtonList?.add(item)
                } else {
                    // invalid type
                }
            } else if (item instanceof Integer) {
                int type = (int) item
                if (isLeftButton(type)) {
                    leftButtonList?.add(new BottomBarItemData(type: type))
                } else {
                    // invalid type
                }
            } else {
                // invalid param
            }
        }
        return leftButtonList
    }

    static boolean isLeftButton(int type) {
        return type > 1000 && type < 2000
    }

    static boolean isRightButton(int type) {
        return type > 2000 && type < 3000
    }

    static boolean isFloatButton(int type) {
        return type > 3000 && type < 4000
    }


    // 左边展示按钮
    List<BottomBarItemData> leftButtonList
    // 右边展示按钮
    List<BottomBarItemData> rightButtonList
    // 悬浮按钮
    List<BottomBarItemData> floatButtonList
    String iid
    String imUrl
    String shopUrl
    String msdUrl
    String shopId
    boolean isFaved
    boolean addCartTips
//    int saleType /** 按理说不需要 */
//    int state /** 按理说不需要 **/
    long saleStartTime
    /** 待售商品日历提醒标题 */
    String waitForSaleNoticeTitle
    /** 待售商品日历提醒内容 */
    String waitForSaleNoticeContent
    /** 待售商品url */
    String itemURL
    int addCartSkuCommunicationType

    BottomBarPintuanButtonVO normalBuy
    BottomBarPintuanButtonVO pintuanBuy
    //以下字段从9.5.0版本开始生效
    /**
     * 普通商品底部按钮文案，一般情况下都是"立即购买"，U质团商品在正式期展示为"参团购买"
     */
    String buyButtonText
    /**
     * H5下单页的BaseURL
     */
    String buyBaseUrl
    /**
     * 普通购买参数
     */
    String normalBuyParams
    /**
     * 拼团购买参数
     */
    String pintuanBuyParams
    /**
     * ptp参数占位符字段
     */
    String ptpPlaceHolder
    /**
     * liveParams占位符字段
     */
    String liveParamsPlaceHolder
    /**
     * fashionParams占位符字段
     */
    String fashionPlaceHolder

//    以下字段已废弃
//    boolean isLive
//    String liveRoomUrl

    /**
     * 是否开启分享积分
     */
    boolean shareIntegralOpen

    // 以下字段从1260开始生效
    IMTips imTips
    List<String> IMText

    //以下字段从1270开始生效
    CouponBubbleVO couponBubble
}
