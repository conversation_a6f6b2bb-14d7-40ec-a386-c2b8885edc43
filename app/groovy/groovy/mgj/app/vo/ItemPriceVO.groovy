package groovy.mgj.app.vo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

class ItemPriceVO {

    transient String highNowPrice
    transient String highPrice
    transient String lowNowPrice
    transient String lowPrice

    String price
    String oldPrice

    // forceNoRange: 强制使用“¥xxx起”的格式，而不是区间
    def updatePrices(boolean forceNoRange = false, boolean noZeroSuffix = false) {
        (oldPrice, price) = ItemPriceCalculator.calculatePrices(this, forceNoRange, noZeroSuffix)
        return this
    }

    def mayHideOldPrice(ItemBaseDO itemBaseDO) {
        if (itemBaseDO == null
                || itemBaseDO.canShowStrikethroughPrice) {
            return this
        }
        oldPrice = ""
        return this
    }

    class ItemPriceCalculator {
        static def wipeZeroSuffix(String price) {
            if (!price.contains(".")) {
                return price
            }
            while (price.length() > 0 && price.endsWith("0")) {
                price = price.substring(0, price.length() - 1)
            }
            if (price.endsWith(".")) {
                return price.substring(0, price.length() - 1)
            }
            return price
        }

        static def calculatePrices(ItemPriceVO priceVO, boolean forceNoRange, boolean noZeroSuffix) {
            String oldPrice
            String price

            if (noZeroSuffix) {
                priceVO.lowPrice = wipeZeroSuffix(priceVO.lowPrice)
                priceVO.highPrice = wipeZeroSuffix(priceVO.highPrice)
                priceVO.lowNowPrice = wipeZeroSuffix(priceVO.lowNowPrice)
                priceVO.highNowPrice = wipeZeroSuffix(priceVO.highNowPrice)
            }

            // oldPrice
            if (priceVO.lowPrice == priceVO.lowNowPrice && priceVO.highPrice == priceVO.highNowPrice) {
                oldPrice = null
            } else {
                oldPrice = "¥" + priceVO.lowPrice
                if (priceVO.lowPrice != priceVO.highPrice) {
                    String osV = DetailContextHolder.get().getOsVersion();
                    if (!forceNoRange && (osV != null && osV.startsWith("10.3") && DetailContextHolder.get().getParam("template").startsWith("1-4"))) {
                        oldPrice = oldPrice + "~¥" + priceVO.highPrice;
                    } else {
                        oldPrice = oldPrice + "起";
                    }
                }
            }

            // price
            if (priceVO.lowNowPrice != priceVO.highNowPrice) {
                if (forceNoRange) {
                    price = "¥" + priceVO.lowNowPrice + "起"
                } else {
                    price = "¥" + priceVO.lowNowPrice + "~¥" + priceVO.highNowPrice
                }
            } else {
                price = "¥" + priceVO.lowNowPrice
            }

            return new Tuple2(oldPrice, price)
        }
    }
}