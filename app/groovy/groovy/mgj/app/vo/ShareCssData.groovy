package groovy.mgj.app.vo

import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by w<PERSON><PERSON> on 2020/3/16.
 *
 * 分享到小程序的csslayout方式需要的数据
 * 注意客户端是使用map<String, String>的形式接这个数据的
 */
class ShareCssData {
    ShareCssData(ItemBaseDO itemBaseDO, ExtraDO extraDO, ShareVO shareVO) {
        if (itemBaseDO?.topImages) {
            imgOne = itemBaseDO?.topImages?.get(0)
            if (itemBaseDO.topImages.size() > 1) {
                imgTwo = itemBaseDO?.topImages?.get(1)
            } else {
                imgTwo = ""
            }
        }
        long salesCount = extraDO?.sales ?: 0
        sales = String.valueOf(salesCount)
        collect = String.valueOf(itemBaseDO.getCFav())

        decorateWithOldData(shareVO)
    }
    /**
     * 商品图1
     */
    String imgOne
    /**
     * 商品图2
     */
    String imgTwo
    /**
     * 优惠信息。7折/品牌促销之类
     */
    String discount
    /**
     * 优惠的价格
     */
    String cutPrice
    /**
     * 现价，不带羊角符
     */
    String nowPrice
    /**
     * 原价，不带羊角符
     */
    String oldPrice
    /**
     * 收藏数
     */
    String collect
    /**
     * 销量
     */
    String sales
    /**
     * 是否区间价。根据现价决定
     */
    boolean isRangePrice
    /**
     * 商品原标题
     */
    String itemTitle

    private void decorateWithOldData(ShareVO shareVO) {
        if (shareVO.oldPrice) {
            oldPrice = shareVO.oldPrice.split("~")[0].replace("¥", "")
            oldPrice = oldPrice.replace("起", "")
        }
        if (shareVO.price) {
            nowPrice = shareVO.price.split("~")[0].replace("¥", "")
            nowPrice = nowPrice.replace("起", "")
            isRangePrice = shareVO.price.contains("~") || shareVO.price.contains("起")
        }
        cutPrice = parseCutPrice(oldPrice, nowPrice)
        itemTitle = shareVO.itemTitle
    }

    private static String parseCutPrice(String oldPrice, String nowPrice) {
        if (!oldPrice || !nowPrice || oldPrice == nowPrice) {
            return ""
        }
        try {
            int oldPriceCent = toCent(oldPrice)
            int nowPriceCent = toCent(nowPrice)
            int cutCent = oldPriceCent - nowPriceCent
            return NumUtil.formatPriceDrawer(cutCent)
        } catch (Exception ignore) {
            return ""
        }
    }

    private static int toCent(String price) {
        String[] s = price.split("\\.")
        int cent = 0
        if (s.length > 0) {
            cent += Integer.parseInt(s[0]) * 100
        }
        if (s.length > 1) {
            cent += Integer.parseInt(s[1])
        }
        return cent
    }
}
