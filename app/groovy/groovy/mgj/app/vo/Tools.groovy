package groovy.mgj.app.vo

import com.mogujie.detail.core.adt.DetailContext
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.core.util.TagUtil
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ImageInfo
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.shop.util.WaitressUtil
import com.mogujie.detail.urlhub.DetailUrlProvider
import com.mogujie.detail.urlhub.constants.App
import com.mogujie.detail.urlhub.constants.Biz
import com.mogujie.detail.urlhub.constants.Platform
import com.mogujie.detail.urlhub.params.DetailUrlQueryOptions
import com.mogujie.marketing.veyron.BizTagUtils
import com.mogujie.marketing.veyron.enums.BizTagTypeEnum
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail
import groovy.mgj.app.common.ContextParamsConst
import groovy.mgj.app.common.DetailType
import groovy.mgj.app.common.common.Source
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.web.util.UriComponentsBuilder

import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 通用工具辅助方法
 * Created by anshi on 17/3/31.
 */
class Tools {
    // 好店优选的标
    static final int HAO_DIAN_YOU_XUAN_TAG = 1523
    // 主播推荐活动的标
    static final int ZHU_BO_TUI_JIAN_TAG = 2002

    static boolean isHaoDianYouXuan(ShopDO shopDO) {
        return shopDO?.tags?.contains(HAO_DIAN_YOU_XUAN_TAG) ?: false
    }

    /**
     * 是否为良品商品
     * @param source
     */
    static isGoodItem(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        try {
            List<Integer> itemTags = new ArrayList<>();
            List<Integer> shopTags = null;
            if (itemBaseDO != null && itemBaseDO.getNumTags() != null && itemBaseDO.getNumTags().size() > 0) {
                int length = itemBaseDO.getNumTags().size()
                for (int i = 0; i < length; i++) {
                    String tag = itemBaseDO.getNumTags().get(i)
                    itemTags.add(Integer.parseInt(tag))
                }
            }
            if (shopDO != null) {
                shopTags = shopDO.getTags();
            } else {
                shopTags = Collections.EMPTY_LIST;
            }
            return BizTagUtils.checkBizTag(BizTagTypeEnum.GOOD_ITEM, itemTags, shopTags)
        } catch (Throwable e) {
            return false
        }
//        if (itemBaseDO?.itemTags?.contains(ItemTag.GOOD)
//                || ItemTag.INSTORE in itemBaseDO?.itemTags
//                || ItemTag.TRANSFER in itemBaseDO?.itemTags) {
//            return true
//        }
    }

    /**
     * 是否包含某个服务体系
     *
     * @param context
     * @return 要检查的服务体系
     */
    static boolean containsService(DetailContext context, ServiceDetailEnum service) {
        if (context == null || service == null) return false
        List<ItemServiceDetail> serviceDetails = WaitressUtil.getItemServices(context)
        if (CollectionUtils.isEmpty(serviceDetails)) {
            return false
        }
        return serviceDetails
                ?.any { itemServiceDetail -> itemServiceDetail.getServiceDetailId() == service.getDetailId() }
    }

    /**
     * 是否为U质团
     * @param groupbuyingDO
     */
    static isUZhi(GroupbuyingDO groupbuyingDO) {
        return false
    }

    static String getH5Url(Long iid) {
        DetailUrlQueryOptions options = new DetailUrlQueryOptions();
        options.setApp(App.MGJ);  // 默认 MGJ
        options.setBiz(Biz.NORMAL);  // 默认普通详情页
        options.setPlatform(Platform.H5); //默认APP
        options.setItemId(iid); // 必填项
        options.setIsExternal(false); // 默认为站内详情页
        options.setNeedEncode(true); // 默认为true, 表示需要对itemId做id2url转换
        // 切换mogu域名
        return StrategyUpUtil.upUrl(DetailUrlProvider.getInstance().getDetailUrl(options));
    }

    /**
     * 是否为拼团
     * @param itemBaseDO
     * @param extraDO
     * @param pintuanDO
     * @return
     */
    static boolean isPintuan(ItemBaseDO itemBaseDO, PinTuanDO pintuanDO) {
        return com.mogujie.detail.spi.dslutils.Tools.isPintuan(itemBaseDO, pintuanDO)
    }

    static boolean isSystemPintuan(ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO) {
        return isPintuan(itemBaseDO, pinTuanDO) && pinTuanDO?.system && (pinTuanDO?.remainTime > 0)
    }

    static int getAppVersion() {
        String av = DetailContextHolder.get().getParam("_av")
        av = StringUtils.isEmpty(av) ? "0" : av
        av = av.isNumber() ? av : "0"
        Integer avInt = av.toInteger()
        return avInt
    }

    /**
     * 前N件立减商品状态 0-不是前N件立减商品或者活动结束 1-前N件立减未开始 2-前N件立减活动中
     * @param itemBaseDO
     * @return
     */
    static int limitedDiscountState(ItemBaseDO itemBaseDO) {
        int noActivity = 0
        int preActivity = 1
        int inActivity = 2

        long currentTime = System.currentTimeSeconds()
        if (itemBaseDO?.limitDiscountInfo?.startTime
                && itemBaseDO?.limitDiscountInfo?.endTime) {
            if (currentTime < itemBaseDO?.limitDiscountInfo?.startTime) {
                return preActivity
            } else if (currentTime > itemBaseDO?.limitDiscountInfo?.endTime) {
                return noActivity
            } else {
                return inActivity
            }
        }
        return noActivity
    }

    static boolean hasDataId(String dataId) {
        com.mogujie.detail.spi.dslutils.Tools.getDSLComponents()?.contains(dataId)
    }

    // 0表示templateId == otherTemplateId
    // 1表示templateId > otherTemplateId
    // -1表示templateId < otherTemplateId
    static int compareTemplate(String templateId, String otherTemplateId) {
        try {
            String[] ids = templateId.split("\\.")
            String[] otherIds = otherTemplateId.split("\\.")
            if (ids.length != otherIds.length) return ids.length > otherIds.length ? 1 : -1
            for (int index = 0; index < ids.length && index < otherIds.length; index++) {
                int id = Integer.parseInt(ids[index])
                int otherId = Integer.parseInt(otherIds[index])
                if (id != otherId) return id > otherId ? 1 : -1
            }
            return 0
        } catch (Exception ignore) {
            return 1
        }
    }

    static String shrinkRangePrice(String price) {
        // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
        if (price?.contains("~")) {
            price = price?.replace("~¥", "~")
            // 比较一言难尽的处理
            // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
            if (price?.contains(".00~") && price?.endsWith(".00")) {
                price = price?.replace(".00", "")
            }
        }
        return price
    }

    static int parseInt(String text, int defaultVal) {
        try {
            return Integer.parseInt(text)
        } catch (Exception ignore) {
            return defaultVal
        }
    }

    static DetailType getDetailType() {
        String key = (DetailContextHolder.get()?.getParam(ContextParamsConst.TEMPLATE) ?: "")
        if (key.contains(DetailType.NORMAL.key)) {
            return DetailType.NORMAL
        } else if (key.contains(DetailType.FASTBUY.key)) {
            return DetailType.FASTBUY
        } else if (key.contains(DetailType.SECKILL.key)) {
            return DetailType.SECKILL
        } else {
            return DetailType.UNKNOW
        }
    }

    /**
     * 是否在新的直播商品进商城的主播推荐活动时间内
     * https://mogu.feishu.cn/docs/doccnv7IHgzKvw3VtVdTDuqr9Vh
     * https://mogu.feishu.cn/docs/doccnuTZP1c0zOXzS00dL89w0fg
     * @return 是否在新的直播商品进商城的主播推荐活动时间内
     */
    static boolean isInLiveRecommend() {
        DetailContext context = DetailContextHolder.get()
        return TagUtil.isContainsTag(context.getItemDO(), ZHU_BO_TUI_JIAN_TAG)
    }

    /**
     * 注意区别于liveParams，这是新的sourceParams，直播商品进商城，主播推荐活动用的
     */
    static boolean isLiveSource() {
        Source source = Source.with(DetailContextHolder.get())
        return Source.Type.Live == source?.type
    }

    // 是否有新的商品发布的图
    static boolean hasSizeHelperIMG(ItemBaseDO itemBaseDO) {
        List<ImageInfo> images = itemBaseDO?.imageInfo
        return images != null && !images.isEmpty()
    }

    // 从 CDN 图片链接中获取宽高
    static def getCDNImageWidthAndHeight(String url) {
        if (!url) {
            return new Tuple2<Integer, Integer>(0, 0)
        }

        Pattern pattern = Pattern.compile("_(\\d+)x(\\d+)\\.")
        Matcher matcher = pattern.matcher(url)

        if (!matcher.find()) {
            return new Tuple2<Integer, Integer>(0, 0)
        }

        Integer width = matcher.group(1).toInteger()
        Integer height = matcher.group(2).toInteger()

        return new Tuple2<Integer, Integer>(width, height)
    }

    static String appendRequestAcm(String target) {
        if (target == null
                || target.isEmpty()) {
            return target
        }
        String acm = DetailContextHolder.get().getParam("acm")
        if (acm == null
                || acm.isEmpty()) {
            return target
        }
        return UriComponentsBuilder.fromUriString(target)
                .queryParam("acm", acm)
                .toUriString()
    }
}