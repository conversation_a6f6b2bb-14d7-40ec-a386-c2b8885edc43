package groovy.mgj.app.vo

class SKUDataVO {
    SKUVO skuInfo
    String iid
    boolean isPresale
    String defaultImageUrl
    String mainPriceStr
    String subPriceStr

    //特殊业务下单参数
    String channel
    Map<String, String> extensions

    //SKUDataVOActivityType
    int activityType

    int skuCommunicationType

    //尺码助手
    String matchedSize
    String sizeTitle
    String sizeHelperEntrance

    //秒杀Id,只有秒杀才会返回
    String secKillId

    /**
     * 最高免息期数
     * 数据来源 SKUDO.freePhases;
     * 客户端10.3.0版本开始使用
     */
    int maxFreePhases

    /**
     * 是否锁定SKU数量为1
     * 客户端10.3.0版本开始使用
     */
    boolean isFreezing
}