package groovy.mgj.app.vo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.detail.core.adt.PromotionDecorate
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO;

class PriceCalculationDO {
    String discountDesc
    String originalPrice
    String originalPriceDesc
    List<PromotionDecorate> discountList

    PriceCalculationDO() {
        this.discountDesc = ""
        this.originalPrice = ""
        this.originalPriceDesc = ""
        this.discountList = []
    }

    PriceCalculationDO(ItemBaseDO itemBaseDO, SkuDO skuDO) {
        this()

        DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()
        this.discountList = itemDO.promotionPriceDetail

        this.discountDesc = "预估到手价 ¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}"
        if (DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal != DetailContextHolder.get()?.getItemDO()?.highNowPriceVal) {
            this.discountDesc += "起"
        }

        ItemPriceVO itemPriceVO = new ItemPriceVO()
        itemPriceVO.highNowPrice = itemBaseDO?.highNowPrice
        itemPriceVO.lowNowPrice = itemBaseDO?.lowNowPrice
        itemPriceVO.updatePrices(true)
        this.originalPrice = itemPriceVO.price

        this.originalPriceDesc = "活动价"
    }
}