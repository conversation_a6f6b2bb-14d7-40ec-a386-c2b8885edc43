package groovy.mgj.app.vo

import groovy.mgj.app.common.common.ShareGiftMaitDO

/**
 * Created by fufeng on 2017/8/24.
 */
class ShareVO extends ItemPriceVO {
    String itemTitle
    String shareText
    String url
    String imUrl
    String itemDesc
    String shopLogo
    String shopId
    Boolean inActivity
    String eventPrice
    String ownerName
    String iid
    /**
     * 是否开启分享有赏
     */
    boolean shareGiftOpen
    /**
     * 分享有赏信息实体
     */
    ShareGiftMaitDO shareBanner
    /**
     * 小程序路径
     */
    String miniProgramPath

    /**
     * 拼团分享需要的id
     * shareV3开始使用，客户端10.3.0版本开始使用
     */
    String businessId
    /**
     * 是否开启分享积分
     */
    boolean shareIntegralOpen
    /**
     * 分享积分数据
     */
    Object shareIntegral
    String saleCountDesc
    String activityIcon

    // 下面是使用csslayout分享的数据

    /**
     * 使用csslayot分享的麦田模板配置
     */
    String miniCardTempMaitID = ""

    /**
     * 分享需要的参数
     */
    String miniCardType

    /**
     * 请求上面的麦田时需要带上的参数
     */
    Map<String, String> miniCardTempMaitParams = new HashMap<>()

    /**
     * 使用csslayout分享需要的具体数据
     * 上面麦田配置的模板会通过jsonpath将这里的数据填充进去
     */
    Object miniCardData = new Object()

    /**
     * 二维码分享卡片应优先使用的商品图
     * 如为空客户端会用当前浏览中的的商品头图
     */
    String coverUrlForQRCard
}
