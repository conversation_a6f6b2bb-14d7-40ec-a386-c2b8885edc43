package groovy.mgj.app.vo

import groovy.mgj.app.common.common.SummaryRateInfoVO

/**
 * Created by fufeng on 2017/8/18.
 */

class SummaryVO extends ItemPriceVO {
    String title
    String priceColor
    PriceTagVO priceTag
    String eventPrice
    String eventPriceColor
    PriceTagVO eventPriceTag
    PriceTagVO[] eventTags
    ActivityVO activity
    ShareInfo shareInfo
    SummaryRateInfoVO rate
    String shareUrl
    boolean isShowPrice
    String titleIcon
    String installment // 名字是历史原因
    TextStyle installmentStyle // 跟着上面
    PriceTagVO eventPriceTag1110
    List<String> titleIconsList
}