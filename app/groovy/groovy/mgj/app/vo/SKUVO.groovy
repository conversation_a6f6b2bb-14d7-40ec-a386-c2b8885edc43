package groovy.mgj.app.vo

import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
//和后端SkuDO一致
class SKUVO {
    String title
    List<SkuData> skus
    List<PropInfo> props
    String styleKey
    String sizeKey
    String priceRange
    String defaultPrice
    int totalStock
    String mainPriceStr
    String subPriceStr
    Integer limitTotalStock
    Integer limitNum
    String limitDesc

    /**
     * 拼团信息
     */
    SKUDataVOPintuanInfo pinTuanInfo

    SKUVO(SkuDO sku) {
        this.title = sku?.title;
        this.skus = sku?.skus;
        this.props = sku?.props;
        this.styleKey = sku?.styleKey;
        this.sizeKey = sku?.sizeKey;
        this.priceRange = sku?.priceRange;
        this.defaultPrice = sku?.defaultPrice;
        this.totalStock = sku?.totalStock;
        this.mainPriceStr = sku?.mainPriceStr;
        this.subPriceStr = sku?.subPriceStr;
        this.limitTotalStock = sku?.limitTotalStock;
        this.limitNum = sku?.limitNum;
        this.limitDesc = sku?.limitDesc;
//        InvokerHelper.setProperties(this, sku?.properties)
    }
}