package groovy.mgj.app.xinren

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.Switcher
import groovy.mgj.app.normal.Share
import groovy.mgj.app.normal.ShareCssDataProducer
import groovy.mgj.app.normal.ShareCssDataProducers
import groovy.mgj.app.normal.ShareCssDataWrap
import groovy.mgj.app.vo.ShareVO

@Translator(id = "shareXinRen")
class ShareXinRen implements IFourDependTranslator<ItemBaseDO, ShopDO, ExtraDO, ChannelInfoDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO, ShopDO shopDO, ExtraDO extraDO, ChannelInfoDO channelInfoDO) {
        def shareNormal = new Share()
        ShareVO result = shareNormal.translate(itemBaseDO, shopDO, null, extraDO)
        // 渠道的分享url跟普通商品不一样
        // activityId直接从参数中取
        def activityId = DetailContextHolder.get()?.getParam("activityId")
        if (activityId) {
            def url = StrategyUpUtil.upUrl("https://h5.mogu.com/detail/newcomer.html?itemId=${itemBaseDO.iid}&activityId=${activityId}")
            result?.url = url

            // 没有小程序页面，小程序里也是打开h5
            def miniProgramPath = "/pages/web/index?src=${encode(result.url)}"
            result?.miniProgramPath = miniProgramPath

            // 渠道的私聊链接跟普通商品不一样
            def imUrl = XinRenUrlGenerator.generateMGJShareImUrl(itemBaseDO, channelInfoDO)
            result?.imUrl = imUrl

            result?.eventPrice = null
        }

        result?.imUrl = Share.getUpdatedIMUrl(result?.imUrl, itemBaseDO, result?.url)

        boolean csslayoutShareOpen = Switcher.csslayoutMiniCardShare()
        ShareCssDataWrap cssDataWrap = null
        // 处理csslayout的相关数据
        if (csslayoutShareOpen) {
            List<ShareCssDataProducer> producers = ShareCssDataProducers.FOR_XINREN
            for (ShareCssDataProducer producer : producers) {
                cssDataWrap = producer.produce(itemBaseDO, extraDO, null, result)
                if (cssDataWrap != null) {
                    break
                }
            }
        }
        if (cssDataWrap != null) {
            result.miniCardTempMaitID = cssDataWrap.maitId
            result.miniCardData = cssDataWrap.data
            result.miniCardTempMaitParams = ["iid": "" + DetailContextHolder.get()?.getItemId()]
            result.miniCardType = cssDataWrap.cardtype
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        ret.mayHideOldPrice(itemBaseDO)

        return result
    }

    static String encode(String s) {
        try {
            return URLEncoder.encode(s, "utf-8")
        } catch (Exception ignore) {
            return s
        }
    }
}
