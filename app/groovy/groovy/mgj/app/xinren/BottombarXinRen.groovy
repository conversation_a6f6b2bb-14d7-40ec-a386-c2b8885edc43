package groovy.mgj.app.xinren

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.BottomBarItemData
import groovy.mgj.app.vo.NormalBottombarVO

import static groovy.mgj.app.vo.NormalBottombarVO.*

/**
 * Created by wuyi on 2020/3/11.
 *
 * 新人渠道页的bottombar
 */
@Translator(id = "bottomBarXinRen")
class BottombarXinRen implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, NormalBottombarVO> {
    // 商品状态
    public static final int STATUS_INVALID = -1 //非正常情况
    public static final int STATUS_NORMAL = 0  // 正常
    public static final int STATUS_OUT_OF_SHELF = 1 // 下架
    public static final int STATUS_SOLD_OUT = 2 // 卖完
    public static final int STATUS_WAIT_SALE = 3  // 待开售

    // 销售类型
    public static final int SALE_TYPE_NORMAL = 0// 正常销售
    public static final int SALE_TYPE_PRESALE = 1// 预售

    @Override
    NormalBottombarVO translate(ItemBaseDO itemBaseDO, ChannelInfoDO channelInfoDO) {
        NormalBottombarVO VO = new NormalBottombarVO(
                shopId: itemBaseDO?.shopId,
                isFaved: itemBaseDO?.isFaved,
                iid: itemBaseDO?.iid
        )
        BottomBarItemData invalidBuyBtn = getInvalidBuyBtn(itemBaseDO)

        // 处理左边的按钮。固定只要店铺和客服两个按钮
        def leftTypeList = [LEFT_SHOP, LEFT_IM]
        VO.leftButtonList = composeLeftList(leftTypeList)
        VO.shopUrl = itemBaseDO?.shopId ? "mgj://shop?shopId=${itemBaseDO?.shopId}" : null
        VO.imUrl = XinRenUrlGenerator.generatorIMUrl(itemBaseDO, channelInfoDO)
        // 处理右边的按钮
        def rightTypeList = []
        if (invalidBuyBtn != null) {
            rightTypeList.add(invalidBuyBtn)
        } else {
            rightTypeList.add(new BottomBarItemData(
                    type: RIGHT_NEW_COMER_BUY,
                    title: "立即购买"
            ))
        }
        VO.rightButtonList = rightTypeList
        return VO
    }

    static BottomBarItemData getInvalidBuyBtn(ItemBaseDO itemBaseDO) {
        switch (itemBaseDO.state) {
            case STATUS_OUT_OF_SHELF:
                return new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "已下架",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )
            case STATUS_SOLD_OUT:
                // fixme 活动库存这里判断行不行
                return new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "已售罄",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )
        }
    }
}
