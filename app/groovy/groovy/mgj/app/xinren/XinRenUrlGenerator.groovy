package groovy.mgj.app.xinren

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by wuyi on 2020/3/12.
 */
class XinRenUrlGenerator {
    static String generatorIMUrl(ItemBaseDO itemBaseDO, ChannelInfoDO channelInfoDO) {
        if (!itemBaseDO) return null
        String baseUrl = "mgjim://talk?bid=${itemBaseDO.shopId}" +
                "&goodsId=${itemBaseDO.iid}" +
                "&userId=${itemBaseDO.userId}" +
                "&shopid=${itemBaseDO.shopId}" +
                "&login=1"
        if (channelInfoDO) {
            baseUrl += "&activityId=${DetailContextHolder.get().getParam("activityId")}" +
                    "&channelId=channel_newcomer" +
                    "&fromType=mgj_newcomer"
        }
        return baseUrl
    }


    static generateMGJShareImUrl(ItemBaseDO item, ChannelInfoDO channelInfoDO) {
        if (!channelInfoDO) {
            return (item?.iid && item?.shopId) ? "mgjim://share?iid=${item.iid}&shopId=${item.shopId}&type=1" : null
        } else {
            String activityId = DetailContextHolder.get().getParam("activityId")
            return "mgjim://share?iid=${item.iid}&shopId=${item.shopId}&type=1&activityId=${activityId}&channelId=channel_newcomer&fromType=mgj_newcomer"
        }
    }
}
