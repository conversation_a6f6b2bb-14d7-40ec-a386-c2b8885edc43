package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.translator.IFiveDependTranslator

/**
 * Created by fufeng on 2017/4/1.
 */
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import groovy.mgj.app.vo.CountdownVO
import groovy.mgj.app.vo.GroupBuyType
import groovy.mgj.app.vo.Tools

//这里展示不需要@translator,因为实际是在group里面返回的
class CountDownNormalV2 implements IFiveDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, CountdownVO> {
    @Override
    CountdownVO translate(ActivityDO input1, GroupbuyingDO input2, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO) {
        //现在有两种倒计时banner，大促和团购，同一个页面只显示一个倒计时
        //优先级：大促>限时爆款>团购
        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get("xsbk")
        //先判断大促
        if (input1?.countdown) {
            def result = new CountdownVO(
                    countdown: input1.countdown
            )
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(43182)

            String img
            // activityState = 1 是预热阶段 2是大促中
            if (input1.activityState == 1.intValue()){
                result.countdownTitle = null
                result.countdown = 0 //预热的时候不需要返回时间了
                result.type = 1
                img = input1?.activityPreImage
            }
            else{
                result.type = 0
                result.countdownTitle = "距结束仅剩"
                img = input1?.activityInImage
            }
            result.countdownBgImg = ImageUtil.img(img)
            return result
        }
        // 限时爆款
        else if (xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY) {
            def xsbkMait = MaitUtil.getMaitData(xsbk.maitId)?.get(0)
            def result = new CountdownVO(
                    countdownBgImg: xsbkMait?.get("backgroundImage"),
                    countdownTitleColor: xsbkMait?.get("fontColor"),
                    countdown: xsbk.countdown,
                    countdownTitle: "距结束仅剩",
                    type: 0
            )
            return result
        }
        //团购中
        else if (input2?.status == TuanStatus.IN && input2?.endTime) {
            //读取团购倒计时背景麦田资源
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(input2)
            def result = new CountdownVO(
                    countdown: input2.endTime - System.currentTimeSeconds(),
                    countdownTitle: "距结束仅剩",
                    countdownBgImg: ImageUtil.img(info?.image),
                    countdownTitleColor: info?.titleColor,
                    type: 0
            )
            return result
        }
        //团购预热
        else if (input2?.status == TuanStatus.PRE && input2?.startTime) {
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(input2)
            def result = new CountdownVO(
                    countdown: input2.startTime - System.currentTimeSeconds(),
                    countdownTitle: "距开始仅剩",
                    countdownBgImg: ImageUtil.img(info?.image),
                    countdownTitleColor: info?.titleColor,
                    type: 0
            )

            //U质团的预告期间，如果商品正在招商非渠道拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
            if(input2?.bizType == TuanBizType.UZHI && Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
                def tmp = getSystemPintuanData(itemBaseDO, pinTuanDO)
                if (tmp) {
                    result = tmp
                }
            }

            return result
        }
        //招商非渠道拼团商品
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            def result = getSystemPintuanData(itemBaseDO, pinTuanDO)
            if (result) {
                return result
            }
        }
        return new CountdownVO()//保证esi能覆盖缓存数据
    }

    /**
     * 获取招商非渠道拼团商品倒计时相关信息
     * @param itemBaseDO
     * @param pinTuanDO
     * @return nullable
     */
    static CountdownVO getSystemPintuanData(ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO) {
        if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            String appVersion = DetailContextHolder.get().getParam("_av")
            Integer version = appVersion?.toInteger()
            //大于等于1000版本才返回，老版本不返回
            if (version && version >= 1000) {
                List<Map<String, Object>> pintuanMaitData = MaitUtil.getMaitData(56776)
                Map<String, Object> pintuanImgData = pintuanMaitData?.get(0)
                String pintuanBackgroundImg = pintuanImgData?.get("normalPintuanImage")
                String pintuanTitleColor = pintuanImgData?.get("normalPintuanTitleColor")
                def result = new CountdownVO(
                        countdown: pinTuanDO.remainTime,
                        countdownTitle: "距结束仅剩",
                        countdownBgImg: ImageUtil.img(pintuanBackgroundImg),
                        countdownTitleColor: pintuanTitleColor,
                        type: 0
                )
                return result
            }
        }
        return null
    }


    static class GroupBuyImageAndTitleColorInfo {
        String image
        String titleColor

        public GroupBuyImageAndTitleColorInfo(GroupbuyingDO groupbuyingDO) {
            Map<String, Object> maitData = MaitUtil.getMaitData(43182)?.get(0)
            if (!maitData) {
                return
            }
            GroupBuyType groupBuyType = GroupBuyType.getGroupBuyType(groupbuyingDO)
            if (groupBuyType == GroupBuyType.UZHI) {
                image= maitData?.get("tuanUZhiImage")
                titleColor= maitData?.get("tuanUZhiTitleColor")
            }
            else if (groupBuyType == GroupBuyType.STORE) {

                image = maitData?.get("tuanInStoreImage")
                titleColor = maitData?.get("tuanInStoreTitleColor")
            }
            else if (groupBuyType == GroupBuyType.PINPAI) {
                image= maitData?.get("tuanPinpaiImage")
                titleColor= maitData?.get("tuanPinpaiTitleColor")
            }
            else if (groupBuyType == GroupBuyType.NORMAL) {
                image= maitData?.get("tuanNormalImage")
                titleColor= maitData?.get("tuanNormalTitleColor")
            }
        }
    }
}