package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.buyershow.domain.BuyerShowItem
import com.mogujie.detail.module.buyershow.domain.SizeInfo
import com.mogujie.detail.module.buyershow.domain.Source
import com.mogujie.detail.module.realityshow.domain.RealityShow
import com.mogujie.detail.module.realityshow.domain.RealityShowDO

@Translator(id = "videoShow", defaultValue = DefaultType.NULL)
class VideoShow implements IOneDependTranslator<RealityShowDO, VideoShowVO> {

    static class VideoShowVO{
        String title
        VideoShowEntryVO[] list

        VideoShowVO(RealityShowDO realityShowDO) {
            title = (realityShowDO?.cid != null && realityShowDO.cid == 1160) ? "开箱评测" : "真人穿搭展示"
            list = realityShowDO.showList?.collect {
                new VideoShowEntryVO(it, realityShowDO?.cid)
            }
        }
    }


    static class VideoShowEntryVO{
        VideoInfoVO video
        String avatar
        String userName
        SizeInfoVO sizeInfo
        String[] skuInfoList

        VideoShowEntryVO(RealityShow realityShow, Integer cid) {
            avatar = realityShow?.starInfo?.avatar
            userName = realityShow?.starInfo?.name
            video = new VideoInfoVO(realityShow)
            sizeInfo = null != realityShow?.starInfo?.effect ? new SizeInfoVO(realityShow) : null
            if (realityShow.skuInfo == null || realityShow.skuInfo.isEmpty()) {
                if (null != cid && cid == 1160) {
                    realityShow.skuInfo = [
                            "颜色": "默认",
                            "规格": "默认"
                    ]
                } else {
                    realityShow.skuInfo = [
                            "颜色": "默认",
                            "尺码": "默认"
                    ]
                }
            }
            skuInfoList = realityShow.skuInfo.collect {
               k, v -> k + ": " + v
            }
        }
    }

    static class VideoInfoVO{
        String cover
        String videoId
        Integer width
        Integer height

        VideoInfoVO(RealityShow realityShow) {
            cover = realityShow?.coverImg
            videoId = realityShow?.videoId
            width = realityShow?.width
            height = realityShow?.height
        }
    }

    static class SizeInfoVO {
        String title
        String desc

        SizeInfoVO(RealityShow realityShow) {
            title = realityShow?.starInfo?.effect
            desc = realityShow?.starInfo?.height + "cm / " + realityShow?.starInfo?.weight + "kg"
        }
    }

    @Override
    VideoShowVO translate(RealityShowDO realityShowDO) {
        if (!realityShowDO){
            //业务上可以return null
            return null
        }
        return new VideoShowVO(realityShowDO)
    }
}
