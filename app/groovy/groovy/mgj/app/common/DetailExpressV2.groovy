package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO

/**
 * Created by wuy<PERSON> on 2019/4/9.
 */
@Translator(id = "detailExpressV2")
class DetailExpressV2 implements IOneDependTranslator<ExtraDO, Object> {
    @Override
    Object translate(ExtraDO extraDO) {
        Object ret = new DetailExpress().translate(extraDO)
        // 不展示蘑豆
        ret.modou = ""
        return ret
    }
}
