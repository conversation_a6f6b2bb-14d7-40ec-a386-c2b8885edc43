package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveItemInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.mgj.app.vo.HideControl

/**
 * Created by pananping on 3/19/21.
 */
@Translator(id = "floatLiveInfo", defaultValue = DefaultType.NULL)
class FloatLiveInfo implements ITwoDependTranslator<LiveDO, LiveSimpleDO, Object> {

    static class FloatLiveInfoVO extends HideControl {
        String jumpUrl
        String avatar
        String textLine1
        String textLine2
        String acm
    }

    @Override
    Object translate(LiveDO liveDO, LiveSimpleDO liveSimpleDO) {
        if (liveDO?.liveItemInfos?.size() > 0 && !liveSimpleDO?.shopLiveInfo) {
            LiveItemInfo liveItemInfo = liveDO.liveItemInfos.get(0)
            String liveRoomURL = "mgj://mglive/enterLiveRoom?actorId=${IdConvertor.idToUrl(liveItemInfo.actUserId)}&acm=${liveItemInfo.acm}"
            return new FloatLiveInfoVO(
                    jumpUrl: liveRoomURL,
                    avatar: liveItemInfo.actorAvatar,
                    textLine1: liveItemInfo.actorName,
                    textLine2: "直播中",
                    acm: liveItemInfo.acm,
                    _extra_control_hide_: false
            )
        }
        return new FloatLiveInfoVO(_extra_control_hide_: true)
    }
}