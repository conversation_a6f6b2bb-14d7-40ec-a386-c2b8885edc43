package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.mgj.app.common.common.SizeTableProvider
import groovy.mgj.app.vo.ItemParamsVO
import groovy.mgj.app.vo.ProductInfoVO
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/5/22.
 */
@Translator(id = "itemParamsAppV2", defaultValue = DefaultType.NULL)
class ItemParamsAppV2 implements ITwoDependTranslator<ItemBaseDO, ItemParamsDO, Object> {
    static class ItemParamsVOV2 {
        String title = "尺码表 & 商品参数"
        ItemParamsDetailVOV2 itemParams
    }

    static class ItemParamsDetailVOV2 {
        ProductInfoVO info
        ItemParamsRuleV2 rule = new ItemParamsRuleV2()
    }

    static class ItemParamsRuleV2 {
        /**
         * 尺码说明的描述
         */
        String desc;
        /**
         * 尺码说明的表格
         */
        groovy.mgj.app.vo.ItemParams sizeTable;
        /**
         * 图片
         */
        List<String> images;
    }

    @Override
    Object translate(ItemBaseDO itemBaseDO, ItemParamsDO itemParamsDO) {
        if (!itemParamsDO) {
            // 业务上可以返回NULL
            return null
        }
        def itemParamsTranslator = new ItemParams()
        ItemParamsVO itemParamsOriginalVO = itemParamsTranslator.translate(itemParamsDO)
        if (itemParamsOriginalVO == null) {
            // 业务上可以返回NULL
            return null
        }

        // 如果有新的尺码助手图，这里就不展示尺码
        boolean hideSize = Tools.hasSizeHelperIMG(itemBaseDO)
        ItemParamsVOV2 itemParamsVOV2 = new ItemParamsVOV2()
        itemParamsVOV2.itemParams = new ItemParamsDetailVOV2()
        itemParamsVOV2.itemParams.info = itemParamsOriginalVO?.itemParams?.info

        boolean actualShowSize = false
        if (!hideSize && itemParamsOriginalVO?.itemParams?.rule != null) {
            def newRule = new ItemParamsRuleV2()
            newRule.desc = itemParamsOriginalVO.itemParams.rule.desc
            newRule.images = itemParamsOriginalVO.itemParams.rule.images
            newRule.sizeTable = SizeTableProvider.getSizeTable(itemParamsDO)
            itemParamsVOV2.itemParams.rule = newRule
            actualShowSize = true
        }

        itemParamsVOV2.title = actualShowSize ? "尺码表 & 商品参数" : "商品参数"

        return itemParamsVOV2
    }

    public static boolean hasSize(ItemParamsDO itemParamsDO) {
        if (itemParamsDO == null) return false
        def itemParamsTranslator = new ItemParams()
        ItemParamsVO itemParamsOriginalVO = itemParamsTranslator.translate(itemParamsDO)
        return itemParamsOriginalVO?.itemParams?.info != null
    }
}