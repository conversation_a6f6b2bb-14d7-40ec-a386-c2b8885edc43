package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.buyershow.domain.BuyerShowItem
import com.mogujie.detail.module.buyershow.domain.SizeInfo
import com.mogujie.detail.module.buyershow.domain.Source

@Translator(id = "buyerShow", defaultValue = DefaultType.NULL)
class BuyerShow implements IOneDependTranslator<BuyerShowDO, BuyerShowVO> {

    class BuyerShowVO{
        String title
        BuyerShowEntryVO[] shows
    }

    static getSourceTypeStrings(Source source) {
        def sourceType = [:]
        def sourceTypeExtra = [:]
        sourceType.put(Source.SELLER,"精选")
        sourceType.put(Source.EDITOR,"精选")
        sourceTypeExtra.put(Source.SELLER, "- SELECTED -")
        sourceTypeExtra.put(Source.EDITOR, "- SELECTED -")

        return new Tuple2(sourceType[source], sourceTypeExtra[source])
    }

     def getSummaryTags(SizeInfo info){
         def result = []
         if (info?.desc) {
             def suitTag = new TextTagVO(
               text: info.desc,
                textColor: "#FFFFFF",
                bgColor: info.isSuit?"#FF5777":"#666666"
             )
             result.add(suitTag)
         }
         if (info?.height && info?.weight) {
             def sizeTag = new TextTagVO(
                     text: "$info.height / $info.weight",
                     textColor: info.isSuit ? "#FF5777" : "#666666",
                     bgColor: info.isSuit ? "#FFE8EE" : "#EFEFEF"
             )
             result.add(sizeTag)
         }

         return result
    }

    class BuyerShowEntryVO{
        String avatar
        String avatarTag
        String name
        TextTagVO[] summaryTags
        String contentType
        String contentTypeExtra
        String content
        String[] images
        String url

        BuyerShowEntryVO(BuyerShowItem item) {
            avatar = item?.userInfo?.avatar
            avatarTag = item?.userInfo?.isDaren()?ImageUtil.img("/p1/160707/upload_ifrdkyjsmftdiyrzhezdambqmeyde_51x51.png"):null
            name = item?.userInfo?.name
            content = item?.content
            if (item?.source){
                (contentType, contentTypeExtra) = getSourceTypeStrings(item.source)
            }
            images = item?.imgs
            if (item?.sizeInfo){
                summaryTags = getSummaryTags(item.sizeInfo)
            }
            if (item?.contentId) {
                def contentId = IdConvertor.idToUrl(item?.contentId)
                url = "mgj://buyershowdetail?iid=$contentId&type=1"
            }
        }
    }

    class TextTagVO{
        String bgColor
        String text
        String textColor
    }

    @Override
    BuyerShowVO translate(BuyerShowDO input1) {
        if (!input1){
            //业务上是可以返回NULL的
            return null
        }
        return new BuyerShowVO(
                title: "买手体验（${input1.count}）",
                shows: input1.items?.collect {
                    new BuyerShowEntryVO(it)
                }
        )
    }
}
