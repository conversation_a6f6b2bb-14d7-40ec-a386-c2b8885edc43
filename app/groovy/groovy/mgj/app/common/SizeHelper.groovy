package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.TrialReportInfo
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import groovy.mgj.app.vo.ItemParams
import groovy.mgj.app.vo.SizeHelperVO
import groovy.mgj.app.vo.TrialReport

/**
 * 客户端sizeMeasureImg字段如果为空,会导致crash,谨慎 - by 2017/05/18
 * Created by anshi on 17/3/29.
 */
@Translator(id = "sizeHelper", defaultValue = DefaultType.NULL)
class SizeHelper implements IThreeDependTranslator<SizeHelperDO, ItemParamsDO, ItemBaseDO, Object> {

    String getSizeImg(String cids) {
        if (cids == null) {
            return null
        }
        if (cids.contains("#710#")) {
            return ImageUtil.img("/mlcdn/c45406/170330_32k878ki186jag03eb00l9k2840k0_1125x2130.png")
        } else if (cids.contains("#684#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_6d5a9f04jk6a6dga0cc63g7g9dd75_1125x1950.png")
        } else if (cids.contains("#706#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_061lc266cbfe4dd79kk9glce60ahc_1125x1560.png")
        } else if (cids.contains("#705#")) {
            return ImageUtil.img("/mlcdn/c45406/170331_66kh5fh6h302al1dgcc3a4a1l1k7d_1125x2250.png")
        } else {
            return null
        }
    }

    @Override
    SizeHelperVO translate(SizeHelperDO input1, ItemParamsDO input2, ItemBaseDO input3) {
        if (input1 == null) {
            // 业务上可以返回NULL
            return null
        }
        def itemParam = new ItemParams()
        if (input2 != null) {
            List<List<String>> table = null;
            if (input2.rule?.tables?.size() > 0) {
                table = input2.rule?.tables?.get(0)
            }
            if (table != null && table.size() > 1 && table.get(0) != null && table.get(0).size() >= 2) {
                Integer sizeTypeIndex = null
                for (int i = 0; i < table[0].size(); i++) {
                    String th = table[0].get(i)
                    if ("号型" == th) {
                        sizeTypeIndex = i;
                    }
                }
                List<List<String>> orderedTable = new ArrayList<>();
                if (sizeTypeIndex != null && sizeTypeIndex > 1) {
                    for (List<String> line : table) {
                        List<String> orderedLine = new ArrayList<>(line);
                        String sizeTypeVal = orderedLine.remove(sizeTypeIndex);
                        orderedLine.add(1, sizeTypeVal);
                        orderedTable.add(orderedLine);
                    }
                    table = orderedTable;
                }
                itemParam.heads = table[0]
                String suggestedSizeType = input1.matchedSizeType
                List<List<String>> values = new ArrayList<>()
                List<String> unit = new ArrayList<>();
                unit.add("");
                for (int i = 1; i < itemParam.heads.size(); i++) {
                    if (i < 2) {
                        unit.add("")
                    } else {
                        unit.add("cm")
                    }
                }
                for (int i = 1; i < table.size(); i++) {
                    List<String> valueLines = table[i]
                    for (String valItem : valueLines) {
                        if (valueLines.size() >= 2 && suggestedSizeType == valueLines[1]) {
                            itemParam.suggestedLineNum = i - 1
                        }
                    }
                    values.add(table.get(i))
                }
                if (itemParam.suggestedLineNum == null) {
                    itemParam.suggestedLineNum = -1
                }
                itemParam.unit = unit
                itemParam.paramVals = values
            }
        }

        List<TrialReport> trialReports = new ArrayList<>();
        if (input3 != null && input3.trialReportInfos != null) {
            for (TrialReportInfo trial : input3.trialReportInfos) {
                trialReports.add(new TrialReport(
                        tag: "试穿${trial.trialSize}",
                        title: "${trial.modelName}: 身高${trial.height}cm  体重${trial.weight}kg  胸围${trial.chest}cm",
                        message: trial.effect
                ))
            }
        }

        def vo = new SizeHelperVO(
                userInfoId: input1.userInfoId,
                userInfoFilled: input1.userInfoFilled,
                showUserInfo: input1.show,
                avatar: input1.avartar,
                height: input1.height,
                weight: input1.weight,
                chest: input1.chest,
                waist: input1.waist,
                hipline: input1.hipline,
                sizeMeasureImg: getSizeImg(input3?.cids),
                itemParams: itemParam,
                trialReports: trialReports
        )
        return vo
    }
}
