package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.mgj.app.vo.Tools

@Translator(id = "atmosphereBanner")
class AtmosphereBanner implements ISixDependTranslator<ActivityDO,GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO,PresaleDO, AtmosphereBannerVO> {
    static class AtmosphereBannerVO {
        String background
        String link
        String acm
    }

    @Override
    AtmosphereBannerVO translate(ActivityDO input1, GroupbuyingDO input2, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presale) {
        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get("xsbk")

        def result = new AtmosphereBannerVO()

        //预售，大促预热期、团购预热不返回,
        if(presale != null || (input1 && input1?.activityState == 1.intValue()) || input2?.status == TuanStatus.PRE)
        {
            return result
        }
        //先判断大促
        else if (input1?.countdown && input1?.activityState == 2.intValue() && input1?.activitySphereImage){
            result.background=input1?.activitySphereImage
            result.link = ""
        }
        //限时爆款
        else  if(xsbk){
            Map<String, Object> maitData = MaitUtil.getMaitData(123210)?.get(0)
            result.background=maitData?.get("activityBanner")
            result.link = ""
        }
        //团购
        else if(input2?.status == TuanStatus.IN && input2?.endTime){
            Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
            result.background=maitData?.get("activityBanner")
            result.link = ""
        }
        //非渠道拼团
        //U质团的预告期间，如果商品正在招商非渠道拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)
                || (input2?.status == TuanStatus.PRE && input2?.startTime && input2?.bizType == TuanBizType.UZHI && Tools.isSystemPintuan(itemBaseDO, pinTuanDO) && input2?.endTime) ) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
            result.background=maitData?.get("activityBanner")
            result.link = ""
        }

        return result
    }

}
