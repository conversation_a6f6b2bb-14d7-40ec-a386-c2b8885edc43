package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.collcationset.domain.CollcationSetDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetCampaignForDetailDTO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetItemGroupDTO

@Translator(id = "collocation", defaultValue = DefaultType.NULL)
class Collocation implements ITwoDependTranslator<ItemBaseDO, CollcationSetDO, Object> {

    static String currentItemId //当前商品的iid

    class CollocationVO{
        CollocationEntryVO[] list
    }

    //这个直接用数据层的数据
    class CollocationEntryVO{
        String promotionMark
        String name
        String url
        CollocationSetItemGroupDTO[] collocationSetItemGroupDTOs


        CollocationEntryVO(CollocationSetCampaignForDetailDTO dto, Integer index) {
            this.promotionMark = dto.promotionMark
            this.name = dto.name
            this.collocationSetItemGroupDTOs = dto.collocationSetItemGroupDTOs
//            InvokerHelper.setProperties(this, dto.properties)
            url = "mgj://collocation?iid=${currentItemId}&initIndex=${index}"
        }
    }

    @Override
    Object translate(ItemBaseDO input1, CollcationSetDO input2) {
        if (!input2){
            // 业务上可以返回NULL
            return null
        }
        currentItemId = input1?.iid
        new CollocationVO(
                list: input2?.withIndex()?.collect { element, index ->
                    new CollocationEntryVO(element, index)
                }
        )
    }
}
