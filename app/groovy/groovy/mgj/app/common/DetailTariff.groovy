package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData

@Translator(id = "tariff", defaultValue = DefaultType.NULL)
class DetailTariff implements ITwoDependTranslator<ItemBaseDO, SkuDO, Object> {
    class DetailTariffInfoVO{
        String title
        String desc
        String descTip

        DetailTariffInfoVO(String title,String desc,String descTip){
            this.title = title
            this.desc = desc
            this.descTip = descTip
        }
    }


    class DetailPopoverVO {
        String title
        List<DetailTariffInfoVO> list

        DetailPopoverVO(OverseaItemEnum overseaType, String tax, String taxRate){
            this.title ="税费说明"
            this.list = new ArrayList<>()
            if(overseaType == OverseaItemEnum.BONDED_ITEM){
                this.list.add(new DetailTariffInfoVO("商品税费","预计" + tax + "元", "(实际计算税费以提交订单时税费为准)"))
                this.list.add(new DetailTariffInfoVO("税率", "该商品的关税率为" + taxRate + "%", "(依据中国海关规定，不同类目的商品征收税率会不同)"))
                this.list.add(new DetailTariffInfoVO("税费计算", "进口税 = 商品完税价格（不含运费）*税率", "(完税价格由海关最终认定)"))
            }
            else if(overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL){
                this.list.add(new DetailTariffInfoVO("商品税费", "商家包税", "(如遇海关抽检产生税费时，商家会承担相应的费用)"))
                this.list.add(new DetailTariffInfoVO("税费税率", "海关抽查时判定税率", null))
                this.list.add(new DetailTariffInfoVO("税费计算", "进口税 = 海关认定完税价 * 认定税率", null))
                this.list.add(new DetailTariffInfoVO("报销税费", "跨境商品需依法向中国海关申报及纳税，若配送期间产生税费，需要您携带身份证前往当地海关缴纳，凭缴税凭证可联系客服报销。", null))
            }
        }
    }


    class DetailTariffVO{
        String text
        String desc
        DetailPopoverVO popover
    }

    @Override
    DetailTariffVO translate(ItemBaseDO itemBaseDO, SkuDO skuDO) {
        if (!itemBaseDO || !itemBaseDO?.overseaItemInfo || !skuDO?.skus) {
            DetailTariffVO ret = new DetailTariffVO()
            return ret
        }

        DetailTariffVO ret = new DetailTariffVO()
        boolean range
        int lowPrice, highPrice
        (range, lowPrice, highPrice) = getPriceInfo(skuDO)
        if (itemBaseDO.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM && itemBaseDO.overseaItemInfo?.taxRate) {
            String taxRate = String.format("%.2f", itemBaseDO.overseaItemInfo?.taxRate / 100.0f)
            if (range) {
                // 区间价
                String lowTax = String.format("%.2f", itemBaseDO.overseaItemInfo?.taxRate / 10000.0f * lowPrice / 100f)
                String highTax = String.format("%.2f", itemBaseDO.overseaItemInfo?.taxRate / 10000.0f * highPrice / 100f)
                String tax = lowTax + "-" + highTax
                ret.desc = "进口税约" + tax + "元"
                ret.popover = new DetailPopoverVO(OverseaItemEnum.BONDED_ITEM, tax, taxRate)
            } else {
                // 非区间价
                String tax = String.format("%.2f", itemBaseDO.overseaItemInfo?.taxRate / 10000.0f * lowPrice / 100f)
                ret.desc = "进口税约" + tax + "元"
                ret.popover = new DetailPopoverVO(OverseaItemEnum.BONDED_ITEM, tax, taxRate)
            }
            ret.text = "税费"
        } else if (itemBaseDO.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
            ret.desc = "商家包税"
            ret.popover = new DetailPopoverVO(OverseaItemEnum.OVERSEA_DIRECT_MAIL, "", "")
            ret.text = "税费"
        }

        return ret
    }

    def getPriceInfo(SkuDO skuDO) {
        boolean range
        int lowPrice = Integer.MAX_VALUE, highPrice = Integer.MIN_VALUE
        // 自己遍历sku拿到最高价和最低价
        for (SkuData skuData : skuDO?.skus) {
            lowPrice = Math.min(skuData.nowprice, lowPrice)
            highPrice = Math.max(skuData.nowprice, highPrice)
        }
        range = lowPrice != highPrice
        return new Tuple(range, lowPrice, highPrice)
    }

}