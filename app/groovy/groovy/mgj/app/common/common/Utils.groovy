package groovy.mgj.app.common.common

import com.mogujie.detail.core.adt.DetailContextHolder
import org.apache.commons.lang3.StringUtils

class Utils {

    /**
     * 获取客户端版本号，没有获取到就是0
     * @return
     */
    static int getClientVersion() {
        String av = DetailContextHolder.get().getParam("_av");
        av = StringUtils.isEmpty(av) ? "0" : av
        av = av.isNumber() ? av : "0"
        return av.toInteger()
    }

}
