package groovy.mgj.app.common.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.MaitUtil
import org.apache.commons.lang3.StringUtils

/**
 * Created by xvxvxxx on 2017/4/26.
 */

class ShareGiftProvider {

    static public ShareGiftMaitDO getShareGiftMaitData() {
        //去麦田获取分享Icon和Text
        String av = DetailContextHolder.get().getParam("_av");
        av = StringUtils.isEmpty(av) ? "0" : av
        av = av.isNumber() ? av : "0"
        Integer avInt = av.toInteger()
        def shareMaitInfo
        //这里要对版本做一下区分,新版本要走定投的逻辑,老版本是通投的逻辑
        if (avInt >= 940) {
            shareMaitInfo = MaitUtil.getTargetedMaitData(38873)
        }
        else {
            shareMaitInfo = MaitUtil.getMaitData(38873)
        }
        if (shareMaitInfo?.get(0)) {
            ShareGiftMaitDO maitData = new ShareGiftMaitDO()
            maitData.shareIcon = shareMaitInfo.get(0)?.get("shareIcon")
            maitData.shareText = shareMaitInfo.get(0)?.get("shareText")
            maitData.shareBannerBg = shareMaitInfo.get(0)?.get("shareBannerBg")
            maitData.shareBannerDesc = shareMaitInfo.get(0)?.get("shareBannerDesc")
            maitData.shareBannerButtonText = shareMaitInfo.get(0)?.get("shareBannerButtonText")
            maitData.shareBannerColor = shareMaitInfo.get(0)?.get("shareBannerColor")
            maitData.acm = shareMaitInfo.get(0)?.get("acm")
            return maitData
        }
        else {
            return null
        }
    }
}