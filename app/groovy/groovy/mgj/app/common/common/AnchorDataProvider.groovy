package groovy.mgj.app.common.common

import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.spi.dslutils.Tools

/**
 * Created by fufeng on 2017/3/29.
 */

class AnchorDataProvider {
    static AnchorDataVO[] getAnchorDataList() {
        def list = [
                new AnchorDataVO(image: ImageUtil.img("/mlcdn/e5265e/170406_05b5880klf31gic5jc4h91517f24c_36x36.png"), text: "评价"),
                new AnchorDataVO(image: ImageUtil.img("/mlcdn/e5265e/170406_331hl0e8dg20ai33l98kjd72e4d45_36x36.png"), text: "详情")
        ]
        // 非直播来源展示热卖推荐
        if (!groovy.mgj.app.vo.Tools.isLiveSource()) {
            list.add(new AnchorDataVO(image: ImageUtil.img("/mlcdn/e5265e/170406_2iea65l4c187bd8580ae44kdc7bgi_36x36.png"), text: "推荐"))
        }
        return list
    }

    static AnchorDataVO getAnchorDataAtIndex(int index) {
        if (index < getAnchorDataList().size()) {
            return (getAnchorDataList())[index]
        }
        return null
    }
}