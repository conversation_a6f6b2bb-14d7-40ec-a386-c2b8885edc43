package groovy.mgj.app.common.common

import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO

/**
 * Created by wuy<PERSON> on 2019-09-16.
 */
class NormalCountdownManager {
    static final String CONFIG_KEY = "extraNormalCountdownKeys"

    def static getActivity(NormalCountdownDO normalCountdownDO) {
        // 通过配置新增的kv标氛围
        String configStr = MetabaseUtil.get(CONFIG_KEY)
        String[] extraActivityKeys = configStr ? configStr.split(",") : []
        CountdownInfo extraInActivity = null
        CountdownInfo extraPreActivity = null
        for (String key : extraActivityKeys) {
            CountdownInfo tempActivity = normalCountdownDO?.getCountdownInfoMap()?.get(key)
            if (tempActivity
                    && tempActivity.state == CountdownState.IN_ACTIVITY
                    && !extraInActivity) {
                extraInActivity = tempActivity
            }
            if (tempActivity
                    && tempActivity.state == CountdownState.WARM_UP
                    && (!extraPreActivity || tempActivity?.startTime < extraPreActivity?.startTime)) {
                extraPreActivity = tempActivity
            }
        }

        return new Tuple2<>(extraPreActivity, extraInActivity)
    }
}
