package groovy.mgj.app.common.common

import groovy.mgj.app.vo.HideControl

/**
 * Created by BBS on 17/7/24.
 */
class ShopFullInfoVO extends HideControl {
    Integer cFans
    Integer cSells
    String cFansString
    String cSellsString
    Integer level
    String star //新的商家DSR
    String name
    String shopId
    String shopLogo
    String shopUrl
    String userId
    String tag
    List<ShopDSRVO> score
    String shopDesc // 展示在星级前面的文案
    String saleDesc // 在售商品数
    List<ShopLabel> dynLabels // 不需要使用缓存的店铺标签
    List<ShopLabel> labels // 需要使用缓存的店铺标签
    List<Integer> labelPriorityList
}

