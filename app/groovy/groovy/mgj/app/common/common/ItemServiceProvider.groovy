package groovy.mgj.app.common.common

import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MetabaseTool
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.metabase.spring.client.MetabaseClient

/**
 * Created by fufeng on 2017/3/28.
 */

class ItemServiceProvider {

    ItemServiceBasicVO getItemServiceBasicVO(ItemBaseDO itemBaseDO, ShopDO shopDO, MetabaseClient metabaseClient) {
        def serviceList = []

        if (Tools.isGoodItem(itemBaseDO, shopDO)) {
            if (shopDO?.goodItemServices) {
                serviceList.addAll(shopDO?.goodItemServices?.findAll({
                    // 1480 版本起，过滤掉发货承诺（serviceHeadId=400）
                    groovy.mgj.app.vo.Tools.getAppVersion() < 1480 || it.serviceHeadId != 400
                })?.collect {
                    new TagItemVO(
                            name: it.name,
                            icon: it.icon,
                            content: it.desc
                    )
                })
            }
        } else {
            if (shopDO?.services) {
                serviceList.addAll(shopDO?.services?.findAll({
                    // 1480 版本起，过滤掉发货承诺（serviceHeadId=400）
                    groovy.mgj.app.vo.Tools.getAppVersion() < 1480 || it.serviceHeadId != 400
                })?.collect {
                    new TagItemVO(
                            name: it.name,
                            icon: it.icon,
                            content: it.desc
                    )
                })
            }
        }

        //分期标
        if (ItemTag.INSTALMENT in itemBaseDO?.itemTags) {
            def intallmentTag = new TagItemVO(
                    name: "白付美（先享后付）",
                    icon: ImageUtil.img("/mlcdn/e5265e/170401_7f87e14b6k1ahkbd0lbkklki404ja_60x63.png"),
                    content: "先收货再付款，0首付，可分期"
            )
            if (!serviceList) {
                serviceList = []
            }
            serviceList.add(0, intallmentTag)
        }
        if (serviceList.size() == 0.intValue()) {
            return null
        }

        //优先级统一为：进口商品,商家包税,假一赔三,健康服务,医美意外险,劣一赔三服务,源头好货,品牌认证,入仓质检,小时发货,天无理由退货,不支持无理由退货,退货补运费,实拍认证,白付美分期
        String servicesString = MetabaseTool.getValue("app_item_service_priority")
        def defautStandardList = ["进口商品", "商家包税", "假一赔三", "健康服务", "医美意外险", "劣一赔三服务","源头好货","品牌认证","入仓质检","小时发货","天无理由退货","不支持无理由退货","退货补运费","实拍认证","白付美分期购"]
        def standardList =  defautStandardList
        if (servicesString) {
            List<String> list = Arrays.asList(servicesString.split(","))
            standardList = list
        }
        serviceList = sortTagList(filterList(serviceList), standardList)

        return new ItemServiceBasicVO(
                list : serviceList
        )
    }

    static List<TagItemVO> sortTagList(List<TagItemVO> serviceList, List<String> standardStringList) {
        if (serviceList == null) {
            return null
        }
        //不对输入的serviceList产生影响
        def inputServiceList = new ArrayList(serviceList)

        def newList = new ArrayList()
        standardStringList.each { String entry ->
            serviceList.each { TagItemVO innerEntry ->
                if (innerEntry.name.endsWith(entry)) {
                    newList.add(innerEntry)
                    return true
                }
            }
        }
        //这里是为了防止出现不存在标准列表里面的Tag被丢弃掉,需要加回去
        inputServiceList.removeAll(newList)
        newList.addAll(inputServiceList)

        return newList
    }

    static List<TagItemVO> filterList(List<TagItemVO> serviceList) {
        List<TagItemVO> newServiceList = new ArrayList<>()
        if (serviceList == null) {
            return newServiceList
        }
        newServiceList.addAll(serviceList)
        if (Tools.isBondedItem()) {
            // 保税商品不展示"发货后不支持退货"
            newServiceList = newServiceList.findAll {
                !it.name.endsWith("发货后不支持退货")
            }
        }
        return newServiceList
    }
}