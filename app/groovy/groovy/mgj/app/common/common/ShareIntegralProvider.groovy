package groovy.mgj.app.common.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.MetabaseTool
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.spi.dslutils.Tools

/**
 * Created by wuy<PERSON> on 2018/3/13.
 */
class ShareIntegralProvider {
    static Object getShareIntegralVO(ItemBaseDO itemBaseDO) {
        if (canGainIntegral(itemBaseDO)) {
            def shareRes = MaitUtil.getMaitData(106787)
            def stepRes = MaitUtil.getMaitData(106861)
            Map<String, Object> ret = new HashMap<>()
            if (shareRes?.get(0) != null) {
                ret.putAll(shareRes?.get(0))
                ret.put("steps", stepRes)
            }
            return ret
        } else {
            // 不能缓存
            return new HashMap()
        }
    }

    static String getShareIconUrl(ItemBaseDO itemBaseDO) {
        def shareRes = MaitUtil.getMaitData(106787)
        if (canGainIntegral(itemBaseDO)) {
            return shareRes?.get(0)?.get("shareEntryActivityIcon")
        } else {
            return shareRes?.get(0)?.get("shareEntryNormalIcon")
        }
    }

    static boolean canGainIntegral(ItemBaseDO itemBaseDO) {
        try {
            boolean isVirtualItem = (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL)
            boolean isValidState = itemBaseDO?.state != 1 && itemBaseDO?.state != 2
            boolean shareIntegralSwitch = Boolean.valueOf(MetabaseTool.getValue("shareIntegralSwitch"))
            boolean liveChannel = "live".equals(DetailContextHolder.get().getRouteInfo().getChannelType())
            boolean isMedicalBeauty = Tools.isMedicalBeautyItem()
            return !isVirtualItem && isValidState && shareIntegralSwitch && !liveChannel && !isMedicalBeauty
        } catch (Throwable throwable) {
            return false
        }
    }
}
