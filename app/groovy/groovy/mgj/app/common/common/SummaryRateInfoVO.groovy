package groovy.mgj.app.common.common

import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.service.rate.util.RateUtils
import groovy.mgj.app.common.ThemeName

/**
 * Created by fufeng on 2017/4/7.
 */
class SummaryRateInfoVO {
    Double score
    Boolean isBetter
    Double star
    String desc
    String image
    String rateDesc
    String rateUrl

    SummaryRateInfoVO() {
    }

    SummaryRateInfoVO(ItemBaseDO input1, RateDO rateDO, ShopDO shopDO) {
        if (rateDO != null) {
            // 如果在新品上市范围内，商品已经有销量评分，则显示评分
            if (rateDO != null && rateDO.itemDsr != null && !rateDO.itemDsr.defaultValue) {
                int avgCatDSR = RateUtils.getDsrAvg(Arrays.asList(rateDO.itemDsr.catDesc, rateDO.itemDsr.catQuality))
                int avgDSR = RateUtils.getDsrAvg(Arrays.asList(rateDO.itemDsr.desc, rateDO.itemDsr.quality))
                this.score = (double) (avgDSR / 100)
                this.isBetter = (avgDSR >= avgCatDSR)
                this.rateDesc = "${rateDO.CRate}评价"

                double star = (double) (Math.round((avgDSR / 100 - 3.0) / (5 - 3.0) * 500) / 100)
                if (star < 0) star = 0
                this.star = star

            }
            // 商品第一次上架才展示新品上市，上新时间会有三个档位：今日上新、3天内上新、7天内上新、最近上新（上架时间超过7天)
            else if (((System.currentTimeMillis() / 1000 - input1.firstOnlineTime) / (24 * 60 * 60)) < 14) {
                this.image = ImageUtil.img("/mlcdn/e5265e/170412_83cagd36al73ab8a70g34laghahll_144x93.png")
                int day = ((System.currentTimeMillis() / 1000 - input1.firstOnlineTime) / (24 * 60 * 60))
                String rateDesc
                if (day < 1) {
                    rateDesc = "今日上新"
                } else if (day >= 1 && day < 3) {
                    rateDesc = "3天内上新"
                } else if (day >= 3 && day < 7) {
                    rateDesc = "7天内上新"
                } else {
                    rateDesc = "最近上新"
                }
                this.rateDesc = rateDesc
            }
            // 如果已经超过14天仍没有销量，但是90天前有评价,则显示暂无评分
            else if (rateDO?.itemDsr?.defaultValue && rateDO?.CRate > 0) {
                this.desc = "暂无评分"
                this.star = 0
                this.rateDesc = "${rateDO.CRate}评价"
            }
            // 如果已经超过14天仍没有销量，则显示暂无评分
            else {
                this.desc = "暂无评价"
                this.star = 0
                this.rateDesc = "0评价"
            }

            if (rateDO?.CRate > 0) {
                //跳转链接
                this.rateUrl = "mgj://ratelist?iid=${input1?.iid}"
                ThemeName themeNameTranslator = new ThemeName()
                String themeName = themeNameTranslator.translate(input1, shopDO)
                this.rateUrl = this.rateUrl + "&themeName=${themeName}"
            }

        }
    }

    static getEmpty() {
        return new SummaryRateInfoVO()
    }
}