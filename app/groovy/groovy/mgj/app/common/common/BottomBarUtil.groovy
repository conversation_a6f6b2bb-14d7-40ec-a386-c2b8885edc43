package groovy.mgj.app.common.common

import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.service.item.domain.entity.Item
import groovy.json.JsonBuilder
import groovy.mgj.app.vo.Tools
import org.apache.http.client.utils.URIBuilder

/**
 * Created by fufeng on 2017/10/18.
 */
class BottomBarUtil {

    static String getBuyParamsString(SkuDO skudo, ItemBaseDO itemBaseDO, PinTuanDO pintuanDO) {
        if (itemBaseDO == null || skudo == null || itemBaseDO?.virtualItemType == VirtualItemType.NORMAL) {
            return ""
        }

        Map params = [
                addressId: "",
                marketType:"market_mogujie",
                shops:[
                        [
                                skus:[
                                        [
                                                stockId:skudo?.skus?.first()?.stockId ?: "",
                                                number:1,
                                                ptp:getPtpPlaceHolder(itemBaseDO),
                                                liveParams:getLiveParamsPlaceHolder(itemBaseDO),
                                                fashionParams:getLiveParamsPlaceHolder(itemBaseDO)
                                        ]
                                ]
                        ]
                ],
                orderFrom:"detail",
                modouUse:0,
                platFormType:"App"    //App or iPad
        ]

        if (Tools.isPintuan(itemBaseDO, pintuanDO)) {
            params["shops"][0]["skus"][0]['pinTuan'] = [
                    "tuanType": 7,
            ]
        }

        return new JsonBuilder( params ).toString() ?: ""
    }

    static String getBuyBaseUrl(ItemBaseDO itemBaseDO) {
        def ret = ""
        if (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL) {
            ret = StrategyUpUtil.upUrl('http://h5.mogujie.com/buy/index.html')
        }
        return ret
    }

    static String getPtpPlaceHolder(ItemBaseDO itemBaseDO) {
        def ret = ""
        if (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL) {
            ret = "\${ptp}"
        }
        return ret
    }

    static String getLiveParamsPlaceHolder(ItemBaseDO itemBaseDO) {
        def ret = ""
        if (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL) {
            ret = "\${liveParams}"
        }
        return ret
    }

    static String getFashionParamsPlaceHolder(ItemBaseDO itemBaseDO) {
        def ret = ""
        if (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL) {
            ret = "\${fashionParams}"
        }
        return ret
    }
}
