package groovy.mgj.app.common.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

/**
 * Created by wuyi on 2019/1/29.
 */
class SummaryHintProvider {
    static def getHint(SkuDO skuDO, boolean needInstallment) {
        String hint = null
        TextStyle installmentStyle = null

        // 分期信息或者延迟发货信息
        // 延迟发货优先级更高 延迟发货信息包括新老两种设置
        boolean hasNewDelayship = false
        int newRecentestDelayHours = Integer.MAX_VALUE
        int newLatestDelayHours = Integer.MIN_VALUE

        String delayReason = null
        for (SkuData skuData : (skuDO?.skus ?: new ArrayList<SkuData>())) {
            int delayHours = skuData?.delayHours ? skuData.delayHours : 0
            if (delayHours > 0) {
                // 全款预售商品会设置延迟发货原因delayReason，使用delayHours这套延迟发货
                if (delayHours > newLatestDelayHours) delayReason = skuData?.delayReason ?: delayReason

                newRecentestDelayHours = Math.min(newRecentestDelayHours, delayHours)
                newLatestDelayHours = Math.max(newLatestDelayHours, delayHours)
                hasNewDelayship = true
            }
        }

        // 春节不打烊
        if (TextUtils.isEmpty(hint)) {
            if (Tools.inSpringFestival()) {
                if (Tools.isSpringFestivalShutdownItem()) {
                    // 打烊
                    hint = "春节快递休息，发货时间请咨询商家客服"
                    installmentStyle = new TextStyle(
                            textColor: "#FFFF4466",
                            fontSize: 11
                    )
                } else {
                    // 不打烊
                    hint = "春节期间不打烊，照常发货"
                    installmentStyle = new TextStyle(
                            textColor: "#FFFF4466",
                            fontSize: 12
                    )
                }
            }
        }

        // 分期免息
        if (skuDO?.freePhases && TextUtils.isEmpty(hint) && needInstallment) {
            hint = "该商品最高享" + skuDO?.freePhases?.toString() + "期分期免手续费"
            installmentStyle = new TextStyle(
                    textColor: "#FFFF5777",
                    fontSize: 13
            )
        }

        return new HintWrapperVO(
                hint: hint,
                textStyle: installmentStyle
        )
    }

    private static String formatDelayTime(int minHours, int maxHours) {
        if (minHours == maxHours) {
            if (minHours > 72) {
                return "${minHours.intdiv(24)}日"
            } else {
                return "${minHours}小时"
            }
        } else {
            if (maxHours > 72) {
                if (minHours <= 72) {
                    return "${minHours}小时-${maxHours.intdiv(24)}日"
                } else {
                    return "${minHours.intdiv(24)}-${maxHours.intdiv(24)}日"
                }
            } else {
                return "${minHours}-${maxHours}小时"
            }
        }
    }

    // 改为不展示区间，只展示最晚时间
    private static String formatDelayTimeV2(int minHours, int maxHours) {
        if (maxHours > 72) {
            return "${maxHours.intdiv(24)}日"
        } else {
            return "${maxHours}小时"
        }
    }
}
