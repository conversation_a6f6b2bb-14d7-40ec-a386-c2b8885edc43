package groovy.mgj.app.common.common

/**
 * Created by wuy<PERSON> on 2019-09-09.
 * 控制 PriceBannerV2、SummaryV4、AtmosphereBannerV2 的氛围展示
 */
class ActivityKey {
    // 限时爆款
    public static final String XSBK   = "xsbk"
    // 新品
    public static final String XINP   = "xinp"
    // 闪购
    public static final String SHANGO = "shango"
    // 非闪购爆款比价，区别于 nbt=117 的闪购爆款比价
    public static final String COMPARE_PRICE = "comparePrice"
}
