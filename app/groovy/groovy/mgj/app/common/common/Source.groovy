package groovy.mgj.app.common.common

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.mogujie.detail.core.adt.DetailContext
import org.apache.http.util.TextUtils

import javax.annotation.Nullable

/**
 * Created by wuy<PERSON> on 2020/4/3.
 *
 * 处理sourceParams相关的逻辑
 * 格式格式sourceParams={"type":x, "xxx":"xxx"...}
 */
class Source {
    Type type

    static enum Type {
        // 直播商品进商城商品
        // 格式sourceParams={"type":1,"actorId":"1er9va0"}
        Live,
        UNKNOWN
    }

    @Nullable
    static Source with(DetailContext context) {
        if (context == null) return null
        try {
            String sourceParams
            if (!TextUtils.isEmpty(sourceParams = context.getParam("sourceParams"))) {
                Gson gson = new Gson()
                JsonObject json = gson.fromJson(sourceParams, JsonObject)
                Type type = toType(json.get("type").getAsInt())
                return new Source(type: type)
            }
        } catch (Exception ignore) {
        }
        return null
    }

    private static Type toType(int originType) {
        switch (originType) {
            case 1:
                return Type.Live
            default:
                return Type.UNKNOWN
        }
    }
}
