package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.BottomBarUtil
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.SKUDataVOCommunicationType
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/8/15.
 */
@Translator(id = "bottomBarPintuan")
class BottomBarPintuan implements IFourDependTranslator<ItemBaseDO, GroupbuyingDO, PinTuanDO, SkuDO, Object> {
    static class BottomBarPintuanVO {
        Boolean isFaved
        String iid
        String shopUrl
        Boolean addCartTips
        String imUrl
        int addCartSkuCommunicationType
        BottomBarPintuanButtonVO normalBuy
        BottomBarPintuanButtonVO pintuanBuy

        //以下字段从客户端10.2.0版本开始生效
        /**
         * 是否隐藏加入购物车按钮
         */
        boolean hideAddCartButton
        /**
         * H5下单页的BaseURL
         */
        String buyBaseUrl
        /**
         * 普通购买参数
         */
        String normalBuyParams
        /**
         * 拼团购买参数
         */
        String pintuanBuyParams
        /**
         * ptp参数占位符字段
         */
        String ptpPlaceHolder
        /**
         * liveParams占位符字段
         */
        String liveParamsPlaceHolder
        /**
         * fashionParams占位符字段
         */
        String fashionPlaceHolder
    }
    static class BottomBarPintuanButtonVO {
        String price
        String text
        int skuCommunicationType
    }
    @Override
    Object translate(ItemBaseDO input1, GroupbuyingDO groupbuyingDO, PinTuanDO pintuanDO, SkuDO skuDO) {
        //非拼团，返回空
        if (!Tools.isPintuan(input1, pintuanDO)) {
            return new Object()
        }

        def ret = new BottomBarPintuanVO(
                shopUrl: input1.shopId ? "mgj://shop?shopId=${input1.shopId}" : null,
                isFaved: input1.isFaved,
                iid: input1.iid,
                addCartTips: input1.addCartTips,
                addCartSkuCommunicationType: SKUDataVOCommunicationType.PINTUAN,
                imUrl: (input1.shopId && input1.iid && input1.userId) ? "mgjim://talk?bid=${input1.shopId}&goodsId=${input1.iid}&userId=${input1.userId}&shopid=${input1.shopId}&login=1" : null,
                normalBuy : new BottomBarPintuanButtonVO(
                        price: "¥${input1?.lowNowPrice}",
                        text: "单独购买",
                        skuCommunicationType: SKUDataVOCommunicationType.DEFAULT
                ),
                pintuanBuy : new BottomBarPintuanButtonVO(
                        price: "¥${pintuanDO?.skuInfo?.lowNowPrice}",
                        text: "${pintuanDO?.tuanNum}人拼团",
                        skuCommunicationType: SKUDataVOCommunicationType.PINTUAN
                )
        )
        ret.hideAddCartButton = (input1.virtualItemType != VirtualItemType.NORMAL)
        ret.buyBaseUrl = BottomBarUtil.getBuyBaseUrl(input1)
        ret.normalBuyParams = BottomBarUtil.getBuyParamsString(skuDO, input1, null)
        ret.pintuanBuyParams = BottomBarUtil.getBuyParamsString(skuDO, input1, pintuanDO)
        ret.ptpPlaceHolder = BottomBarUtil.getPtpPlaceHolder(input1)
        ret.liveParamsPlaceHolder = BottomBarUtil.getLiveParamsPlaceHolder(input1)
        ret.fashionPlaceHolder = BottomBarUtil.getFashionParamsPlaceHolder(input1)

        return ret
    }
}
