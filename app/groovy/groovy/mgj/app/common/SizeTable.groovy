package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import groovy.mgj.app.common.common.SizeTableProvider
import groovy.mgj.app.vo.ItemParams

/**
 * Created by fufeng on 2017/5/22.
 */

@Translator(id = "sizeTable", defaultValue = DefaultType.NULL)
class SizeTable implements IOneDependTranslator<ItemParamsDO, Object> {
   @Override
    ItemParams translate(ItemParamsDO itemParamsDO) {
       if (itemParamsDO == null) {
           // 业务上可以返回NULL
           return null
       }
       def sizeTable = SizeTableProvider.getSizeTable(itemParamsDO)
       return sizeTable
   }
}