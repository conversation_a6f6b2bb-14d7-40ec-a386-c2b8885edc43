package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.collcationset.domain.CollcationSetDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetCampaignForDetailDTO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetItemGroupDTO
/**
 * Created by fufeng on 2017/3/28.
 */
@Translator(id = "collocationV2", defaultValue = DefaultType.NULL)
class CollocationV2 implements ITwoDependTranslator<ItemBaseDO, CollcationSetDO, Object> {

    static String currentItemId //当前商品的iid

    static class CollocationV2VO {
        String title
        CollocationElementVO[] list
    }

    static class CollocationElementVO {
        String name
        String totalPrice
        String discount //已优惠：¥78.8
        String countString //5件商品
        ArrayList list //展示的图片List,现在至多两张
        String url //跳转链接

        CollocationElementVO(CollocationSetCampaignForDetailDTO dto, Integer index) {
            if (dto == null) {
                return
            }

            this.name = dto.name
            if (this.name != null && !this.name.endsWith(":")) {
                this.name = "${this.name}："
            }
            this.list = new ArrayList()
            long totalPrice = 0
            long priceDiff = 0
            for (int i = 0; i < dto?.collocationSetItemGroupDTOs?.size(); i++) {
                CollocationSetItemGroupDTO element = dto.collocationSetItemGroupDTOs[i]
                long originalPrice = element.originalPrice
                long diff = originalPrice - element.proPrice
                priceDiff += diff
                totalPrice += element.proPrice
                //目前只展示2张
                if (i<2) {
                    this.list.add(i, element.img)
                }
            }
            //钱的单位都是分,返回的时候要带两位小数的
            if (priceDiff <= 0) {
                priceDiff = 0
            }
            this.totalPrice = "¥" + NumUtil.formatPriceDrawer((int)totalPrice)
            this.discount = "已优惠：¥" + NumUtil.formatPriceDrawer((int)priceDiff)
            this.countString = "${dto?.collocationSetItemGroupDTOs?.size()}件商品"
            this.url = "mgj://collocation?iid=${currentItemId}&initIndex=${index}"
        }
    }

    @Override
    Object translate(ItemBaseDO input1, CollcationSetDO input2) {
        if (!input2){
            // 业务上可以返回NULL
            return null
        }
        ArrayList<CollocationSetCampaignForDetailDTO> collocationListDO = input2
        currentItemId = input1?.iid

        CollocationV2VO VO = new CollocationV2VO()
        VO.title = "搭配套餐(${collocationListDO.size()})"
        collocationListDO.collect {

        }
        VO.list = collocationListDO.indexed().collect { index, item ->
            new CollocationElementVO(item, index)
        }
        return VO
    }
}