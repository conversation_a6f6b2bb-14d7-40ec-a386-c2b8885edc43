package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shopRecommend.domain.ShopRecommendDO
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.Tools


@Translator(id = "shopRecommendV2")
class ShopRecommendV2 implements IFourDependTranslator<ItemBaseDO, ShopRecommendDO, PinTuanDO, LiveSimpleDO, ShopRecommendV2VO> {

    static class ShopRecommendV2VO extends HideControl {
        Map<String, String> sectionTitle
        Map<String, ArrayList<ShopRecommendV2VOItem>> shopRecommendData
    }

    static class ShopRecommendV2VOItem {
        String price
        String sales
        String priceTag
        String image
        String link
        String title
        String iid
        String acm
    }

    @Override
    ShopRecommendV2VO translate(ItemBaseDO itemBaseDO, ShopRecommendDO shopRecommendDO, PinTuanDO pinTuanDO, LiveSimpleDO liveSimpleDO) {

        // 有主播信息时屏蔽店铺推荐
        if (liveSimpleDO?.actUserInfo) {
            return new ShopRecommendV2VO(_extra_control_hide_: true)
        }

        def vo = new ShopRecommendV2VO(
                _extra_control_hide_: false,
                shopRecommendData: [:]
        )
        boolean needTitle = false
        if (shopRecommendDO?.recommendItemList?.size() > 0) {
            def list = []
            shopRecommendDO.recommendItemList.each {
                def item = new ShopRecommendV2VOItem(
                        price: it.price,
                        sales: it.sale,
                        priceTag: it.isPintuan ? ImageUtil.img("/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png") : "",
                        image: it.img,
                        title: it.title,
                        link: "mgj://detail?iid=${it.iid}",
                        iid: it.iid
                )
                list << item
            }
            //list走缓存
            vo.shopRecommendData.put("list", list)
            needTitle = true
        }

        if (shopRecommendDO?.realTimeRecommendItemList?.size() > 0){
            def list = []
            shopRecommendDO.realTimeRecommendItemList.each {
                def item = new ShopRecommendV2VOItem(
                        price: it.price,
                        sales: it.sale,
                        priceTag: it.isPintuan ? ImageUtil.img("/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png") : "",
                        image: it.img,
                        title: it.title,
                        link: "mgj://detail?iid=${it.iid}&acm=${it.acm}",
                        iid: it.iid,
                        acm: it.acm
                )
                list << item
            }
            vo.shopRecommendData.put("systemList", list)
            needTitle = true
        }
        else {
            //systemList不走缓存，就算是空list也要放进去
            vo.shopRecommendData.put("systemList", [])
        }

        // 有数据时拼上title
        if (needTitle) {
            vo.sectionTitle = ["key": "店铺推荐"]
        }

        // 直播商品进图墙的商品，隐藏店铺推荐
        if (Tools.isLiveSource()) {
            vo = new ShopRecommendV2VO()
        }

        return vo
    }

}
