package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import org.apache.http.util.TextUtils

/**
 * Created by fufeng on 2017/8/15.
 */
@Translator(id = "pintuanInfo", defaultValue = DefaultType.EMPTY_MAP)
class PintuanInfo implements IOneDependTranslator<PinTuanDO, PintuanInfoVO> {
    static class PintuanInfoVO {
        String titleImageUrl
        String ruleInfo
        String ruleLinkTitle
        String ruleLinkUrl
        String pintuanListTitle
    }

    @Override
    PintuanInfoVO translate(PinTuanDO pintuanDO) {
        if (!pintuanDO) {
            // defaultValue = DefaultType.EMPTY_MAP 动态请求时会返回空对象
            return null
        }
        def ret = new PintuanInfoVO(
                titleImageUrl: ImageUtil.img("/mlcdn/c45406/170831_54cee806142bkcj9c89dl7ajga7a9_240x66.png"),
                ruleInfo: "开团后邀请好友参团，人数不足自动退款",
                ruleLinkTitle: "详细规则",
                ruleLinkUrl: StrategyUpUtil.upUrl("http://pintuan.mogujie.com/rule"),
                pintuanListTitle: "小伙伴发起的拼团：",
        )

        return ret
    }
}
