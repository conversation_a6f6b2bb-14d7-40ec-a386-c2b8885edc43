package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.mgj.app.vo.CountdownVO


@Translator(id = "countDownNormalV3Group")

class CountDownNormalV3Group implements ISixDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO,PresaleDO, CountDownNormalV3GroupVO> {
    static class CountDownNormalV3GroupVO {
        CountdownVO countDownNormalV2
    }
    @Override
    CountDownNormalV3GroupVO translate(ActivityDO input1, GroupbuyingDO input2, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presale) {
        CountDownNormalV2 translator = new CountDownNormalV2()
        CountdownVO countdownVO = translator.translate(input1, input2, itemBaseDO, pinTuanDO, normalCountdownDO)
        if (countdownVO == null || countdownVO?.countdown == null || countdownVO?.countdownBgImg == null) {
            return new CountDownNormalV3GroupVO()
        }
        else if (presale != null || input1?.activityState == 1.intValue() || input2?.status == TuanStatus.PRE)
        {   //只有预售或预热，才返回countdown
            return new CountDownNormalV3GroupVO(
                    countDownNormalV2: countdownVO
            )
        }
        else {
            return new CountDownNormalV3GroupVO()
        }
    }
}
