package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.TitleIconAndPriceHistoryUtil
import groovy.mgj.app.vo.PriceHistoryVO
import org.apache.http.util.TextUtils

/**
 * Created by wuy<PERSON> on 2019-09-26.
 */
@Translator(id = "historyPrice", defaultValue = DefaultType.EMPTY_MAP)
class PriceHistory implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, RateDO, ShopDO, PinTuanDO, NormalCountdownDO, SkuDO, PriceHistoryVO> {

    @Override
    PriceHistoryVO translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, PresaleDO presaleDO, GroupbuyingDO groupbuyingDO, RateDO rateDO, ShopDO shopDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, SkuDO skuDO) {
        String titleIcon
        String priceHistory
        (titleIcon, priceHistory) = TitleIconAndPriceHistoryUtil.getTitleIcon(itemBaseDO, activityDO, groupbuyingDO, pinTuanDO, normalCountdownDO, presaleDO)
        // 是否展示历史低价图，由是否有价格证明决定
        if (itemBaseDO != null && itemBaseDO.canShowStrikethroughPrice
                && !TextUtils.isEmpty(priceHistory)) {
            return new PriceHistoryVO(
                    background: ImageUtil.img(priceHistory)
            )
        }
        return PriceHistoryVO.EMPTY
    }
}