package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/4/1.
 */

@Translator(id = "themeName")
class ThemeName implements ITwoDependTranslator<ItemBaseDO, ShopDO, String> {
    @Override
    String translate(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        Boolean isExcellent = Tools.isGoodItem(itemBaseDO, shopDO)
        if (isExcellent) {
            return "mgj_highlight_red"
        }
        else {
            return "mgj_normal_pink"
        }
    }
}