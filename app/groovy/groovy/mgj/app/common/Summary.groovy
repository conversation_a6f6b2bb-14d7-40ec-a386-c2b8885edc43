package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.activity.domain.GiftType
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.ActivityItemVO
import groovy.mgj.app.vo.ActivityVO
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.common.common.SummaryRateInfoVO
import groovy.mgj.app.vo.PriceTagVO
import groovy.mgj.app.vo.ShareInfo
import groovy.mgj.app.vo.SummaryVO

@Translator(id = "summary")
class Summary implements ISixDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, RateDO, ShopDO, Object> {
    @Override
    SummaryVO translate(ItemBaseDO input1, ActivityDO input2, PresaleDO input3, GroupbuyingDO input4, RateDO rateDO, ShopDO shopDO) {
        if (!input1) {
            return null
        }
        def summary = new SummaryVO()

        def defaultBgColor = "#FFE8EE"
        def defaultTextColor = "#FF2255"

        //默认属性
        summary.priceColor = "#333333"
        summary.title = input1.title
        summary.highNowPrice = input1.highNowPrice
        summary.lowNowPrice = input1.lowNowPrice
        summary.highPrice = input1.highPrice
        summary.lowPrice = input1.lowPrice
//        InvokerHelper.setProperties(summary, input1.properties)

        //优先级 预售>大促>团购>普通
        if (input3 != null) {
            //预售
            summary.with {
                price = input3.totalPrice
                oldPrice = input1.lowNowPrice
                priceTag = new PriceTagVO(
                        text: "预售价",
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                eventPrice = input3.deposit
                eventPriceTag = input3.presaleDesc ? new PriceTagVO(
                        text: "定金",
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                ) : null
            }
        } else if (input2 != null && (input2.warmUpPrice || input2.inActivityItem)) {
            //大促
            summary.with {
                updatePrices()
                priceTag = input2.priceDesc ? new PriceTagVO(
                        text: input2.priceDesc
                ) : null

                //正式期
                //颜色变成红色
                if (input2.inActivityItem) {
                    priceColor = defaultTextColor
                }

                //预热期
                eventPrice = input2.warmUpPrice?.price
                eventPriceColor = input2.warmUpPrice?.color
                if (eventPrice) {
                    eventPriceTag = new PriceTagVO(
                            text: input2.warmUpPrice?.priceDesc,
                            bgColor: null,//无背景色
                            textColor: defaultTextColor
                    )
                }
            }
        } else if (input4 != null && (input4.status == TuanStatus.IN || input4.status == TuanStatus.PRE)) {
            //团购中
            if (input4.status == TuanStatus.IN) {
                if (input1) {
//                    InvokerHelper.setProperties(summary, input1.properties)
                    summary.updatePrices()
                } else {
                    summary.price = input4.price
                }
                summary.priceTag = new PriceTagVO(
                        text: '团购价',
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                if (input4.bizType == TuanBizType.UZHI) {
                    summary.priceTag.text = "U质团"
                }
                else if (input4.bizType == TuanBizType.PINPAI) {
                    summary.priceTag.text = "品牌团"
                }
            } else if (input4.status == TuanStatus.PRE) {
                //预热期
                if (input1) {
//                    InvokerHelper.setProperties(summary, input1.properties)
                    summary.updatePrices()
                }
                summary.eventPrice = input4.price
                summary.eventPriceColor = defaultTextColor
                summary.eventPriceTag = new PriceTagVO(
                        text: '团购价',
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                if (input4.bizType == TuanBizType.UZHI) {
                    summary.eventPriceTag.text = "U质团"
                }
                else if (input4.bizType == TuanBizType.PINPAI) {
                    summary.eventPriceTag.text = "品牌团"
                }
            }

        } else {
            //普通
//            InvokerHelper.setProperties(summary, input1.properties)
            summary.updatePrices()

            if (!input2?.hideDiscount && input1?.discountDesc) {
                summary.priceTag = new PriceTagVO(
                        text: input1.discountDesc,
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
            }
        }

        //其他属性
        summary.with {
            title = input1?.title

            eventTags = input2?.eventTags?.collect {
                new PriceTagVO(
                        text: it.tagText,
                        bgColor: it.tagBgColor,
                        textColor: defaultTextColor
                )
            }

            //去麦田获取分享Icon和Text
            def shareMaitInfo = MaitUtil.getMaitData(38873)
            if (shareMaitInfo?.get(0)) {
                shareInfo = new ShareInfo(
                        shareIcon: shareMaitInfo.get(0)?.get("shareIcon"),
                        shareText: shareMaitInfo.get(0)?.get("shareText")
                );
            }

            //非ESI时才返回
            if (!DetailContextHolder.get().isDyn()) {
                //DSR信息
                if (rateDO != null && input1 != null) {
                    SummaryRateInfoVO summaryRateInfoVO = new SummaryRateInfoVO(input1, rateDO, shopDO)
                    summary.rate = summaryRateInfoVO
                }
            }
        }

        //clean up 为了esi能覆盖
        summary.with {
            //oldPrice
            if (oldPrice == null) {
                oldPrice = ""
            }
            //TODO:安卓预热期会有问题，所以不要在预热期做这个覆盖
            //priceTag
            if (!input2?.warmUpPrice) {
                //非预热期
                if (priceTag == null) {
                    priceTag = new PriceTagVO(
                            text: null,
                            textColor: null,
                            bgColor: null
                    )
                }
            }
            //eventPrice
            if (eventPrice == null) {
                eventPrice = ""
            }
            //eventPriceTag
            if (eventPriceTag == null) {
                eventPriceTag = new PriceTagVO(
                        text: null,
                        textColor: null,
                        bgColor: null
                )
            }
            //eventTags
            if (eventTags == null) {
                eventTags = []
            }
            //activity
            if (activity == null) {
                activity = new ActivityVO()
            }
        }

        return summary
    }
}
