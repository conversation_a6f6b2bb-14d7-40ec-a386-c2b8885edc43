package groovy.mgj.app.common.anchor

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import groovy.mgj.app.common.common.AnchorDataProvider
import groovy.mgj.app.common.common.AnchorDataVO

/**
 * Created by fufeng on 2017/3/29.
 */
@Translator(id = "anchorDetail")
class AnchorDetail implements IZeroDependTranslator<AnchorDataVO> {
    @Override
    AnchorDataVO translate() {
        int index = 1;
        return AnchorDataProvider.getAnchorDataAtIndex(index)
    }
}