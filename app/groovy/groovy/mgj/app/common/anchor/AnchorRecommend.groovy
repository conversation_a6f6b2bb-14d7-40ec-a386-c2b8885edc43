package groovy.mgj.app.common.anchor

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.AnchorDataProvider
import groovy.mgj.app.common.common.AnchorDataVO

/**
 * Created by fufeng on 2017/3/29.
 */
@Translator(id = "anchorRecommend")
class AnchorRecommend implements IZeroDependTranslator<AnchorDataVO> {
    @Override
    AnchorDataVO translate() {
        // 直播来源的商品不展示热卖推荐
        if (groovy.mgj.app.vo.Tools.isLiveSource()) {
            return AnchorDataVO.getEmpty()
        }

        int index = 2;
        return AnchorDataProvider.getAnchorDataAtIndex(index)
    }
}