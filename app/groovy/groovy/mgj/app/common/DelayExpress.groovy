package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.vo.HideControl

/**
 * Created by pananping on 2020/11/15.
 */
@Translator(id = "delayExpress", defaultValue = DefaultType.NULL)
class DelayExpress implements ITwoDependTranslator<ShopDO, SkuDO, Object> {

    class DelayExpressHintConfigVO {
        String title = "发货"
        String titleColor = "#999999"
        String textLine1
        String textColor1 = "#333333"
        String textLine2
        String textColor2 = "#999999"
    }

    class DelayExpressVO extends HideControl {
        String componentKey = "forDelayShipping"
        DelayExpressHintConfigVO defaultData
        Map<String, DelayExpressHintConfigVO> showKeys
    }

    @Override
    Object translate(ShopDO shopDO, SkuDO skuDO) {
        if (!skuDO) {
            return new DelayExpressVO(_extra_control_hide_: true)
        }

        // 春节打烊期间不展示
        if (Tools.inSpringFestival() && Tools.isSpringFestivalShutdownItem()) {
            return new DelayExpressVO(_extra_control_hide_: true)
        }

        Integer itemPromiseShipTime = shopDO?.itemPromiseDeliveryTime
        Integer shopAvgShipTime = shopDO?.shopAvgDeliveryTime

        Map<String, DelayExpressHintConfigVO> showKeys = new HashMap<>()

        DelayExpressHintConfigVO slowestDelayedShipSKU = null
        Integer longestDelayHour = null

        skuDO.skus.each {
            if (it.stockId) {
                DelayExpressHintConfigVO vo = new DelayExpressHintConfigVO()
                if (it.delayHours) {
                    Integer delayShipDay = it.delayHours < 24 ? 1 : it.delayHours / 24
                    if (shopAvgShipTime && shopAvgShipTime < it.delayHours * 3600) {
                        String avgShipDay = String.format("%.1f", (double) shopAvgShipTime / 3600 / 24)
                        if (avgShipDay == "0.0") {
                            avgShipDay = "1.0"
                        }
                        vo.textLine1 = "该店平均发货时长为${avgShipDay}天 "
                        vo.textLine2 = "付款后最晚于${delayShipDay}天内发货"
                    } else {
                        vo.textLine1 = "付款后最晚于${delayShipDay}天内发货"
                    }
                    if (!slowestDelayedShipSKU || longestDelayHour < it.delayHours) {
                        slowestDelayedShipSKU = vo
                        longestDelayHour = it.delayHours
                    }
                    showKeys.put(it.stockId, vo)
                } else if (itemPromiseShipTime) {
                    Integer itemPromiseShipDay = itemPromiseShipTime < 86400 ? 1 : itemPromiseShipTime / 86400
                    vo.textLine1 = "付款后最晚于${itemPromiseShipDay}天内发货"
                    showKeys.put(it.stockId, vo)
                } else {
                    // 既没有 SKU 级别的承诺发货时间，也没有商品级别的承诺发货时间，没办法展示
                }
            }
        }

        // 没有一个 SKU 设置了发货时长，不展示
        if (!slowestDelayedShipSKU) {
            return new DelayExpressVO(_extra_control_hide_: true)
        }

        return new DelayExpressVO(defaultData: slowestDelayedShipSKU, showKeys: showKeys, _extra_control_hide_: false)
    }
}