package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.BottomBarUtil
import groovy.mgj.app.vo.GroupBuyType
import groovy.mgj.app.vo.Tools

@Translator(id = "bottomBarNormal")
class BottomBarNormal implements ISixDependTranslator<ItemBaseDO, ExtraDO, GroupbuyingDO, SkuDO, PinTuanDO, LiveDO, Object> {
    static class BottomBarNormalVO{
        String iid
        String imUrl
        String shopUrl
        String shopId
        Boolean isFaved
        Double saleStartTime
        Integer saleType
        Integer state
        Boolean addCartTips

        String waitForSaleNoticeTitle
        String waitForSaleNoticeContent
        String itemURL

        //以下字段从9.5.0版本开始生效
        /**
         * 普通商品底部按钮文案，一般情况下都是"立即购买"，U质团商品在正式期展示为"参团购买"
         */
        String buyButtonText

        //以下字段从客户端10.2.0版本开始生效
        /**
         * 是否隐藏加入购物车按钮
         */
        boolean hideAddCartButton
        /**
         * H5下单页的BaseURL
         */
        String buyBaseUrl
        /**
         * 普通购买参数
         */
        String normalBuyParams
        /**
         * ptp参数占位符字段
         */
        String ptpPlaceHolder
        /**
         * liveParams占位符字段
         */
        String liveParamsPlaceHolder
        /**
         * fashionParams占位符字段
         */
        String fashionPlaceHolder
    }
    @Override
    Object translate(ItemBaseDO input1, ExtraDO input2, GroupbuyingDO groupbuyingDO, SkuDO skuDO, PinTuanDO pinTuanDO, LiveDO aLiveDO) {
        if (!input1) {
            return null
        }
        BottomBarNormalVO VO = new BottomBarNormalVO(
                imUrl: (input1.shopId && input1.iid && input1.userId) ? "mgjim://talk?bid=${input1.shopId}&goodsId=${input1.iid}&userId=${input1.userId}&shopid=${input1.shopId}&login=1" : null,
                shopUrl: input1.shopId ? "mgj://shop?shopId=${input1.shopId}" : null,
                shopId: input1.shopId,
                isFaved: input1.isFaved,
                saleStartTime: input2?.onSaleTime,
                saleType: input1.saleType,
                iid: input1.iid,
                state: input1.state,
                addCartTips: input1.addCartTips,
                itemURL: Tools.getH5Url(IdConvertor.urlToId(input1?.iid)),
                buyButtonText: "立即购买"
        )
        //待开售商品
        if (VO.state == 3 && input2?.onSaleTime) {
            VO.waitForSaleNoticeTitle = "开售提醒：${input1.title}"
            VO.waitForSaleNoticeContent = "【蘑菇街开售提醒】：${input1.title} ${VO.itemURL}"
        }

        //U质团商品的购买按钮文案需要变成参团购买
        if (groupbuyingDO?.status == TuanStatus.IN) {
            GroupBuyType groupBuyType = GroupBuyType.getGroupBuyType(groupbuyingDO)
            if (groupBuyType == GroupBuyType.UZHI) {
                VO.buyButtonText = "参团购买"
            }
        }

        /**
         * 直播特卖商品不返回 shopUrl @苏哲
         */
        if (aLiveDO) {
            VO.shopUrl = ""
        }

        VO.hideAddCartButton = (input1.virtualItemType != VirtualItemType.NORMAL)
        VO.buyBaseUrl = BottomBarUtil.getBuyBaseUrl(input1)
        VO.normalBuyParams = BottomBarUtil.getBuyParamsString(skuDO, input1, pinTuanDO)
        VO.ptpPlaceHolder = BottomBarUtil.getPtpPlaceHolder(input1)
        VO.liveParamsPlaceHolder = BottomBarUtil.getLiveParamsPlaceHolder(input1)
        VO.fashionPlaceHolder = BottomBarUtil.getFashionParamsPlaceHolder(input1)

        return VO
    }
}