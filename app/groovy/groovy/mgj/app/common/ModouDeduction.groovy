package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import groovy.mgj.app.common.common.AnchorDataProvider
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

/**
 * Created by fufeng on 2017/11/14.
 */
@Translator(id = "modouDeduction", defaultValue = DefaultType.EMPTY_MAP)
class ModouDeduction implements IOneDependTranslator<ExtraDO, Object> {
    static class ModouDeductionVO {
        ModouDeductionItemVO[] platformCoupon //叫这个名字是历史遗留问题
    }

    static class ModouDeductionItemVO {
        String iconTitle
        String title
        String accessoryTitle
        String linkUrl
        TextStyle iconTitleTextStyle
    }

    @Override
    ModouDeductionVO translate(ExtraDO extraDO) {
        ModouDeductionVO ret = new ModouDeductionVO()
        //麦田 50418
        def promotionTags = MaitUtil.getMaitData(89807)
        def maitInfo =  promotionTags?.get(0)
        def iconTitle = maitInfo?.get("iconTitle")
        String maitTitle = maitInfo?.get("title")

        if (iconTitle && maitTitle && extraDO?.modouDiscount) {
            def theTitle = maitTitle.replace("{{value}}", extraDO.modouDiscount)
            ModouDeductionItemVO item = new ModouDeductionItemVO(
                    iconTitle: iconTitle,
                    title: theTitle,
                    accessoryTitle: maitInfo.get("accessoryTitle"),
                    linkUrl: maitInfo.get("linkUrl"),
            )
            def textStyle = new TextStyle(
                    textColor: maitInfo.get("iconTextColor") ?: "#FFFFFF",
                    backgroundColor: maitInfo.get("iconBackgroundColor") ?: "#FF4B6D",
                    fontSize: 10,
            )
            item.iconTitleTextStyle = textStyle
            ret.platformCoupon = [item]
        }
        return ret
    }
}
