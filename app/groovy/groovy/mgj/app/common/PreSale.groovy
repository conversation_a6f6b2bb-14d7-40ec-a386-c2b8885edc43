package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.presale.domain.PresaleDO

@Translator(id = "preSale")
class PreSale implements IOneDependTranslator<PresaleDO, Object>{
    private static final long PRE_SOURCE_ID = 5191L

    static String presaleDefaultColor = '#EDF9FF'

    class PreSaleVO{
        String presaleDesc
        String presaleEndDate
        String presaleDate
        String topImage
        String backgroundColor
        String backgroundColorV2
        String textColor
        String topImageV2
    }

    @Override
    PreSaleVO translate(PresaleDO input1) {
        if (!input1){
            return new PreSaleVO() //这样可以使esi覆盖缓存数据
        }
//        List<Map<String, Object>> resources = MaitUtil.getMaitData(PRE_SOURCE_ID)
        return new PreSaleVO(
                presaleDesc: input1.presaleDesc,
                presaleEndDate: input1.presaleEndDate,
                presaleDate: input1.presaleDate,
                topImage: input1.titleIcon,
                backgroundColor: presaleDefaultColor,
                backgroundColorV2: "#FEF8EB",
                textColor: "#815600",
//                topImageV2: resources?.get(0)?.get("preIconV3")
                topImageV2: ImageUtil.img("/mlcdn/c45406/190409_7a67ib1af53a5a3j9di5hec61fcg0_57x29.png")
        )
    }
}
