package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.Tools

@Translator(id = "addCartRedPackets")
class AddCartRedPackets implements ITwoDependTranslator<ItemBaseDO, ShopDO, Object> {
    class AddCartRedPacketsVO{
        Boolean redPacketsSwitch
        String shopId
        ArrayList<String> itemTags
        String iid
    }

    @Override
    Object translate(ItemBaseDO input1, ShopDO shopDO) {
        if (!input1 || !input1.redPacketSwitch){
            return new AddCartRedPacketsVO()//JsonNull.INSTANCE
        }
        def result = new AddCartRedPacketsVO(
                redPacketsSwitch: input1.redPacketSwitch,
                shopId: input1.shopId,
                itemTags: input1.numTags,
                iid: input1.iid
        )
        if (Tools.isGoodItem(input1, shopDO)) {
            //为了Cover之前客户端-麦田实现上的一个问题,需要专门给良品商品添加一个"26"的标,@suzhe @xiumi
            if (result.itemTags == null) {
                result.itemTags  = ["26"]
            }
            else {
                result.itemTags.add("26")
            }
        }
        return result
    }
}
