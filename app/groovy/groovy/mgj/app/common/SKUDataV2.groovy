package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SizePropData
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.module.sku.domain.StylePropData
import groovy.mgj.app.vo.SKUVO
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUDataVOActivityType
import org.apache.commons.collections4.CollectionUtils

@Translator(id = "skuDataV2")
class SKUDataV2 implements IFourDependTranslator<SkuDO, ItemBaseDO,PresaleDO, GroupbuyingDO, Object> {

    @Override
    SKUDataVO translate(SkuDO input1, ItemBaseDO input2, PresaleDO input3, GroupbuyingDO input4) {
        if (!input1 || !input2){
            return null
        }

        String defaultImageUrl = null;
        if (CollectionUtils.isNotEmpty(input2?.topImages)){
            defaultImageUrl = input2.topImages?.first();
        }
        def vo = new SKUDataVO(
                skuInfo: new SKUVO(input1),
                iid: input2?.iid,
                isPresale: input3 != null,
                defaultImageUrl: defaultImageUrl,
                mainPriceStr: input1.mainPriceStr,
                subPriceStr: input1.subPriceStr,
                activityType: SKUDataVOActivityType.DEFAULT
        )
        if(DetailContextHolder.get().getParam("template").startsWith("1-3")) {
            if (input1?.styleKey == null && input1?.sizeKey) {
                vo?.skuInfo?.props?.add(0, new PropInfo())
            }
        }
        if (input3 != null) {
            vo.activityType = SKUDataVOActivityType.PRESALE;
        }
        else if (input4?.status == TuanStatus.IN) {
            vo.activityType = SKUDataVOActivityType.TUANGOU;
        }
        return vo;

    }
}
