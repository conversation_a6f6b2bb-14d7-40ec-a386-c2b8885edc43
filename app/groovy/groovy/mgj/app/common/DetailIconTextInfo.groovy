package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import groovy.mgj.app.vo.TextStyle

/**
 * Created by fufeng on 2017/3/9.
 */

@Translator(id = "detailIconTextInfo")
class DetailIconTextInfo implements IZeroDependTranslator <DetailIconTextInfoVO> {

    static class DetailIconTextInfoVO{
        DetailIconTextInfoItemVO[] platformCoupon //叫这个名字是历史遗留问题
    }
    static class DetailIconTextInfoItemVO{
        String iconTitle
        String title
        String accessoryTitle
        String linkUrl
        TextStyle iconTitleTextStyle
    }

    DetailIconTextInfoVO translate() {

        DetailIconTextInfoVO ret = new DetailIconTextInfoVO();
        //麦田 39942
        def promotionTags = MaitUtil.getTargetedMaitData(39942)
        def maitInfo =  promotionTags?.get(0)
        if (maitInfo?.get("iconTitle") && maitInfo?.get("title")) {
            DetailIconTextInfoItemVO[] platformCoupon = new DetailPlatformCoupon[1];
            DetailIconTextInfoItemVO item = new DetailIconTextInfoItemVO(
                    iconTitle: maitInfo.get("iconTitle"),
                    title: maitInfo.get("title"),
                    accessoryTitle: maitInfo.get("accessoryTitle"),
                    linkUrl: maitInfo.get("linkUrl"),
            )
            def textStyle = new TextStyle(
                    textColor: maitInfo.get("iconTextColor") ?: "#FFFFFF",
                    backgroundColor: maitInfo.get("iconBackgroundColor") ?: "#FF5577",
                    fontSize: 10,
            )
            item.iconTitleTextStyle = textStyle
            platformCoupon[0] = item;
            ret.platformCoupon = platformCoupon;
        }

        return ret
    }
}