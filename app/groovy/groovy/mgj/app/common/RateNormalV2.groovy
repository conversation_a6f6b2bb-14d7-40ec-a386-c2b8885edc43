package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.rate.domain.DetailRate
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.rate.domain.RateUserInfo
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.service.rate.domain.Video
import com.mogujie.service.rate.domain.tag.RateTag
import com.mogujie.service.rate.util.RateUtils
import groovy.mgj.app.common.common.Source
import groovy.mgj.app.vo.Tools
import org.apache.http.util.TextUtils

import java.text.DecimalFormat

@Translator(id = "rateNormalV2", defaultValue = DefaultType.NULL)
class RateNormalV2 implements IFiveDependTranslator<RateDO, ItemBaseDO, BuyerShowDO, ExtraDO, ShopDO, Object> {

    static class RateVO {

        // >=1480，实际只用这个
        String title1480
        // <1480，用下面两个
        String title
        String subTitle

        String rateUrl
        List<RateEntryVO> list
        RateTagVO[] rateTags
        String dsrTitle
        String dsr

        static getEmpty() {
            return new RateVO()
        }
    }

    static class Tag {
        String text
    }

    static class RateEntryVO {
        Boolean canExplain
        String content
        List<Tag> tags
        Long created
        String explain
        List<String> images
        Integer isAnonymous
        Boolean isEmpty
        String level
        String rateId
        String style
        RateUserInfo user
        List userTags

        Map<String, String> sizeInfo
        String buyerShowDesc
        String buyerShowEx
        String buyerShowUrl
        boolean isBuyerShow
        List<Video> videos

        RateEntryVO() {}

        RateEntryVO(DetailRate rate) {
            this.canExplain = rate.canExplain
            this.content = rate.content
            this.created = rate.created
            this.explain = rate.explain
            this.images = rate.images
            this.isAnonymous = rate.isAnonymous
            this.isEmpty = rate.isEmpty
            this.level = rate.level
            this.rateId = rate.rateId
            this.style = rate.style
            this.user = rate.user
            this.userTags = rate.userTags
            this.sizeInfo = rate.sizeInfo
            this.isBuyerShow = rate.isBuyerShow()
            List<Video> tempVideo = new ArrayList<>()
            for (Video video : rate.videos) {
                Video newVideo = new Video(
                        videoId: video.videoId,
                        imgUrl: ImageUtil.img(video.imgUrl)
                )
                tempVideo.add(newVideo)
            }
            this.videos = tempVideo

            // 被拼好的标签文案
            String rateTagNames = rate.rateTagNames
            List<Tag> tagList = new ArrayList<>()
            if (!TextUtils.isEmpty(rateTagNames)) {
                try {
                    String separator = " "
                    rateTagNames.split(separator).each { tagText ->
                        tagList.add(new Tag(text: tagText))
                    }
                } catch (Exception ignore) {
                    tagList.clear()
                    tagList.add(new Tag(text: rateTagNames))
                }
            }
            this.tags = tagList
        }
    }

    static class RateUserVO {
        String avatar
        String profileUrl
        String tagIndex
        String uid
        String uname
    }

    static class RateTagVO {
        String emotion
        Integer num
        String property
        Integer labelId
        String labelIds
        String value
        String link

        RateTagVO(RateTag tag, String iid, String themeName) {
            if (!tag) {
                return
            }
            this.emotion = tag.emotion
            this.num = tag.num
            this.property = tag.property
            this.value = tag.value
            this.labelId = tag.labelId
            this.labelIds = tag.labelIds

            if (Tools.getAppVersion() >= 1510) {
                link = "mgj://ratelist?iid=$iid&labelIds=$labelIds&themeName=${themeName}"
            } else {
                link = "mgj://ratelist?iid=$iid&property=$property&emotion=$emotion&labelId=$labelId&themeName=${themeName}"
            }
        }
    }

    @Override
    Object translate(RateDO input1, ItemBaseDO input2, BuyerShowDO input3, ExtraDO input4, ShopDO shopDO) {
        String needDSR_CONFIG  = MetabaseUtil.get("rateShowDSR")
        boolean needDSR = needDSR_CONFIG ? Boolean.parseBoolean(needDSR_CONFIG) : false

        if (input1 == null) {
            return new RateVO(
                    title: "评价 0"
            )
        }
        ThemeName themeNameTranslator = new ThemeName()
        String themeName = themeNameTranslator.translate(input2, shopDO)

        if (input1?.rateTags && input1.rateTags.size() > 0 && input1.imgTotal && input1.imgTotal > 0) {
            RateTag imgTag = new RateTag()
            imgTag.setNum(input1.imgTotal)
            imgTag.setEmotion("positive")
            imgTag.setProperty("图片")
            imgTag.setValue("有图片")
            input1.rateTags.add(0, imgTag);
        }

        RateVO VO = new RateVO(
                title1480: "评价(${input1?.CRate})",
                title: "评价 ${input1?.CRate}",
                subTitle: "历史销量 ${input4?.sales}",
                rateUrl: "mgj://ratelist?iid=${input2?.iid}",
                rateTags: input1?.rateTags ? input1.rateTags.collect {
                    new RateTagVO(it, input2?.iid, themeName)
                } : null
        )
        VO.rateUrl = VO.rateUrl + "&themeName=${themeName}"

        // 最多返回5个评价
        List<RateEntryVO> list = new ArrayList<>()
        int maxNumRate
        try {
            String[] templateIds = DetailContextHolder.get().getParam("template").split("-")
            String templateId = templateIds[templateIds.length - 1]
            DetailType detailType = Tools.getDetailType()
            if (DetailType.NORMAL == detailType
                    && Tools.compareTemplate(templateId, "2.2.5") >= 0) {
                // 普通详情页2.2.5后返回5条数据
                maxNumRate = 5
            } else if (DetailType.FASTBUY == detailType
                    && Tools.compareTemplate(templateId, "2.1.9") >= 0) {
                // 快抢详情页2.1.9后返回5条数据
                maxNumRate = 5
            } else if (DetailType.SECKILL == detailType
                    && Tools.compareTemplate(templateId, "2.1.2") >= 0) {
                // 秒杀详情页2.1.2后返回5条数据
                maxNumRate = 5
            } else {
                maxNumRate = 1
            }
        } catch (Exception ignore) {
            maxNumRate = 1
        }

        for (int index = 0; list.size() < maxNumRate && index < input1?.list?.size(); index++) {
            DetailRate detailRateDO = (DetailRate) input1?.list?.get(index)
            if (!detailRateDO) continue

            def rateEntry = new RateEntryVO(detailRateDO)

            // 1290: 此开关若打开，将展示独立的精选晒单组件，评价中不再展示图片
            if (input1?.switchContent) {
                if ((!rateEntry.content || rateEntry.content.trim().equals("")) && (!rateEntry.tags || rateEntry.tags.isEmpty())) {
                    continue
                }
                if (!Tools.isLiveSource()) {
                    rateEntry.images = null
                    rateEntry.videos = null
                }
            }

            if (detailRateDO?.isBuyerShow()) {
                rateEntry.buyerShowDesc = "红人精选"
                rateEntry.buyerShowEx = "- Experience -"
                //这里原来是直接跳转买家秀的短链,产品要求在详情页里面统一用调到RateList的短链
                def url = VO.rateUrl
                rateEntry.buyerShowUrl = url
                rateEntry.isBuyerShow = detailRateDO.isBuyerShow()

            }
            list.add(rateEntry)
        }
        if (!list?.isEmpty()) {
            VO.list = list
        } else {
            VO.list = null
        }

        if (input1?.getCRate() == 0) {
            VO.rateUrl = null
            VO.title = "评价 0"
        }

        if (input4 == null || input4?.sales == 0.longValue()) {
            VO.subTitle = "历史销量 0"
        }

        if (!needDSR) {
            VO.dsrTitle = ""
            VO.dsr = ""
        } else {
            if (input1?.itemDsr) {
                VO.dsrTitle = "评分"
                int avgDSR = RateUtils.getDsrAvg(Arrays.asList(input1.itemDsr.desc, input1.itemDsr.quality))
                DecimalFormat decimalFormat = new DecimalFormat(".00")
                String scoreStr = decimalFormat.format((double) (avgDSR / 100))
                VO.dsr = scoreStr
            } else {
                VO.dsrTitle = "暂无评分"
                VO.dsr = ""
            }
        }

        return VO
    }
}
