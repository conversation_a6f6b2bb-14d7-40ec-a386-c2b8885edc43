package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.rate.domain.RateDO
import groovy.mgj.app.vo.Tools

/**
 * Created by YanZe on 2019-09-04.
 */
@Translator(id = "detailShowOrder", defaultValue = DefaultType.NULL)
class DetailShowOrder implements ITwoDependTranslator<RateDO, ItemBaseDO, Object> {

    static class ShowOrderItem {
        String img
        String link
    }

    static class ShowOrderVO {
        String title1480
        String title
        String more
        String link
        List<ShowOrderItem> showList

        static ShowOrderVO EMPTY = new ShowOrderVO()
    }

    @Override
    Object translate(RateDO input1, ItemBaseDO input2) {

        if (!input1 && DetailContextHolder.get().isDyn()) {
            // 动态请求rateDO给了null, 使用缓存
            return null
        }

        if (input1 == null || !input1.switchContent || input1.contentInfos?.size() < 4) {
            return ShowOrderVO.EMPTY
        }

        ShowOrderVO VO = new ShowOrderVO(
                title1480: "买家秀(${input1.shareTotal})",
                title: "晒单 ${input1.shareTotal} | 精选晒单 ${input1.contentTotal}",
                link: Tools.getAppVersion() >= 1310 ? "mgj://selectionbuyershow?itemId=${input2.iid}" : input1.contentMoreLink,
                more: "查看更多",
                showList: input1.contentInfos.subList(0, 4).collect {
                    new ShowOrderItem(
                            img: it.cover,
                            link: Tools.getAppVersion() >= 1310 ? "mgj://selectionbuyershow?itemId=${input2.iid}&stickyId=${it.contentId}" : it.link
                    )
                }
        )
        return VO
    }
}