package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.mgj.app.vo.PriceBannerVO

@Translator(id = "priceBannerGroup")

class PriceBannerGroup implements ISixDependTranslator<ActivityDO, GroupbuyingDO,ItemBaseDO, PinTuanDO, NormalCountdownDO,PresaleDO, PriceBannerGroupVO> {

    static class PriceBannerGroupVO {
        PriceBannerVO priceBanner
    }
    @Override
    PriceBannerGroupVO translate(ActivityDO input1, GroupbuyingDO input2,ItemBaseDO itemBase, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO,PresaleDO presale) {
        PriceBanner translator = new PriceBanner()
        PriceBannerVO priceBannerVO = translator.translate(input1, input2,itemBase, pinTuanDO, normalCountdownDO,presale)
        if (priceBannerVO == null) {
            return new PriceBannerGroupVO()
        }
        else {
            return new PriceBannerGroupVO(
                    priceBanner: priceBannerVO
            )
        }
    }

}
