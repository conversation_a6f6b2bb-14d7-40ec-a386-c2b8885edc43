package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.Tools

/**
 * Created by pananping on 2020/11/2.
 */
@Translator(id = "hotSaleRankInfo", defaultValue = DefaultType.NULL)
class HotSaleRankInfo implements IOneDependTranslator<ExtraDO, Object> {

    class HotSaleRankInfoVO extends HideControl {
        String bgImage
        String link
        String prefixTitle
        String suffixTitle
        String rankNumber
        String rankId
        String numberColor
        Integer w
        Integer h
    }

    @Override
    Object translate(ExtraDO extraDO) {

        com.mogujie.detail.module.extra.domain.HotSaleRankInfo hotSaleRankInfo = extraDO?.hotSaleRankInfo

        if (!hotSaleRankInfo) {
            return new HotSaleRankInfoVO(_extra_control_hide_: true)
        }

        HotSaleRankInfoVO vo = new HotSaleRankInfoVO(_extra_control_hide_: false)

        Integer width, height
        (width, height) = Tools.getCDNImageWidthAndHeight(hotSaleRankInfo.bgImage)
        vo.bgImage = hotSaleRankInfo.bgImage
        vo.w = width
        vo.h = height

        vo.link = hotSaleRankInfo.link
        vo.numberColor = hotSaleRankInfo.numberColor ?: "#FF4466"
        vo.prefixTitle = hotSaleRankInfo.prefixTitle
        vo.rankNumber = hotSaleRankInfo.rankNumber ? hotSaleRankInfo.rankNumber.toString() : ""
        vo.suffixTitle = hotSaleRankInfo.suffixTitle
        vo.rankId = hotSaleRankInfo.rankIdNum

        return vo
    }
}