package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.live.domain.ExplainInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.Tools
import org.apache.http.util.TextUtils

import java.text.DecimalFormat

/**
 * Created by fufeng on 2017/12/7.
 */

@Translator(id = "liveInfo", defaultValue = DefaultType.EMPTY_MAP)
class LiveInfo implements ITwoDependTranslator<ShopDO, LiveSimpleDO, LiveInfoVO> {

    static class LiveInfoVO {
        LiveInfoElementData[] list

        //供应商相关信息
        String vendorTitle
        String vendorShipTitle
        String vendorUrl
        String licenseDesc
    }
    static class LiveInfoElementData {
        String userName
        String avatar
        String userDesc
        String title
        String desc
        String linkUrl
        String title1160
    }

    LiveInfoVO translate(ShopDO shopDO, LiveSimpleDO liveSimpleDO) {
        if (Tools.isLiveSource() && liveSimpleDO?.pickedExplainInfo) {
            // 直播sourceParams的主播推荐活动
            LiveInfoVO vo = new LiveInfoVO()
            vo.list = [toElement(liveSimpleDO.pickedExplainInfo)]
            vo.vendorTitle = "此商品由供应商${shopDO.name}"
            vo.vendorShipTitle = "提供"
            vo.vendorUrl = ""
            return vo
        } else if (Tools.isHaoDianYouXuan(shopDO)) {
            // 好店优选，伪装成这个样式
            LiveInfoVO vo = new LiveInfoVO()
            vo.vendorTitle = "此商品由供应商${shopDO.name}"
            vo.vendorShipTitle = "提供"
            vo.vendorUrl = ""
            return vo
        } else {
            return new LiveInfoVO()
        }
    }

    static LiveInfoElementData toElement(ExplainInfo info) {
        def userDesc
        DecimalFormat decimalFormat = new DecimalFormat("###.##")
        String heightStr = info.actHeight ? "${decimalFormat.format(info.actHeight)}cm " : ""
        String weightStr = info.actWeight ? "${decimalFormat.format(info.actWeight)}kg" : ""
        String hwStr = "${heightStr}${weightStr}"
        if (!TextUtils.isEmpty(hwStr)) {
            hwStr = hwStr + " | "
        }
        if (info.fansNum < 10000) {
            userDesc = "${hwStr}粉丝 ${info.fansNum}"
        } else {
            userDesc = String.format("${hwStr}粉丝 %.1f万", (float)info.fansNum/10000)
        }
        String linkUrl
        linkUrl = "mgj://user?uid=${info?.actUserId}&isActor=true"
        return new LiveInfoElementData(
                userName: info.actUserName,
                avatar: info.avatar,
                userDesc: userDesc,
                title: "个人主页",
                desc: "",
                linkUrl: linkUrl,
                title1160: "个人主页"
        )
    }
}
