package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

/**
 * Created by fufeng on 2017/11/16.
 */

@Translator(id = "installmentInfo", defaultValue = DefaultType.EMPTY_MAP)
class InstallmentInfo implements IOneDependTranslator<SkuDO, InstallmentInfoVO> {

    static class InstallmentInfoVO{
        InstallmentInfoItemVO[] platformCoupon //叫这个名字是历史遗留问题
    }
    static class InstallmentInfoItemVO{
        String iconTitle
        String title
        String accessoryTitle
        String linkUrl
        TextStyle iconTitleTextStyle
    }

    InstallmentInfoVO translate(SkuDO skuDO) {

        InstallmentInfoVO ret = new InstallmentInfoVO()
        //麦田 50418
        def promotionTags = MaitUtil.getMaitData(50418)
        def maitInfo =  promotionTags?.get(0)
        def iconTitle = maitInfo?.get("iconTitle")
        String freePhasesText = maitInfo?.get("freePhasesText")

        if (iconTitle && freePhasesText && skuDO?.freePhases) {
            def title = freePhasesText.replace("{phases}", skuDO.freePhases.toString())
            InstallmentInfoItemVO item = new InstallmentInfoItemVO(
                    iconTitle: iconTitle,
                    title: title,
                    accessoryTitle: maitInfo.get("accessoryTitle"),
                    linkUrl: maitInfo.get("linkUrl"),
            )
            def textStyle = new TextStyle(
                    textColor: maitInfo.get("iconTextColor") ?: "#FFFFFF",
                    backgroundColor: maitInfo.get("iconBackgroundColor") ?: "#FF5577",
                    fontSize: 10,
            )
            item.iconTitleTextStyle = textStyle
            ret.platformCoupon = [item]
        }
        return ret
    }
}