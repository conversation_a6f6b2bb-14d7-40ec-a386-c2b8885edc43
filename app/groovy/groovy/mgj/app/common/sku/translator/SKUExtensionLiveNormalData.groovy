package groovy.mgj.app.common.sku.translator


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.vo.*
import org.apache.http.util.TextUtils
import org.springframework.beans.factory.annotation.Autowired

/**
 * 应用于直播间SKU接口
 * 适用于普通商品/直播间不应用拼团价
 */
@Translator(id = "skuExtensionLiveNormalData", defaultValue = DefaultType.NULL)
class SKUExtensionLiveNormalData implements IFourDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SKUExtensionLiveDataVO> {

    @Autowired
    private Tools tools;

    @Override
    SKUExtensionLiveDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionLiveDataVO()

        /**
         * 直播间不走拼团价，默认caller == normal
         */
        SkuDO realSkuDO = aSkuDO

        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null)
        vo.data.oldPrice = vo.calculateOldPrice(aItemBaseDO)
        if (vo.data.mainPriceStr) {
            vo.data.mainDesc = "定金"
        }
        if (vo.data.subPriceStr) {
            vo.data.subDesc = "总价"
        }
        if (vo.data.title) {
            // 去掉标题里的主播名
            vo.data.title = vo.data.title.replaceFirst("^【.+?】", "")
        }
        /**
         * 拼接sku号型数据`均码（xxx/xxx）`
         */
        SKUExtensionSizeHelperInfo.splicingSizeType(realSkuDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(realSkuDO, true)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(realSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUExtensionDataProvider.getActivityType(aItemBaseDO, null, aPreSaleDO, aGroupBuyingDO)
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(aItemBaseDO)
        /**
         * 直播预售相关信息
         */
        vo.shopId = aItemBaseDO?.shopId
        vo.userId = aItemBaseDO?.userId
        vo.imLink = (aItemBaseDO?.shopId && aItemBaseDO?.iid && aItemBaseDO?.userId) ? "mgjim://talk?bid=${aItemBaseDO?.shopId}&goodsId=${aItemBaseDO?.iid}&userId=${aItemBaseDO?.userId}&shopid=${aItemBaseDO?.shopId}&login=1" : null

        if (!TextUtils.isEmpty(vo.imLink) && !vo.imLink.contains("channelId")) {
            String s = vo.imLink.contains("?") ? "&" : "?"
            vo.imLink += s + "channelId=channel_from_live"
        }

        vo.expireSeconds = tools.getCacheExpireSeconds()
        if (aPreSaleDO) {
            vo.presale = new SKUExtensionLivePresaleInfo(aPreSaleDO)
        }

        if (!(null == aGroupBuyingDO?.startTime || null == aGroupBuyingDO?.endTime)) {
            vo.tuanInfo = new SKUExtensionLiveTuanInfo(aGroupBuyingDO)
            def maitData = MaitUtil.getMaitData(123204)?.get(0)
            vo.data.priceDesc = maitData?.get("priceTag")
        }

        /**
         * 直播间商品标
         */
        vo.itemTags = vo.liveItemTags(aItemBaseDO, false)

        vo.data.pinTuanInfo = null

        /**
         * 直播分享立减价格
         */
        vo.data.otherPrices = vo.liveShareOtherPrices(aSkuDO)

        return vo
    }
}
