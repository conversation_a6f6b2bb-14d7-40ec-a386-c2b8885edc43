package groovy.mgj.app.common.sku.translator


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionLiveDataVO
import groovy.mgj.app.common.sku.vo.SKUExtensionLiveFastbuyInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionSizeHelperInfo
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.vo.SKUDataVOActivityType
import org.springframework.beans.factory.annotation.Autowired

/**
 * 应用于直播间SKU接口
 * 适用于快抢渠道SKU
 */
@Translator(id = "skuExtensionLiveFastbuyData", defaultValue = DefaultType.NULL)
class SKUExtensionLiveFastbuyData implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, SKUExtensionLiveDataVO> {

    @Autowired
    private Tools tools;


    @Override
    SKUExtensionLiveDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, FastbuyDO aFastbuyDO) {

        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionLiveDataVO()
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null)
        /**
         * 拼接sku号型数据`均码（xxx/xxx）`
         */
        SKUExtensionSizeHelperInfo.splicingSizeType(aSkuDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO, true)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.FASTBUY
        /**
         * 活动ID
         */
        vo.activityId = FastbuyIdConvertor.convertFastbuyId(aFastbuyDO?.activityId)
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [
                "channel"        : "channel_kuaiqiang",
                "orderExtensions": [
                        "activityType": "1",
                        "activityId"  : FastbuyIdConvertor.convertFastbuyId(aFastbuyDO?.activityId),
                ]
        ]
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(aItemBaseDO)

        /**
         * 直播预售相关信息
         */
        vo.shopId = aItemBaseDO?.shopId
        vo.userId = aItemBaseDO?.userId
        def fastbuyId = FastbuyIdConvertor.convertFastbuyId(aFastbuyDO?.activityId)
        vo.imLink = (aItemBaseDO?.shopId && aItemBaseDO?.iid && aItemBaseDO?.userId) ? "mgjim://talk?bid=${aItemBaseDO?.shopId}&goodsId=${aItemBaseDO?.iid}&userId=${aItemBaseDO?.userId}&shopid=${aItemBaseDO?.shopId}&login=1&activityId=${fastbuyId}&channelId=channel_kuaiqiang&fromType=mgj_rush" : null

        vo.data.oldPrice = vo.calculateOldPrice(aItemBaseDO)
        vo.expireSeconds = tools.getCacheExpireSeconds()
        if (aFastbuyDO) {
            vo.fastbuyInfo = new SKUExtensionLiveFastbuyInfo(aFastbuyDO)
            vo.data.priceDesc = "快抢价"
        }

        vo.data.pinTuanInfo = null

        return vo
    }
}
