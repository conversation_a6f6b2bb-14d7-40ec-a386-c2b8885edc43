package groovy.mgj.app.common.sku.translator

import com.mogujie.contentcenter.utils.JsonUtil
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.service.item.domain.basic.ItemTagDO
import groovy.mgj.app.common.Switcher
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDataVO
import groovy.mgj.app.fastbuy.common.FastbuyIdConvertor
import groovy.mgj.app.vo.SKUDataVOActivityType

/**
 * Created by fufeng on 2018/1/5.
 */

/**
 * 应用于商品详情页中的SKU
 * 适用于普通商品
 */
@Translator(id = "skuExtensionDetailFastBuyData", defaultValue = DefaultType.NULL)
class SKUExtensionDetailFastBuyData implements IThreeDependTranslator<SkuDO, ItemBaseDO, FastbuyDO, SKUExtensionDataVO> {
    @Override
    SKUExtensionDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, FastbuyDO aFastbuyDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionDataVO()
        /**
         * 卖家id
         */
        vo.userId = aItemBaseDO?.userId
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null, true)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(aSkuDO)
        /**
         * 开通分期免息
         */
        vo.installmentEntrance = SKUExtensionDataProvider.getBFMEntrance(aItemBaseDO)
        /**
         * 新的分期免息标题右边的提示文案
         */
        vo.installmentHint = SKUExtensionDataProvider.getInstallmentHint()
        /**
         * 保险相关麦田数据
         */
        vo.insuranceMait = SKUExtensionDataProvider.getInsuranceMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.FASTBUY
        /**
         * 店铺标，计价接口需要，走itemDo去取
         */
        DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()
        /**
         * 活动ID
         */
        vo.activityId = FastbuyIdConvertor.convertFastbuyId(aFastbuyDO?.activityId)
        vo.promotionExtraParams = ["channel": "1", "fastbuyId": vo.activityId, "shopTags": itemDO?.shopInfo?.tags, "cids": aItemBaseDO?.cids]

        // 快枪传商品标
        List<ItemTagDO> tags = DetailContextHolder.get().getItemDO().getItemTags()
        if (tags != null) {
            vo.promotionItemTags = JsonUtil.toJson(tags.findAll { "pp" == it.getTagKey() || "1897" == it.getTagValue() })
        }
        vo.promotionItemTags = vo.promotionItemTags ?: ""

        /**
         * 下单额外参数
         */
        vo.orderBillParams = [
                "channel": "channel_kuaiqiang",
                "orderExtensions": [
                    "activityType": "1",
                    "activityId" : FastbuyIdConvertor.convertFastbuyId(aFastbuyDO?.activityId),
                ]
        ]
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing

        if (isFreezing && aFastbuyDO?.limitNum > 0) {
            vo.data.freezingNum = aFastbuyDO?.limitNum > 0 ? aFastbuyDO?.limitNum : 1
        }

        vo.showPromotion = Switcher.showFastBuyPromotion()

        return vo
    }
}