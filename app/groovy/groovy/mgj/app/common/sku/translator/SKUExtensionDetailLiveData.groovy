package groovy.mgj.app.common.sku.translator

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.dataProvider.SKUJDDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDataVO
import groovy.mgj.app.live.common.LiveIdConvertor
import groovy.mgj.app.vo.SKUDataVOActivityType

/**
 * Created by wuyi on 2018/1/24.
 */
@Translator(id = "skuExtensionDetailLiveData")
class SKUExtensionDetailLiveData implements IThreeDependTranslator<ItemBaseDO, SkuDO, ChannelInfoDO, SKUExtensionDataVO> {

    @Override
    SKUExtensionDataVO translate(ItemBaseDO itemBaseDO, SkuDO skuDO, ChannelInfoDO channelInfoDO) {
        if (!itemBaseDO || !skuDO) {
            // 实在不行就显示缓存
            return null
        }
        SKUExtensionDataVO vo = new SKUExtensionDataVO()

        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(skuDO, itemBaseDO, null, true)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(skuDO)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(skuDO)
        /**
         * 开通分期免息
         */
        vo.installmentEntrance = SKUExtensionDataProvider.getBFMEntrance(itemBaseDO)
        /**
         * 新的分期免息标题右边的提示文案
         */
        vo.installmentHint = SKUExtensionDataProvider.getInstallmentHint()
        /**
         * 保险相关麦田数据
         */
        vo.insuranceMait = SKUExtensionDataProvider.getInsuranceMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.LIVE
        /**
         * 活动ID
         */
        vo.activityId = LiveIdConvertor.convertActivityId(DetailContextHolder.get().getParam("activityId"))
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [
                "channel"        : channelInfoDO?.channelMetaInfo?.orderChannelId,
                "orderExtensions": [
                        "activityType": String.valueOf(channelInfoDO?.channelMetaInfo?.outType),
                        "activityId"  : LiveIdConvertor.convertActivityId(DetailContextHolder.get().getParam("activityId")),
                ]
        ]
        /**
         * 京东商品无库存提示文案
         */
        vo.oosTips = Tools.isJdItem() ? "抱歉，所选地区内暂时无货" : ""
        /**
         * 配送地址信息
         */
        vo.addressInfo = SKUJDDataProvider.getSkuAddressInfo(skuDO)
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(itemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(itemBaseDO)

        return vo
    }
}
