package groovy.mgj.app.common.sku.translator

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.dataProvider.SKUJDDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDataVO
import groovy.mgj.app.common.sku.vo.SKUExtensionSizeHelperInfo
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.SKUDataVOCommunicationType

/**
 * 应用于商品详情页中的SKU
 * Created by fufeng on 2018/1/11.
 */

@Translator(id = "skuExtensionDetailPinTuanData", defaultValue = DefaultType.EMPTY_MAP)
class SKUExtensionDetailPinTuanData implements ISixDependTranslator<ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, PinTuanDO, ItemParamsDO, Object> {
    @Override
    Object translate(ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO, SizeHelperDO aSizeHelperDO, PinTuanDO aPinTuanDO, ItemParamsDO itemParamsDO) {
        SkuDO aSkuDO = aPinTuanDO?.skuInfo
        if (!aSkuDO || !aItemBaseDO || !Tools.isPintuan(aItemBaseDO, aPinTuanDO)) {
            //业务上可以返回NULL
            return new Object()
        }
        def vo = new SKUExtensionDataVO()
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, aPinTuanDO, true)
        /**
         * 尺码助手
         */
        vo.sizeHelperInfo = new SKUExtensionSizeHelperInfo(aItemBaseDO, aSizeHelperDO, aSkuDO, itemParamsDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(aSkuDO)
        /**
         * 开通分期免息
         */
        vo.installmentEntrance = SKUExtensionDataProvider.getBFMEntrance(aItemBaseDO)
        /**
         * 新的分期免息标题右边的提示文案
         */
        vo.installmentHint = SKUExtensionDataProvider.getInstallmentHint()
        /**
         * 保险相关麦田数据
         */
        vo.insuranceMait = SKUExtensionDataProvider.getInsuranceMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.PINTUAN
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 京东商品无库存提示文案
         */
        vo.oosTips = Tools.isJdItem() ? "抱歉，所选地区内暂时无货" : ""
        /**
         * 配送地址信息
         */
        vo.addressInfo = SKUJDDataProvider.getSkuAddressInfo(aSkuDO)
        /**
         * SKU与SKU-Select选择条交互参数
         */
        vo.skuCommunicationType = SKUDataVOCommunicationType.PINTUAN
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(aItemBaseDO)

        return vo
    }
}