package groovy.mgj.app.common.sku.translator

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDiscountPriceType
import groovy.mgj.app.common.sku.vo.SKUExtensionLiveDataVO
import groovy.mgj.app.common.sku.vo.SKUExtensionSizeHelperInfo
import groovy.mgj.app.live.common.LiveIdConvertor
import groovy.mgj.app.vo.SKUDataVOActivityType
import org.springframework.beans.factory.annotation.Autowired

/**
 * 应用于直播间SKU接口
 * 适用于直播渠道特卖渠道SKU
 */
@Translator(id = "skuExtensionLiveChannelLiveData", defaultValue = DefaultType.NULL)
class SKUExtensionLiveChannelLiveData implements IThreeDependTranslator<ItemBaseDO, SkuDO, ChannelInfoDO, SKUExtensionLiveDataVO> {

    @Autowired
    private Tools tools


    @Override
    SKUExtensionLiveDataVO translate(ItemBaseDO itemBaseDO, SkuDO skuDO, ChannelInfoDO channelInfoDO) {
        if (!itemBaseDO || !skuDO) {
            // 实在不行就显示缓存
            return null
        }
        def vo = new SKUExtensionLiveDataVO()

        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(skuDO, itemBaseDO, null)
        /**
         * 拼接sku号型数据`均码（xxx/xxx）`
         */
        SKUExtensionSizeHelperInfo.splicingSizeType(skuDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(skuDO, true)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(skuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.LIVE
        /**
         * 活动ID
         */
        vo.activityId = LiveIdConvertor.convertActivityId(DetailContextHolder.get().getParam("activityId"))
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [
                "channel"        : channelInfoDO?.channelMetaInfo?.orderChannelId,
                "orderExtensions": [
                        "activityType": String.valueOf(channelInfoDO?.channelMetaInfo?.outType),
                        "activityId"  : LiveIdConvertor.convertActivityId(DetailContextHolder.get().getParam("activityId")),
                ]
        ]
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(itemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(itemBaseDO)

        /**
         * 直播相关信息
         */
        vo.shopId = itemBaseDO?.shopId
        vo.userId = itemBaseDO?.userId
        vo.imLink = (itemBaseDO?.shopId && itemBaseDO?.iid && itemBaseDO?.userId) ? "mgjim://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&login=1" : null

        vo.data.oldPrice = vo.calculateOldPrice(itemBaseDO)
        vo.expireSeconds = tools.getCacheExpireSeconds()
        if (channelInfoDO?.currentPriceIsChannelPrice) {
            vo.data.priceDesc = "直播秒杀价"
            vo.disType = SKUExtensionDiscountPriceType.LIVECHANNEL
        } else {
            vo.data.priceDesc = "折扣价"
        }

        /**
         * 直播间商品标
         */
        vo.itemTags = vo.liveItemTags(itemBaseDO, true)

        vo.data.pinTuanInfo = null

        return vo
    }
}
