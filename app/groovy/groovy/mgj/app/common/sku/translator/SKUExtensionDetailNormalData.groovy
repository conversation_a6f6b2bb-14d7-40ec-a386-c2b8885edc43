package groovy.mgj.app.common.sku.translator

import com.mogujie.contentcenter.utils.JsonUtil
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO

/**
 * Created by fufeng on 2017/12/14.
 */

import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.service.item.domain.basic.ItemTagDO
import groovy.mgj.app.common.Switcher
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.dataProvider.SKUJDDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDataVO
import groovy.mgj.app.common.sku.vo.SKUExtensionSizeHelperInfo
import groovy.mgj.app.vo.SKUDataVOActivityType

/**
 * 应用于商品详情页中的SKU
 * 适用于普通商品
 */
@Translator(id = "skuExtensionDetailNormalData", defaultValue = DefaultType.NULL)
class SKUExtensionDetailNormalData implements ISevenDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, ChannelInfoDO, ItemParamsDO, SKUExtensionDataVO> {
    @Override
    SKUExtensionDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO, SizeHelperDO aSizeHelperDO, ChannelInfoDO channelInfoDO, ItemParamsDO itemParamsDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionDataVO()
        /**
         * 卖家id
         */
        vo.userId = aItemBaseDO?.userId
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null, true)
        /**
         * 尺码助手
         */
        vo.sizeHelperInfo = new SKUExtensionSizeHelperInfo(aItemBaseDO, aSizeHelperDO, aSkuDO, itemParamsDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(aSkuDO)
        /**
         * 开通分期免息
         */
        vo.installmentEntrance = SKUExtensionDataProvider.getBFMEntrance(aItemBaseDO)
        /**
         * 新的分期免息标题右边的提示文案
         */
        vo.installmentHint = SKUExtensionDataProvider.getInstallmentHint()
        /**
         * 保险相关麦田数据
         */
        vo.insuranceMait = SKUExtensionDataProvider.getInsuranceMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUExtensionDataProvider.getActivityType(aItemBaseDO, null, aPreSaleDO, aGroupBuyingDO)
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 京东商品无库存提示文案
         */
        vo.oosTips = Tools.isJdItem() ? "抱歉，所选地区内暂时无货" : ""
        /**
         * 配送地址信息
         */
        vo.addressInfo = SKUJDDataProvider.getSkuAddressInfo(aSkuDO)
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(aItemBaseDO)

        // 需要展示优惠信息
        vo.showPromotion = Switcher.showBuyPromotion()
        vo.jsonExtra = DetailContextHolder.get().getItemDO().getJsonExtra()
        // 只需要pp标
        List<ItemTagDO> tags = DetailContextHolder.get().getItemDO().getItemTags()
        if (tags != null) {
            vo.promotionItemTags = JsonUtil.toJson(tags.findAll { "pp" == it.getTagKey() || "1897" == it.getTagValue() })
        }
        vo.jsonExtra = vo.jsonExtra ?: ""
        vo.promotionItemTags = vo.promotionItemTags ?: ""

        DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()
        vo.promotionExtraParams = ["cids": aItemBaseDO.cids, "shopTags": itemDO?.shopInfo?.tags]

        if (BizType.CHANNEL == DetailContextHolder.get()?.getRouteInfo()?.bizType) {
            vo.activityType = SKUDataVOActivityType.COMMON_CHANNEL
            /**
             * 活动ID
             */
            vo.activityId = DetailContextHolder.get().getParam("activityId")
            /**
             * 下单额外参数
             */
            vo.orderBillParams = [
                    "channel"        : channelInfoDO?.channelMetaInfo?.orderChannelId,
                    "orderExtensions": [
                            "activityType": String.valueOf(channelInfoDO?.channelMetaInfo?.outType),
                            "activityId"  : vo.activityId,
                    ]
            ]

            // 新人价商品限购一件
            if ("xinren" == DetailContextHolder.get()?.getRouteInfo()?.channelType) {
                vo.data.limitDesc = "新人限购一件"
                vo.isFreezing = true
                vo.data.freezingNum = 1
            }

            // 渠道不展示领券购买的信息
            vo.showPromotion = false
        }

        // 主播推荐链路的商品不展示领券购买优惠
        if (groovy.mgj.app.vo.Tools.isLiveSource()) {
            vo.showPromotion = false
        }

        return vo
    }
}