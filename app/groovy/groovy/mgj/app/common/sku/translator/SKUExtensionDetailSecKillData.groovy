package groovy.mgj.app.common.sku.translator

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.sku.dataProvider.SKUExtensionDataProvider
import groovy.mgj.app.common.sku.vo.SKUExtensionDataInfo
import groovy.mgj.app.common.sku.vo.SKUExtensionDataVO
import groovy.mgj.app.common.sku.vo.SKUExtensionSizeHelperInfo
import groovy.mgj.app.vo.SKUDataVOActivityType

/**
 * Created by fufeng on 2018/1/5.
 */

/**
 * 应用于商品详情页中的SKU
 * 适用于普通商品
 */
@Translator(id = "skuExtensionDetailSecKillData", defaultValue = DefaultType.NULL)
class SKUExtensionDetailSecKillData implements ISevenDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, SeckillDO, ItemParamsDO, SKUExtensionDataVO> {
    @Override
    SKUExtensionDataVO translate(SkuDO aSkuDO, ItemBaseDO aItemBaseDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO, SizeHelperDO aSizeHelperDO, SeckillDO aSecKillDO, ItemParamsDO itemParamsDO) {
        if (!aSkuDO || !aItemBaseDO) {
            //业务上可以返回NULL
            return null
        }
        def vo = new SKUExtensionDataVO()
        /**
         * 主要信息
         */
        vo.data = new SKUExtensionDataInfo(aSkuDO, aItemBaseDO, null, true)
        /**
         * 尺码助手
         */
        vo.sizeHelperInfo = new SKUExtensionSizeHelperInfo(aItemBaseDO, aSizeHelperDO, aSkuDO, itemParamsDO)
        /**
         * 白付美分期免息
         */
        vo.installmentMait = SKUExtensionDataProvider.getInstallmentMaitData(aSkuDO)
        /**
         * 分期状态
         */
        vo.installmentStatus = SKUExtensionDataProvider.getInstallmentStatus(aSkuDO)
        /**
         * 开通分期免息
         */
        vo.installmentEntrance = SKUExtensionDataProvider.getBFMEntrance(aItemBaseDO)
        /**
         * 新的分期免息标题右边的提示文案
         */
        vo.installmentHint = SKUExtensionDataProvider.getInstallmentHint()
        /**
         * 保险相关麦田数据
         */
        vo.insuranceMait = SKUExtensionDataProvider.getInsuranceMaitData(aSkuDO)
        /**
         * 活动类型
         */
        vo.activityType = SKUDataVOActivityType.SECKILL
        /**
         * 活动ID
         */
        vo.activityId = aSecKillDO?.secKillId
        /**
         * 下单额外参数
         */
        vo.orderBillParams = [:]
        /**
         * 商品售卖状态
         */
        def stateDesc, stateLockBill, stateLockCart, isFreezing
        (stateDesc, stateLockBill, stateLockCart, isFreezing) = SKUExtensionDataProvider.getSkuActionState(aItemBaseDO, vo.activityType)
        vo.stateDesc = stateDesc
        vo.stateLockCart = stateLockCart
        vo.stateLockBill = stateLockBill
        vo.isFreezing = isFreezing
        vo.setupFreezingNum(aItemBaseDO)

        return vo
    }
}