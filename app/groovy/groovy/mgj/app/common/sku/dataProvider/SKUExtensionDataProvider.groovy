package groovy.mgj.app.common.sku.dataProvider

import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.vo.SKUExtensionBFMEntrance
import groovy.mgj.app.common.sku.vo.SKUExtensionInstallmentStatus
import groovy.mgj.app.common.sku.vo.SKUInstallmentHint
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.TextTag

/**
 * Created by fufeng on 2017/12/14.
 */
class SKUExtensionDataProvider {

    static SKUExtensionInstallmentStatus getInstallmentStatus(SkuDO skuDO) {
        SKUExtensionInstallmentStatus status = new SKUExtensionInstallmentStatus()
        String title = MaitUtil.getMaitData(136255L)?.get(0)?.get("title") ?: "白付美分期"
        skuDO?.installmentState?.with {
            status.isDisabled = it != NORMAL
            status.title = title
            status.disableToast = it == UN_PAY_OFF ? "您有先享后付订单未还清，暂不可用该功能" : ""
        }
        return status
    }

    static TextTag getInstallmentMaitData (SkuDO aSkuDO) {
        return getInstallmentMaitData(aSkuDO, false)
    }

    static TextTag getInstallmentMaitData (SkuDO aSkuDO, boolean fromLive) {
        /**
         * 白付美分期免息
         */
        def installmentMait
        def sku = aSkuDO?.skus?.find {
            element -> element?.installment != null
        }
        boolean hasInstallment = sku ? true : false
        if (hasInstallment) {
            def title
            def promotionTags = MaitUtil.getTargetedMaitData(50418)
            def maitInfo =  promotionTags?.get(0)
            String textColor = maitInfo?.get("textColor") ?: "#FFFFFF"
            String bgColor = maitInfo?.get("bgColor") ?: "FF5777"
            if (aSkuDO?.freePhases) {
                String freePhasesText = maitInfo?.get("freePhasesText")
                if (freePhasesText) {
                    title = freePhasesText.replace("{phases}", aSkuDO.freePhases.toString())
                }
            }
            else {
                String text = fromLive ? maitInfo?.get("liveText") : maitInfo?.get("text")
                title = text
            }
            installmentMait = new TextTag(text: title?:"", textColor: textColor?:"", bgColor: bgColor?:"")
        }

        return installmentMait ?: new TextTag()
    }

    static List<Map<String, Object>> getInsuranceMaitData (SkuDO aSkuDO) {
        def sku = aSkuDO?.skus?.find {
            element -> element?.insuranceMap != null
        }
        if (sku) {
            return MaitUtil.getMaitData(145273)
        }
        return []
    }

    static int getActivityType(ItemBaseDO aItemBaseDO, PinTuanDO aPintuanDO, PresaleDO aPreSaleDO, GroupbuyingDO aGroupBuyingDO) {
        def activityType = SKUDataVOActivityType.DEFAULT
        if (Tools.isPintuan(aItemBaseDO, aPintuanDO)) {
            activityType = SKUDataVOActivityType.PINTUAN
        } else if (aPreSaleDO) {
            activityType = SKUDataVOActivityType.PRESALE
        } else if (aGroupBuyingDO?.status == TuanStatus.IN) {
            activityType = SKUDataVOActivityType.TUANGOU
        }
        return activityType
    }

    static def getSkuActionState(ItemBaseDO itemBaseDO,  int activityType) {
        def stateDesc = ""
        def isFreezing = false
        def stateLockBill = false
        def stateLockCart = false
        switch (itemBaseDO?.getState()) {
            case 1:
                stateDesc = "已下架"
                stateLockBill = true
                stateLockCart = true
                break
            case 2:
                stateDesc = "卖光了"
                stateLockBill = true
                stateLockCart = true
                break
            case 3:
                stateDesc = "待开售"
                stateLockBill = true
                // 可以加购
                stateLockCart = false
                break
        }

        /**
         * 不能加购，且需要冻结SKU的情况：
         * 1.医美虚拟商品
         * 2.预售
         * 3.虚拟商品
         * 4.快抢
         * 5.秒杀
         */
        boolean isVirtualItem = (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL)
        if (    Tools.isMedicalBeautyItem() ||
                isVirtualItem ||
                activityType == SKUDataVOActivityType.PRESALE ||
                activityType == SKUDataVOActivityType.FASTBUY ||
                activityType == SKUDataVOActivityType.SECKILL
        ) {

            isFreezing = true
            stateLockCart = true
        }

        /**
         * 不冻结SKU，但不能加购的情况：
         * 1.直播特卖渠道
         */
        if (activityType == SKUDataVOActivityType.LIVE
                || activityType == SKUDataVOActivityType.COMMON_CHANNEL) {
            stateLockCart = true
        }

        return new Tuple(stateDesc,stateLockBill,stateLockCart, isFreezing)
    }

    static boolean shouldAddInstallmentTag(ItemBaseDO item) {
        def res = MaitUtil.getMaitData(29802)
        return res && item?.canApplyInstallment && ItemTag.INSTALMENT in item?.itemTags
    }

    static SKUExtensionBFMEntrance getBFMEntrance(ItemBaseDO item) {
        // 要注意如果缓存
        if(!shouldAddInstallmentTag(item)) return new SKUExtensionBFMEntrance()

        //如果符合分期购资格，加入开通分期购入口
        //读麦田配置136255
        Map<String, Object> map = MaitUtil.getMaitData(136255L)?.get(0)
        SKUExtensionBFMEntrance entrance = new SKUExtensionBFMEntrance(
                title: map?.get("entranceTitle") ?: "白付美分期",
                content: map?.get("entranceContent") ?: "",
                link: map?.get("entranceLink") ?: "",
                arrowDesc: map?.get("entranceArrowDesc") ?: ""
        )
        return entrance
    }

    static SKUInstallmentHint getInstallmentHint() {
        String hintTemplate = MaitUtil.getMaitData(136255L)?.get(0)?.get("hintTemplate") ?: ""
        return new SKUInstallmentHint(
                hintTemplate: hintTemplate
        )
    }
}
