package groovy.mgj.app.common.sku.dataProvider

import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.sku.vo.SKUAddressInfo

/**
 * Created by wuy<PERSON> on 2018/5/31.
 */
class SKUJDDataProvider {
    static SKUAddressInfo getSkuAddressInfo(SkuDO skuDO) {
        SKUAddressInfo addressInfo = new SKUAddressInfo()
        // 只要是京东商品就展示地址条
        addressInfo.isShow = Tools.isJdItem()
        addressInfo.addressTemplate = '{PROVINCE} {CITY} {AREA}'
        if (skuDO?.addressInfo) {
            addressInfo.addressId = skuDO?.addressInfo?.addressId
            addressInfo.addressDetail =
                    addressInfo.addressTemplate ?
                            addressInfo.addressTemplate.replace('{PROVINCE}', skuDO?.addressInfo?.getProvince() ? skuDO?.addressInfo?.getProvince() : "")
                                    .replace('{CITY}', skuDO?.addressInfo?.getCity() ? skuDO?.addressInfo?.getCity() : "")
                                    .replace('{AREA}', skuDO?.addressInfo?.getArea() ? skuDO?.addressInfo?.getArea() : "")
                                    .replace('{STREET}', skuDO?.addressInfo?.getStreet() ? skuDO?.addressInfo?.getStreet() : "")
                                    .replace('{ADDRESS}', skuDO?.addressInfo?.getAddress() ? skuDO?.addressInfo?.getAddress() : "")
                            : ""
        } else {
            addressInfo.addressId = 0
            addressInfo.addressDetail = "请填写收货地址"
        }
        return addressInfo
    }
}
