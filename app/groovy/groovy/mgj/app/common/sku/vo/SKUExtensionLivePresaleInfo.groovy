package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.core.adt.DetailContext
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.DetailItemDO
import com.mogujie.detail.module.presale.domain.PresaleDO


class SKUExtensionLivePresaleInfo {

    int depositCountDownTime   //时间差
    double startTime
    double endTime
    int payStartTime   //时间戳
    String comment     //注: 定金一旦支付就...

    int prePrice    // 定金
    int totalPrice  // 总价
    int expandPrice	// 膨胀金

    SKUExtensionLivePresaleInfo(PresaleDO aPresaleDO) {

        int nowTime = (int) (System.currentTimeMillis() / 1000);

        this.comment = "注: 定金一旦支付,非卖家原因不予退回。"
        this.startTime = aPresaleDO.startTime
        this.endTime = aPresaleDO.endTime
        this.payStartTime = aPresaleDO.payStartTime
        this.depositCountDownTime = aPresaleDO.endTime - nowTime

        DetailItemDO itemDO = DetailContextHolder?.get()?.getItemDO()

        this.prePrice = itemDO?.getItemPreSaleDO()?.deposit ?: 0
        this.totalPrice = itemDO?.getItemPreSaleDO()?.price ?: 0
        this.expandPrice = itemDO?.getItemPreSaleDO()?.getExpandMoney() ?: 0
    }
}
