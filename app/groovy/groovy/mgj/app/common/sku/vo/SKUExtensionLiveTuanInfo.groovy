package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO

class SKUExtensionLiveTuanInfo {

    int countdown   //时间差
    double startTime
    double endTime
    String countdownDesc    //距离团购结束还剩：


    SKUExtensionLiveTuanInfo(GroupbuyingDO aGroupBuyingDO) {

        int nowTime = (int) (System.currentTimeMillis() / 1000)
        this.startTime = aGroupBuyingDO.startTime
        this.endTime = aGroupBuyingDO.endTime

        if (this.startTime - 86400 < nowTime && nowTime < this.startTime) {
            this.countdown = this.startTime - nowTime
            this.countdownDesc = "距团购开始还剩 ："
        } else if (this.startTime <= nowTime && nowTime < this.endTime) {
            this.countdown = this.endTime - nowTime
            this.countdownDesc = "距团购结束还剩 ："
        }
    }

}
