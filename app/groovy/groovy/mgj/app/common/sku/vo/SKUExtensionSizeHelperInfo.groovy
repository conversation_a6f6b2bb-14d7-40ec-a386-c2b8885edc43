package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SizePropData
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.mgj.app.common.ItemParamsAppV2
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/12/14.
 */
class SKUExtensionSizeHelperInfo {
    String sizeTitle
    String matchedSize
    String sizeHelperEntrance

    SKUExtensionSizeHelperInfo(ItemBaseDO itemBaseDO, SizeHelperDO aSizeHelperDO, SkuDO aSkuDO, ItemParamsDO itemParamsDO) {
        def sizeTitle = ""
        def matchedSize = ""
        def sizeHelperEntrance = ""
        if (aSizeHelperDO != null && !Tools.hasSizeHelperIMG(itemBaseDO) && ItemParamsAppV2.hasSize(itemParamsDO)) {
            //有尺码助手内容,需要在sku里添加号型数据
            splicingSizeType(aSkuDO)
            //默认展示查看尺码表
            sizeHelperEntrance = "查看尺码表"
            sizeTitle = "尺码推荐: 暂无"
            // 已登录,但是未填写尺码参数,应该显示完善尺码入口
            if (DetailContextHolder.get().getLoginUserId() != null && !aSizeHelperDO.userInfoFilled) {
                sizeHelperEntrance = "完善尺码"
                sizeTitle = "尺码推荐: 暂无，请先完善尺码"
            } else if (aSizeHelperDO.matchedSizeType != null) {
                // 已登录, 有填写尺码参数
                for (int i = 0; i < aSkuDO?.skus?.size(); i++) {
                    SkuData skuData = aSkuDO.skus[i]
                    // 推荐的号型与当前商品sku匹配
                    if (skuData?.sizeType == aSizeHelperDO.matchedSizeType) {
                        matchedSize = skuData.size
                        sizeTitle = "尺码推荐"
                        break
                    }
                }
            }
        }
        this.sizeTitle = sizeTitle ?: ""
        this.matchedSize = matchedSize ?: ""
        this.sizeHelperEntrance = sizeHelperEntrance ?: ""
    }

    public static void splicingSizeType(SkuDO aSkuDO) {
        Map<String, String> sizeTypeMap = new HashMap<>()
        for (SkuData skuData : aSkuDO?.skus) {
            if (skuData.sizeType != null) {
                sizeTypeMap.put(skuData.size, skuData.sizeType)
                skuData.size = "${skuData.size} (${skuData.sizeType})"
            }
        }
        String sizeKey = aSkuDO?.sizeKey
        for (PropInfo prop : aSkuDO?.getProps()) {
            if (prop.label.equals(sizeKey) && prop.list != null && prop.list.size() > 0) {
                for (SizePropData sizePropData : prop.list) {
                    String sizeType = sizeTypeMap.get(sizePropData.name)
                    if (sizeType != null) {
                        sizePropData.name = "${sizePropData.name} (${sizeType})"
                    }
                }
                break
            }
        }
    }
}
