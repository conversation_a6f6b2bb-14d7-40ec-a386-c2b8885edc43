package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.ContextUtil
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.mgj.app.vo.SKUDataVOPintuanInfo
import com.mogujie.detail.spi.dslutils.Tools
import org.apache.commons.collections4.CollectionUtils

/**
 * Created by fufeng on 2017/12/13.
 */
class SKUExtensionDataInfo {
    /**
     * 商品标题
     */
    String title
    /**
     * 白付美剩余额度，包含可用固定额度和可用分期额度
     */
    Long availableQuota
    /**
     * IM唤起SKU请求接口返回的默认图片
     * 数据来源是itemBase.topImages.first()
     */
    String img
    /**
     * SKU列表
     */
    List<SkuData> skus
    /**
     * SKU属性表
     */
    List<PropInfo> props
    /**
     * SKU对应的促销价格
     */
    List<SkuOtherPrice> otherPrices
    /**
     * 价格区间
     */
    String priceRange
    String defaultPrice
    String oldPrice
    String highNowPrice
    String lowNowPrice
    /**
     * 总剩余库存(所有sku当前库存之和)
     */
    int totalStock
    /**
     * 预售定金
     */
    String mainPriceStr
    /**
     * 预售总价
     */
    String subPriceStr
    String mainDesc     //定金:
    String subDesc     //总价:
    String priceDesc    //快抢价，直播秒杀价，折扣价
    /**
     * 限购总库存
     */
    Integer limitTotalStock
    /**
     * 限购数量
     */
    Integer limitNum
    /**
     * 冻结 SKU 时限购数量，1460
     */
    Integer freezingNum
    /**
     * 限购描述
     */
    String limitDesc
    /**
     * 拼团信息
     */
    SKUDataVOPintuanInfo pinTuanInfo
    /**
     * 商品ID
     */
    String iid
    /**
     * 延迟发货提示模板，"* 付款后最晚于{date}前发货"，客户端替换{date}
     */
    String delayShipTipsTemplate
    /**
     * 延迟发货提示模板，"{date}前发货"，客户端替换{date}
     */
    String delayShipButtonTemplate
    /**
     * 延迟发货展示样式
     */
    Integer delayShipStyle

    SKUExtensionDataInfo(SkuDO sku, ItemBaseDO aItemBaseDO, PinTuanDO aPinTuanDO) {
        this(sku, aItemBaseDO, aPinTuanDO, false)
    }

    SKUExtensionDataInfo(SkuDO sku, ItemBaseDO aItemBaseDO, PinTuanDO aPinTuanDO, boolean hideDelayShip) {
        // 白付美剩余额度
        this.availableQuota = sku.availableQuota
        def realSkuInfo = sku
        if (Tools.isPintuan(aItemBaseDO, aPinTuanDO)) {
            this.pinTuanInfo = new SKUDataVOPintuanInfo(
                    tuanType: "${aPinTuanDO?.tuanType}",
                    icon: ImageUtil.img("/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png")
            )

            //
            realSkuInfo = aPinTuanDO.skuInfo
        } else {
            this.pinTuanInfo = new SKUDataVOPintuanInfo()
        }

        this.title = realSkuInfo?.title
        this.skus = realSkuInfo?.skus
        this.props = realSkuInfo?.props
        this.priceRange = realSkuInfo?.priceRange
        // 直播间不处理区间价的.00小数
        boolean isFromLive = ContextUtil.isLiveSkuDetail(DetailContextHolder.get())
        if (!isFromLive) {
            this.defaultPrice = groovy.mgj.app.vo.Tools.shrinkRangePrice(realSkuInfo?.defaultPrice)
        } else {
            this.defaultPrice = realSkuInfo?.defaultPrice
        }
        this.totalStock = realSkuInfo?.totalStock
        this.mainPriceStr = realSkuInfo?.mainPriceStr
        this.subPriceStr = realSkuInfo?.subPriceStr
        if (aItemBaseDO?.highNowPrice) {
            this.highNowPrice = "¥" + aItemBaseDO?.highNowPrice
        }
        if (aItemBaseDO?.lowNowPrice) {
            this.lowNowPrice = "¥" + aItemBaseDO?.lowNowPrice
        }

        this.limitTotalStock = realSkuInfo?.limitTotalStock
        this.limitNum = realSkuInfo?.limitNum
        this.limitDesc = realSkuInfo?.limitDesc==null?"":realSkuInfo?.limitDesc
        this.iid = aItemBaseDO?.iid
        boolean needDelayShipTipsTemplate = false
        boolean needChangeDelayTemplate = false
        boolean isJDGood = Tools.isJdItem()
        for (SkuData aSku in realSkuInfo?.skus) {
            if (aSku?.getDelayTime() > 0) {
                needDelayShipTipsTemplate = true
            }
            if (isJDGood && realSkuInfo?.jdSkuStock?.get(aSku.stockId)?.intValue() == 0) {
                needDelayShipTipsTemplate = true
                needChangeDelayTemplate = true
                aSku.delayTime = 1
            }
        }
        if (needDelayShipTipsTemplate) {
            Map<String, Object> maitData = MaitUtil.getMaitData(101942)?.get(0)
            def tipsTemplate = maitData?.get("skuDelayDeliveryTime")
            def tipsTemplateOnButton = maitData?.get("skuDelayDeliveryButtonTime")
            def style = maitData?.get("skuDelayDeliveryStyle")
            this.delayShipTipsTemplate = tipsTemplate
            this.delayShipButtonTemplate = tipsTemplateOnButton
            if (style) {
                try {
                    this.delayShipStyle = Integer.valueOf(style)
                } catch (NumberFormatException e) {
                }
            }


            if (needChangeDelayTemplate) {
                this.delayShipTipsTemplate = "* 您所在地区可能无货"
                this.delayShipStyle = 1
            } else {
                if (hideDelayShip) {
                    // sku上不展示延迟发货信息
                    this.delayShipButtonTemplate = ""
                    this.delayShipTipsTemplate = ""
                }
            }
        }

        //
        String defaultImageUrl = ""
        if (CollectionUtils.isNotEmpty(aItemBaseDO?.topImages)){
            defaultImageUrl = aItemBaseDO.topImages?.first()
        }
        this.img = defaultImageUrl ?: ""

    }

}
