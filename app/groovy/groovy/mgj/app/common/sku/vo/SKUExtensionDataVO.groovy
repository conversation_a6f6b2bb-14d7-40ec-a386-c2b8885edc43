package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.mgj.app.vo.TextTag

import java.util.function.Consumer

/**
 * Created by fufeng on 2017/12/13.
 */
class SKUExtensionDataVO {
    SKUExtensionDataInfo data
    /**
     * 活动类型：预售、团购、快抢等
     * SKUDataVOActivityType
     * public static final int DEFAULT = 0//默认为普通商品
     * public static final int PRESALE = 1//预售
     * public static final int TUANGOU = 2//团购
     * public static final int FASTBUY = 3//快抢
     * public static final int SECKILL = 4//秒杀
     * public static final int PINTUAN = 5//拼团
     */
    int activityType

    /**
     * 店铺ID（id2url后）
     */
    String shopId

    /**
     * 店主ID（id2url后）
     */
    String userId

    /**
     * 联系商家的IM链接
     */
    String imLink

    String activityId

    //尺码助手
    SKUExtensionSizeHelperInfo sizeHelperInfo

    /**
     * 分期免息Tag信息
     */
    TextTag installmentMait
    /**
     * 开通白富美
     */
    SKUExtensionBFMEntrance installmentEntrance
    /**
     *  白付美标题旁边的文案，在新样式中替代 installmentMait
     */
    SKUInstallmentHint installmentHint
    /**
     * 分期状态的一些信息
     */
    SKUExtensionInstallmentStatus installmentStatus
    /**
     * 额外下单参数
     */
    Map orderBillParams

    int skuCommunicationType
    /**
     * 商品是否能加购
     */
    boolean stateLockCart
    /**
     * 商品是否能下单
     */
    boolean stateLockBill
    /**
     * 状态描述
     */
    String stateDesc
    /**
     * 是否冻结sku，虚拟、快抢、秒杀商品需要冻结（1460 前版本使用）
     */
    boolean isFreezing
    /**
     * 无库存提示文案
     * 目前只有京东开普勒商品在用
     */
    String oosTips
    /**
     * 配送地址信息
     */
    SKUAddressInfo addressInfo
    /**
     * 预售相关数据
     */
    SKUExtensionLivePresaleInfo presale

    /**
     * 是否在sku中展示优惠信息
     * 目前普通详情页中会展示
     */
    Boolean showPromotion = false

    /**
     * 展示优惠时需要透传的extra，减少对应后端查商品表
     */
    String jsonExtra

    /**
     * 展示优惠时需要透传的商品标，减少对应后端查商品表
     */
    String promotionItemTags

    /**
     * 展示优惠时需要透传的除了上面jsonExtra和promotionItemTags之外其他需要透传的参数
     */
    Map<String, String> promotionExtraParams

    /**
     * 保险相关麦田数据
     */
    List<Map<String, Object>> insuranceMait

    def setupFreezingNum(ItemBaseDO itemBaseDO) {
        // 空保护
        if (data == null) {
            return
        }
        // 特殊类目需要冻结单次购买数量
        if (isFreezing) {
            data.freezingNum = 1
            // 当商品限购的情况下，如果有sku级的限购，那么应该取两者的最小值，避免sku限购>商品限购
            // 如古茗的优惠券，平台类目限制了单次购买1件，但是商家设置了sku限购5件，如果不做此处理，
            // 会导致单次可以购买5件
            // 这里直接置为0，是因为freezingNum肯定为1<=purchaseSkuLimit，
            // 那么直接让purchaseSkuLimit=0即可其走商品的限购逻辑
            data.getSkus()?.forEach(new Consumer<SkuData>() {
                @Override
                void accept(SkuData skuData) {
                    skuData.purchaseSkuLimit = 0
                }
            })
            return
        }
        // 商品限购数量
        if (itemBaseDO == null
                || itemBaseDO.purchaseLimit == null) {
            data.freezingNum = 0    // 不限购
        } else {
            data.freezingNum = itemBaseDO.purchaseLimit
        }
    }
}
