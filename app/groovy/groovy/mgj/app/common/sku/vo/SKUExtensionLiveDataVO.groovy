package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.OtherPrice
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.TagUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.metabase.client.MetaClient
import com.mogujie.metabase.client.MetabaseConfigure
import org.apache.http.util.TextUtils


class SKUExtensionLiveDataVO extends SKUExtensionDataVO {

    SKUExtensionLiveTuanInfo tuanInfo
    SKUExtensionLiveFastbuyInfo fastbuyInfo

    List<SKUExtensionLiveItemTag> itemTags

    int disType   //价格折扣类型（直播间没有拼团）

    int expireSeconds   //SKU信息过期时间间隔时长

    def calculateOldPrice(ItemBaseDO aItemBaseDO) {

        // 具体 SKU 的划线价展示控制
        mayHideOldPriceInSku(aItemBaseDO)

        String oldPrice = null

        if (!aItemBaseDO || !aItemBaseDO.canShowStrikethroughPrice) {
            return oldPrice
        }

        //lowPrice
        if (aItemBaseDO.lowPrice == aItemBaseDO.lowNowPrice && aItemBaseDO.highPrice == aItemBaseDO.highNowPrice) {
            oldPrice = null
        } else {
            if (aItemBaseDO.lowPrice) {
                oldPrice = "¥" + aItemBaseDO.lowPrice
                //拼接区间价
                if (aItemBaseDO.highPrice && aItemBaseDO.highPrice != aItemBaseDO.lowPrice) {
                    oldPrice = oldPrice + "~¥" + aItemBaseDO.highPrice
                }
            }
        }

        return oldPrice
    }

    private def mayHideOldPriceInSku(ItemBaseDO itemBaseDO) {
        // 空保护
        if (data == null
                || data.skus == null
                || data.skus.isEmpty()) {
            return
        }
        // 可以展示划线价
        if (itemBaseDO != null && itemBaseDO.canShowStrikethroughPrice) {
            return
        }
        // 不展示划线价
        // 直播间 SKU 面板选中某个 SKU 时根据 price、nowprice 是否相等决定是否展示划线价
        data.skus.forEach({ sku ->
            if (sku.nowprice != null && sku.price != null
                    && sku.price != sku.nowprice) {
                sku.price = sku.nowprice
            }
        })
    }

    /**
     * 直播间商品标
     */
    static def liveItemTags(ItemBaseDO itemBaseDO, boolean isChannel) {
        def list = []
        def nameList = []

        //1590 https://mogu.feishu.cn/docs/doccnFAaZ4tCHOsLoWp6sQzq2Kb#  大促主力盘商品标签
        Map<String, Object> maitData156207 = MaitUtil.getMaitData(156207)?.get(1)
        if (maitData156207 != null) {
            def itemMark = maitData156207.get("itemmark")
            def containsTag = TagUtil.isContainsTag(DetailContextHolder.get()?.getItemDO()?.getItemTags(), Integer.valueOf(itemMark + ""))
            if (containsTag) {
                list << new SKUExtensionLiveItemTag(
                        "icon": maitData156207.get("tagIconImg")
                )
            }
        }

        int appVersion
        try {
            appVersion = DetailContextHolder.get()?.getParam("_av")?.toInteger() ?: 0
        } catch (Exception ignore) {
            appVersion = 0
        }

        if (Tools.isLiveNewcomerPrice() && itemBaseDO?.saleType != 1 && !isChannel) {
            // 直播新人标
            String icon
            // 因为老版本的ios限定了标的高宽比，所以需要区分版本返回
            if (appVersion >= 1170) {
                icon = ImageUtil.img(MetabaseUtil.get("live_sku_newcomer_icon"))
            } else {
                icon = ImageUtil.img("/mlcdn/c45406/190218_7ci2dl5ic3k6e26bclhlci4lali1e_75x48.png")
            }
            list << new SKUExtensionLiveItemTag(
                    "icon": icon
            )
        }

        boolean filteByTag = TagUtil.isContainsTag(DetailContextHolder.get()?.getItemDO()?.getItemTags(), 1897)
        boolean filterByMetabase = filterByMetabaseShopIds()
        // 除了渠道、跨境、预售，以及特殊标的商品，其它商品要不要透出，由麦田配没配决定
        if (!filterByMetabase && !filteByTag && !itemBaseDO?.overseaItemInfo?.overseaType && itemBaseDO?.saleType != 1 && !isChannel) {
            // 满减标
            Map<String, Object> maitData = MaitUtil.getMaitData(134291)?.get(0)
            String icon
            if (appVersion >= 1170) {
                icon = maitData?.get("liveSkuIcon")
            } else {
                icon = maitData?.get("liveSkuIconOld")
            }
            if (!TextUtils.isEmpty(icon)) {
                list << new SKUExtensionLiveItemTag(
                        "icon": icon
                )
            }
        }

        // 新品标，注意这个标要放在最后
        if (System.currentTimeSeconds() - itemBaseDO.firstOnlineTime < 7 * 86400 && !isChannel) {
            list << new SKUExtensionLiveItemTag(
                    "icon": ImageUtil.img("/mlcdn/c45406/210914_02f8gbjh3ibl16bi74ah32e966a0i_78x48.png")
            )
        }

        return list
    }


    /**
     * 直播分享立减价
     */
    def liveShareOtherPrices(SkuDO aSkuDO) {

        def priceList = []

        if (aSkuDO?.otherPrices) {
            for (OtherPrice it : aSkuDO?.otherPrices) {

                //直播分享立减，枚举值为1
                if (it.priceType == 1) {

                    def priceVO = new SkuOtherPrice()
                    priceVO.priceType = it.priceType
                    priceVO.maxPrice = it.maxPrice
                    priceVO.minPrice = it.minPrice
                    priceVO.skuRealPriceMap = it.skuRealPriceMap

                    //拼接区间价
                    String lowPrice = formatPrice(it.minPrice)
                    if (it.maxPrice == it.minPrice) {
                        priceVO.defaultPriceDesc = lowPrice
                    } else {
                        priceVO.defaultPriceDesc = lowPrice + "~" + formatPrice(it.maxPrice)
                    }
                    priceVO.lowPriceDesc = lowPrice


                    priceList << priceVO
                    break
                }
            }
        }

        return priceList
    }

    private String formatPrice(long pennyPrice) {
        return String.format("¥%.2f", ((double) pennyPrice) / 100)
    }

    private static boolean filterByMetabaseShopIds() {
        try {
            MetabaseConfigure metabaseConfigure = new MetabaseConfigure()
            metabaseConfigure.setConfigGroup("mogulive")
            String shopIds = MetaClient.client2(metabaseConfigure).getConfiguration("gouwujin_tag_block")
            return shopIds.split(",").toList().contains("" + DetailContextHolder.get().getItemDO().getShopId())
        } catch (Exception ignore) {
            return false
        }
    }
}
