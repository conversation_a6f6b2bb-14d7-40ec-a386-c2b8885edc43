package groovy.mgj.app.common.sku.vo

import com.mogujie.detail.module.fastbuy.domain.FastbuyDO

class SKUExtensionLiveFastbuyInfo {

    int state    //快抢状态
    int countdown     //时间差
    double startTime
    double endTime
    String countdownDesc     //距离团购结束还剩 :

    SKUExtensionLiveFastbuyInfo(FastbuyDO aFastbuyDO) {

        int nowTime = (int) (System.currentTimeMillis() / 1000);
        this.state = aFastbuyDO.state
        this.startTime = aFastbuyDO.startTime
        this.endTime = aFastbuyDO.endTime
        this.countdown = aFastbuyDO.endTime - nowTime
        this.countdownDesc = "距快抢结束还剩 ："
    }

}
