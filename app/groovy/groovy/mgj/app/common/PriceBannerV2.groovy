package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITenDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.dailyconfig.domain.NewActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.ActivityKey
import groovy.mgj.app.common.common.NormalCountdownManager
import groovy.mgj.app.vo.*
import org.apache.http.util.TextUtils

import java.text.SimpleDateFormat

/**
 * Created by wuyi on 2018/9/6.
 */
@Translator(id = "priceBannerV2")
class PriceBannerV2 implements ITenDependTranslator<NewActivityDO, ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, PresaleDO, ExtraDO, SkuDO, LiveSimpleDO, PriceBannerVOV2> {
    // 0.normal 1.活动正式期 2.预热 3.比价
    // 活动包括大促 团购 拼团 限时爆款等
    final int SHOW_TYPE_NORMAL = 0
    final int SHOW_TYPE_IN_ACTIVITY = 1
    final int SHOW_TYPE_PRE = 2
    final int SHOW_TYPE_BIJIA = 3

    // 倒计时从具体日期进入倒计时样式的阈值
    final int COUNTDOWNTHRESHOLD = 72

    @Override
    PriceBannerVOV2 translate(NewActivityDO newActivityDO, ActivityDO activityDO, GroupbuyingDO groupbuyingDO, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presaleDO, ExtraDO extraDO, SkuDO skuDO, LiveSimpleDO liveSimpleDO) {
        PriceBannerVOV2 result = new PriceBannerVOV2()
        def defaultBgColor = "#FFE8EE"
        def defaultTextColor = "#FF5777"

        boolean need2107EventTag = true

        result.highNowPrice = itemBaseDO?.highNowPrice
        result.lowNowPrice = itemBaseDO?.lowNowPrice
        result.highPrice = itemBaseDO?.highPrice
        result.lowPrice = itemBaseDO?.lowPrice

        // 1480 版本起，不展示区间价，改为 xx起 的格式
        if (groovy.mgj.app.vo.Tools.getAppVersion() >= 1480) {
            result.updatePrices(true)
        } else {
            result.updatePrices(false)
        }
        
        result.eventTags = new ArrayList<PriceTagVO>()

        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XSBK)
        CountdownInfo xinpin = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XINP)
        CountdownInfo shango = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.SHANGO)

        // 通过配置新增的kv标氛围
        CountdownInfo extraPreActivity, extraInActivity
        (extraPreActivity, extraInActivity) = NormalCountdownManager.getActivity(normalCountdownDO)


        // 正式大于预热
        // 同样条件下 预售>待开售>主播推荐>大促正式>限时爆款正式>团购正式>非渠道招商拼团正式>大促预热>团购预热>普通
        // 预售
        if (presaleDO) {
            result.type = SHOW_TYPE_NORMAL

            need2107EventTag = false

            result.with {
                price = presaleDO.totalPrice
                oldPrice = itemBaseDO.lowPrice
                priceTag = "预售价"
                eventPrice = presaleDO.deposit
                if (presaleDO.presaleDesc) {
                    if (!TextUtils.isEmpty(presaleDO?.expandMoney)) {
                        eventTags.add(0, new PriceTagVO(
                                text: "抵" + presaleDO?.expandMoney,
                                bgColor: defaultBgColor,
                                textColor: defaultTextColor
                        ))
                    }
                    eventTags.add(0, new PriceTagVO(
                            text: "定金",
                            bgColor: defaultBgColor,
                            textColor: defaultTextColor
                    ))
                }
            }
        }
        // 待开售
        else if (itemBaseDO?.state == 3 && extraDO?.onSaleTime) {
            result.type = SHOW_TYPE_NORMAL

            need2107EventTag = false

            Date date = new Date(extraDO?.onSaleTime * 1000)
            SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日HH:mm")
            String dateString = formatter.format(date)
            result.eventTags.add(new PriceTagVO(
                    text: dateString + "开售，请提前设置提醒",
                    bgColor: defaultBgColor,
                    textColor: defaultTextColor
            ))
        }
        // 主播推荐
        else if (groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend()) {
            result.countdown = new CountdownVOV2()

            def maitData = MaitUtil.getMaitData(144975L)?.get(0)
            result.priceTag = maitData?.get("priceDesc") ?: "主播特价"
            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")
            result.discountPriceColor = maitData?.get("discountPriceColor")
            result.discountPriceBgColor = maitData?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        //  当前是大促(包括品牌日等)正式期，并且本商品是活动商品
        //  2023.01.04号：正式期图片上也不需要价格，价格展示在日常展示的位置。倒计时也没有！
        else if (activityDO && activityDO?.activityState == 2) {
            //新的不展示倒计时
            if (!TextUtils.isEmpty(newActivityDO?.img)) {
                //不走下面的逻辑
            }
            else {//2023.01.04号:老逻辑不动

            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = activityDO?.countdown
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = activityDO?.endTimeHintColor
            countDown.type = 1
            // 计算出当前剩余时间
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = activityDO.countdown / (60 * 60 * 24)
            long hours = (activityDO.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.coverBg = activityDO?.activityInImage1110 ? activityDO?.activityInImage1110 : ""
            
            }//2023.01.04号

            result.priceTag = activityDO?.priceDesc
            result.priceColor = activityDO?.endTimeHintColor
            result.discountPriceColor = activityDO?.discountPriceColor
            result.discountPriceBgColor = activityDO?.discountPriceBgColor

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 限时爆款。限时爆款没有预热，就是正式期
        else if (xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY) {
            def xsbkMait = MaitUtil.getMaitData(xsbk?.maitId1110)?.get(0)
            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = xsbk.countdown
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = xsbkMait?.get("countdownColor")
            countDown.type = 1
            // 计算出当前剩余时间
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = xsbk.countdown / (60 * 60 * 24)
            long hours = (xsbk.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.priceTag = xsbkMait?.get("priceDesc") ?: "限时爆款价"
            result.coverBg = xsbkMait?.get("coverBg")
            result.priceColor = xsbkMait?.get("priceColor")
            result.discountPriceColor = xsbkMait?.get("discountPriceColor")
            result.discountPriceBgColor = xsbkMait?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 团购正式期
        else if (groupbuyingDO && groupbuyingDO?.status == TuanStatus.IN && groupbuyingDO?.endTime) {
            def maitData = MaitUtil.getMaitData(123204)?.get(0)
            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = groupbuyingDO?.endTime - System.currentTimeSeconds()
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = maitData?.get("countdownColor")
            countDown.type = 1
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = countDown.countdown / (60 * 60 * 24)
            long hours = (countDown.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.priceTag = maitData?.get("priceTag")
            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")
            result.discountPriceColor = maitData?.get("discountPriceColor")
            result.discountPriceBgColor = maitData?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 招商非渠道拼团。没有预热期，就是正式期
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            def maitData = MaitUtil.getMaitData(123206)?.get(0)
            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = pinTuanDO.remainTime
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = maitData?.get("countdownColor")
            countDown.type = 1
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = countDown.countdown / (60 * 60 * 24)
            long hours = (countDown.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")
            result.discountPriceColor = maitData?.get("discountPriceColor")
            result.discountPriceBgColor = maitData?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 新品正式期
        else if (xinpin && xinpin.startTime <= System.currentTimeSeconds() && xinpin.endTime > System.currentTimeSeconds() && xinpin.state == CountdownState.IN_ACTIVITY) {
            def xinpinMait = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = xinpin.countdown
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = xinpinMait?.get("countdownColor")
            countDown.type = 1
            // 计算出当前剩余时间
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = xinpin.countdown / (60 * 60 * 24)
            long hours = (xinpin.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.priceTag = xinpinMait?.get("priceDesc") ?: "新品价"
            result.coverBg = xinpinMait?.get("coverBg")
            result.priceColor = xinpinMait?.get("priceColor")
            result.discountPriceColor = xinpinMait?.get("discountPriceColor")
            result.discountPriceBgColor = xinpinMait?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 闪购正式期
        else if (shango && shango.startTime <= System.currentTimeSeconds() && shango.endTime > System.currentTimeSeconds() && shango.state == CountdownState.IN_ACTIVITY) {

            Long maitID = shango?.maitId1110
            if (shango.nbt == "117" && groovy.mgj.app.vo.Tools.appVersion < 1471) {
                maitID = 152342
            }

            if (shango.nbt == "117" && groovy.mgj.app.vo.Tools.appVersion >= 1471) {
                fillBiJia(result, shango, itemBaseDO, liveSimpleDO)
            } else {
                def maitData = MaitUtil.getMaitData(maitID)?.get(0)
                // 麦田配置可控制是否需要倒计时
                String needCountDown = maitData?.get("needCountDown") != null ? String.valueOf(maitData.get("needCountDown")) : "1"
                CountdownVOV2 countDown = new CountdownVOV2()
                if ("0" != needCountDown) {
                    countDown.countdown = shango.countdown
                    countDown.countdownTitle = "距结束"
                    countDown.countdownTitleColor = maitData?.get("countdownColor")
                    countDown.type = 1
                    // 计算出当前剩余时间
                    countDown.threshold = COUNTDOWNTHRESHOLD
                    long days = shango.countdown / (60 * 60 * 24)
                    long hours = (shango.countdown % (60 * 60 * 24)) / (60 * 60)
                    countDown.countdownText = String.format("%d天%d时", days, hours)
                }
                result.countdown = countDown
                result.priceTag = maitData?.get("priceDesc") ?: ("闪购价")
                result.coverBg = maitData?.get("coverBg")
                result.priceColor = maitData?.get("priceColor")
                result.discountPriceColor = maitData?.get("discountPriceColor")
                result.discountPriceBgColor = maitData?.get("discountPriceBgColor")

                result.type = SHOW_TYPE_IN_ACTIVITY
                result.activityType = ActivityType.SHANGO_IN
            }
        }
        // 其他kv标配置的氛围正式期
        else if (extraInActivity) {
            def maitData = MaitUtil.getMaitData(extraInActivity?.maitId1110)?.get(0)
            CountdownVOV2 countDown = new CountdownVOV2()
            countDown.countdown = extraInActivity.countdown
            countDown.countdownTitle = "距结束"
            countDown.countdownTitleColor = maitData?.get("countdownColor")
            countDown.type = 1
            // 计算出当前剩余时间
            countDown.threshold = COUNTDOWNTHRESHOLD
            long days = extraInActivity.countdown / (60 * 60 * 24)
            long hours = (extraInActivity.countdown % (60 * 60 * 24)) / (60 * 60)
            countDown.countdownText = String.format("%d天%d时", days, hours)
            result.countdown = countDown

            result.priceTag = maitData?.get("priceDesc")
            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")
            result.discountPriceColor = maitData?.get("discountPriceColor")
            result.discountPriceBgColor = maitData?.get("discountPriceBgColor")

            result.type = SHOW_TYPE_IN_ACTIVITY
        }
        // 不是正式期，是预热或者普通
        else {
            long lastPreActivityStartTime = Long.MAX_VALUE
            boolean isPre = false

            result.discountPriceColor = "#FFFFFF"
            result.discountPriceBgColor = "#FF4466"
            //1682修改
            // 当前是大促(包括品牌日等)预热期并且本商品是活动商品，并且比其他预热的开始时间要早
            if (activityDO && activityDO?.warmUpPrice?.price && activityDO?.activityState == 1
                    && activityDO.startTime < lastPreActivityStartTime) {

                //2023.01.04号：预热的时候不需要展示活动价了，倒计时也不展示
                if (!TextUtils.isEmpty(newActivityDO?.img)) {
                    //不走下面的逻辑
                }
                else {//2023.01.04号
                
                CountdownVOV2 countDown = new CountdownVOV2()
                countDown.countdown = activityDO.countdown
                countDown.countdownTitle = "距开始"
                countDown.countdownTitleColor = activityDO?.endTimeHintColor ? activityDO?.endTimeHintColor : "#FFFFFF"
                countDown.type = 2
                countDown.threshold = COUNTDOWNTHRESHOLD
                long startTime = activityDO.startTime
                Date date = new Date(startTime * 1000)
                String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
                countDown.countdownText = dateStr + "开始"
                result.countdown = countDown

                WarmUpInfo warmupInfo = new WarmUpInfo()
                warmupInfo.with {
                    price = activityDO?.warmUpPrice?.price
                    if (price) {
                        priceTag = activityDO.warmUpPrice?.priceDesc ? activityDO.warmUpPrice?.priceDesc + ": " : null
                        bgImage = activityDO.preActivityInImage1110
                        if (!TextUtils.isEmpty(bgImage)) {
                            priceColor = activityDO?.endTimeHintColor ? activityDO?.endTimeHintColor : "#FFFFFF"
                        } else {
                            priceColor = "#000000"
                        }
                    }
                }
                result.warmUpInfo = warmupInfo
                result.priceColor = "#000000"
                
                }//2023.01.04号

                result.type = SHOW_TYPE_PRE
                lastPreActivityStartTime = activityDO.startTime
                isPre = true
            }
            // 团购预热期，并且比其他预热的开始时间要早
            if (groupbuyingDO?.status == TuanStatus.PRE && groupbuyingDO?.startTime
                    && groupbuyingDO?.startTime < lastPreActivityStartTime) {
                def maitData = MaitUtil.getMaitData(123204)?.get(0)
                CountdownVOV2 countDown = new CountdownVOV2()
                countDown.countdown = groupbuyingDO?.startTime - System.currentTimeSeconds()
                countDown.countdownTitle = "距开始"
                countDown.countdownTitleColor = maitData?.get("countdownColor")
                countDown.type = 2
                countDown.threshold = COUNTDOWNTHRESHOLD
                long startTime = groupbuyingDO?.startTime
                Date date = new Date(startTime * 1000)
                String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
                countDown.countdownText = dateStr + "开始"
                result.countdown = countDown

                WarmUpInfo warmupInfo = new WarmUpInfo()
                warmupInfo.price = groupbuyingDO?.price
                warmupInfo.priceTag = maitData?.get("priceTag")
                if (warmupInfo.priceTag) {
                    warmupInfo.priceTag = "${warmupInfo.priceTag}: "
                }
                warmupInfo.bgImage = maitData?.get("preCoverBg")
                warmupInfo.priceColor = maitData?.get("priceColor")
                result.warmUpInfo = warmupInfo

                result.priceColor = "#000000"

                result.type = SHOW_TYPE_PRE
                lastPreActivityStartTime = groupbuyingDO.startTime
                isPre = true
            }
            // 新品预热期，并且比其他预热的开始时间要早
            if (xinpin && xinpin.startTime > System.currentTimeSeconds() && xinpin.state == CountdownState.WARM_UP
                    && xinpin.startTime < lastPreActivityStartTime) {
                def maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
                CountdownVOV2 countDown = new CountdownVOV2()
                countDown.countdown = xinpin?.startTime - System.currentTimeSeconds()
                countDown.countdownTitle = "距开始"
                countDown.countdownTitleColor = maitData?.get("countdownColor")
                countDown.type = 2
                countDown.threshold = COUNTDOWNTHRESHOLD
                long startTime = xinpin?.startTime
                Date date = new Date(startTime * 1000)
                String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
                countDown.countdownText = dateStr + "开始"
                result.countdown = countDown

                WarmUpInfo warmupInfo = new WarmUpInfo()
                warmupInfo.with {
                    price = xinpin?.price ? "¥" + NumUtil.formatNum(xinpin.price / 100D) : null
                    if (price) {
                        priceTag = maitData?.priceDesc ? maitData?.priceDesc + ": " : "新品价: "
                        bgImage = maitData?.get("preCoverBg")
                        priceColor = maitData?.get("priceColor") ? maitData?.get("priceColor") : "#FFFFFF"
                    }
                }
                result.warmUpInfo = warmupInfo

                result.priceColor = "#000000"

                result.type = SHOW_TYPE_PRE
                lastPreActivityStartTime = xinpin.startTime
                isPre = true
            }
            // 闪购（比价除外）预热期，并且比其他预热的开始时间要早
            if (shango && shango.nbt != "117" && shango.startTime > System.currentTimeSeconds() && shango.state == CountdownState.WARM_UP
                    && shango.startTime < lastPreActivityStartTime) {
                Long maitID = shango?.maitId1110
                def maitData = MaitUtil.getMaitData(maitID)?.get(0)
                // 麦田配置可控制是否需要倒计时
                String needCountDown = maitData?.get("needPreCountDown") != null ? String.valueOf(maitData.get("needPreCountDown")) : "1"
                CountdownVOV2 countDown = new CountdownVOV2()
                if ("0" != needCountDown) {
                    countDown.countdown = shango?.startTime - System.currentTimeSeconds()
                    countDown.countdownTitle = "距开始"
                    countDown.countdownTitleColor = maitData?.get("countdownColor")
                    countDown.type = 2
                    countDown.threshold = COUNTDOWNTHRESHOLD
                    long startTime = shango?.startTime
                    Date date = new Date(startTime * 1000)
                    String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
                    countDown.countdownText = dateStr + "开始"
                }
                result.countdown = countDown

                WarmUpInfo warmupInfo = new WarmUpInfo()
                warmupInfo.with {
                    price = shango?.price ? "¥" + NumUtil.formatNum(shango.price / 100D) : null
                    if (price) {
                        priceTag = maitData?.priceDesc ? maitData?.priceDesc + ": " : ("闪购价: ")
                        bgImage = maitData?.get("preCoverBg")
                        priceColor = maitData?.get("priceColor") ? maitData?.get("priceColor") : "#FFFFFF"
                    }
                }
                result.warmUpInfo = warmupInfo

                result.priceColor = "#000000"

                result.type = SHOW_TYPE_PRE
                lastPreActivityStartTime = shango.startTime
                isPre = true
                result.activityType = ActivityType.SHANGO_PRE
            }
            // 其他kv标配置的氛围预热期，并且比其他预热的开始时间要早
            if (extraPreActivity && extraPreActivity?.startTime < lastPreActivityStartTime) {
                def maitData = MaitUtil.getMaitData(extraPreActivity?.maitId1110)?.get(0)
                CountdownVOV2 countDown = new CountdownVOV2()
                countDown.countdown = extraPreActivity?.startTime - System.currentTimeSeconds()
                countDown.countdownTitle = "距开始"
                countDown.countdownTitleColor = maitData?.get("countdownColor")
                countDown.type = 2
                countDown.threshold = COUNTDOWNTHRESHOLD
                long startTime = extraPreActivity?.startTime
                Date date = new Date(startTime * 1000)
                String dateStr = new SimpleDateFormat("M月d日 HH:mm").format(date)
                countDown.countdownText = dateStr + "开始"
                result.countdown = countDown

                WarmUpInfo warmupInfo = new WarmUpInfo()
                warmupInfo.with {
                    price = extraPreActivity?.price ? "¥" + NumUtil.formatNum(extraPreActivity.price / 100D) : null
                    if (price) {
                        priceTag = maitData?.priceDesc ? maitData?.priceDesc + ": " : ""
                        bgImage = maitData?.get("preCoverBg")
                        priceColor = maitData?.get("priceColor") ? maitData?.get("priceColor") : "#FFFFFF"
                    }
                }
                result.warmUpInfo = warmupInfo

                result.priceColor = "#000000"

                result.type = SHOW_TYPE_PRE
                lastPreActivityStartTime = extraPreActivity.startTime
                isPre = true
            }

            // 普通
            if (!isPre) {
                result.type = SHOW_TYPE_NORMAL
            }
        }

        // 券后价，展示条件完全同底部的领券购买按钮
        if (Switcher.showBuyPromotion()
                && skuDO?.promotionPrice != null
                && skuDO.promotionPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal
                && !(groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend())) {

            result.discountPrice = "券后¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}"
            if (DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal != DetailContextHolder.get()?.getItemDO()?.highNowPriceVal) {
                result.discountPrice += "起"
            }
            result.discountPriceColor = result.discountPriceColor ?: "#FF4466"
            result.discountPriceBgColor = result.discountPriceBgColor ?: "#FFFFFF"
        } else {
            result.discountPrice = "" // 避免缓存合并
        }

        // 没有背景图片时文字颜色的兜底
        switch (result.type) {
            case SHOW_TYPE_NORMAL:
                break
            case SHOW_TYPE_IN_ACTIVITY:
                if (TextUtils.isEmpty(result.coverBg)) {
                    result?.countdown?.countdownTitleColor = "#000000"
                }
                break
            case SHOW_TYPE_PRE:
                if (result.warmUpInfo && TextUtils.isEmpty(result.warmUpInfo.bgImage)) {
                    result?.countdown?.countdownTitleColor = "#000000"
                }
                break
        }

        updatePintuanSummary(result, itemBaseDO, pinTuanDO)

        // 1410 活动中商品 priceTag 展示为[大促官方补贴文案]、「活动价」
        if (result.priceTag == null) {
            // 判断是否是 直播秒杀中 或者 直播同价
            boolean platformAllowanceBtn = extraDO?.isLiveSeckill() ? true : (itemBaseDO?.numTags?.contains("1733") && groovy.mgj.app.vo.Tools.getAppVersion() >= 1410)

            // platformAllowanceBtn为true，展示为[大促官方补贴文案]
            if (platformAllowanceBtn && extraDO?.platformAllowanceMsg) {
                result.priceTag = extraDO?.platformAllowanceMsg
            } else {
                // 1410 活动中商品 priceTag 固定展示为「活动价」
                if (itemBaseDO?.numTags?.contains("733")
                        && groovy.mgj.app.vo.Tools.getAppVersion() >= 1410) {
                    result.priceTag = "活动价"
                } else {
                    result.priceTag = itemBaseDO?.discountDesc
                }
            }
        }

        // 如果是虚拟优惠券商品且eventPriceTag.text为空，就占用下显示个提示
        if (Tools.isVirtualCouponItem() && result?.eventTags?.isEmpty()) {
            result.eventTags.add(new PriceTagVO(
                    text: "购买后自动发券，不支持退款"
            ))
        }

        // 2107资源位配的tags
        if (activityDO?.eventTags && need2107EventTag) {
            result?.eventTags?.addAll(activityDO?.eventTags?.collect {
                new PriceTagVO(
                        text: it.tagText,
                        bgColor: it.tagBgColor,
                        textColor: "#FF2255"
                )
            })
        }

        // 处理区间价
        result.with {
            if (price?.contains("~")) {
                price = price?.replace("~¥", "~")
                // 比较一言难尽的处理
                // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
                if (price?.contains(".00~") && price?.endsWith(".00")) {
                    price = price?.replace(".00", "")
                }
            }
            if (warmUpInfo?.price?.contains("~")) {
                warmUpInfo.price = warmUpInfo.price.substring(0, warmUpInfo.price.indexOf("~"))
                warmUpInfo.price += "起"
            }
        }

        // 为了esimerge能正确覆盖的操作
        result.with {
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (countdown == null) {
                countdown = new CountdownVOV2()
            }
            if (priceTag == null) {
                priceTag = ""
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (warmUpInfo == null) {
                warmUpInfo = new WarmUpInfo()
            }
            if (coverBg == null) {
                coverBg = ""
            }
            if (priceColor == null) {
                priceColor == ""
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (discountPrice == null) {
                discountPrice = ""
            }
        }

        return result
    }

    /**
     * 更新拼团商品的价格以及价格标
     * @param summary
     * @param input1
     * @param pintuanDO
     * @return
     */
    def updatePintuanSummary(PriceBannerVOV2 priceBannerVO, ItemBaseDO input1, PinTuanDO pintuanDO) {
        //是拼团
        if (Tools.isPintuan(input1, pintuanDO)) {
            //price 现价

            priceBannerVO.lowNowPrice = pintuanDO.skuInfo?.lowNowPrice
            priceBannerVO.highNowPrice = pintuanDO.skuInfo?.highNowPrice

            priceBannerVO.updatePrices()

            //priceTag 现价标
            if (priceBannerVO?.priceTag == null) {
                priceBannerVO.priceTag = "拼团价"
            }
        }
    }

    /**
     * 填充比价相关字段
     */
    def fillBiJia(PriceBannerVOV2 vo, CountdownInfo countdownInfo, ItemBaseDO itemBaseDO, LiveSimpleDO liveSimpleDO) {
        vo.type = SHOW_TYPE_BIJIA

        String wwPrice = TextUtils.isEmpty(countdownInfo?.outNetPrice) ? "" : "¥" + ItemPriceVO.ItemPriceCalculator.wipeZeroSuffix(countdownInfo.outNetPrice)
        String wwImage = TextUtils.isEmpty(countdownInfo?.outNetImage) ? "" : countdownInfo.outNetImage

        // 如果商品有视频，或者是直播商品进图墙活动期内（总之就是客户端详情页有视频可供播放时），不展示外网图，避免 UI 遮挡问题
        if (itemBaseDO?.video || (groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend() && liveSimpleDO?.pickedExplainInfo)) {
            wwImage = ""
        }

        vo.updatePrices(true, true)

        vo.biJia = new BiJiaVO(
                mgjPrice: vo.price, 
                mgjOldPrice: vo.oldPrice,
                wwPrice: wwPrice,
                wwImage: wwImage
        )

        def maitData = MaitUtil.getMaitData(countdownInfo.maitId1110)?.get(0)
        vo.biJia.mgjPriceColor = maitData?.get("priceColor") ?: "#FFFFFF"
        vo.biJia.mgjOldPriceColor = "#B3FFFFFF"
        vo.biJia.wwImageTag = maitData?.get("goodsTag") ?: "某宝"
        vo.biJia.wwPriceColor = maitData?.get("outPriceColor") ?: "#7E3E00"

        if (TextUtils.isEmpty(wwImage)) {
            if (TextUtils.isEmpty(wwPrice)) {
                // 这种情况不会有，但是 UI 稿设计了，客户端也开发了
                vo.biJia.bgImage = "https://s10.mogucdn.com/mlcdn/c45406/210105_81bg6cb2g6f349a77j0cib20i3e8f_1125x180.webp"
            } else {
                vo.biJia.bgImage = maitData?.get("coverBg")
            }
        } else {
            vo.biJia.bgImage = maitData?.get("hasImgCoverBg")
        }
    }
}
