package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "itemDetailNormal", defaultValue = DefaultType.NULL)
class ItemDetailNormal implements IOneDependTranslator<ItemBaseDO,Object>{
    class ItemDetailNormalVO{
        String iid
        String detailURL
    }

    @Override
    Object translate(ItemBaseDO input1) {
        if (!input1){
            // 业务上可以返回NULL
            return null
        }
        new ItemDetailNormalVO(
                iid: input1?.iid,
                detailURL: input1?.iid ? "http://shop.mogujie.com/ajax/mgj.pc.detailinfo/v1?_ajax=1&itemId=${input1?.iid}" : null
        )
    }
}