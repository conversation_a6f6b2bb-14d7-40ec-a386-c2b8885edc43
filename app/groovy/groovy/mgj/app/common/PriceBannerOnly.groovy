package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.dailyconfig.domain.NewActivityDO

/**
 * Created by enuola on 1/5/23.
 * 价格条上方的纯图片banner
 */

@Translator(id = "priceBannerOnly")
class PriceBannerOnly implements IOneDependTranslator<NewActivityDO, Object> {

    class PriceBannerOnlyVO {
        String imageURL
        String link
        Integer w
        Integer h
    }

    @Override
    Object translate(NewActivityDO newActivityDO) {

        //2023.01.04号：普通商品展示新banner
        if (newActivityDO) {
            def result = new PriceBannerOnlyVO()
            result.imageURL = newActivityDO?.img ? newActivityDO?.img : ""
            // result.link = newActivityDO?.link ? newActivityDO?.link : ""
            result.w = newActivityDO?.w
            result.h = newActivityDO?.h

            return result
        } else {
            return new Object()
        }
    }
}