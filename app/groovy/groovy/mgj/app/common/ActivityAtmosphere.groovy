package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.TextStyle

/**
 * Created by fufeng on 2017/9/14.
 */

@Translator(id = "activityAtmosphere")
class ActivityAtmosphere implements ITwoDependTranslator<ItemBaseDO, ExtraDO, Object> {
    class ActivityAtmosphereVO {
        String title
        String signedTitle
        TextStyle textStyle
    }
    @Override
    Object translate(ItemBaseDO itemBaseDO, ExtraDO extraDO){
        if (extraDO?.crossStoreDiscount?.length()) {
            def result = new ActivityAtmosphereVO()
            result.textStyle = new TextStyle(textColor: "#FA6813", fontSize: 12, backgroundColor: "#FFF3E6")
            result.title = extraDO.crossStoreDiscount
            result.signedTitle = extraDO.crossStoreDiscount
            return result
        } else {
            return new Object()
        }
    }
}

