package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "topBarNormal", defaultValue = DefaultType.NULL)
class TopBarNormal implements IOneDependTranslator<ItemBaseDO,Object>{

    class TopBarNormalVO{
        String iid
        String reportUrl
        int state
        boolean canEdit
        TopBarEventIconVO eventIcon
    }

    class TopBarEventIconVO{
        String imageURL
        String linkURL
    }
    @Override
    Object translate(ItemBaseDO input1) {
        if (!input1){
            //业务上可以return null
            return null
        }
        //MARK:这里要保证最新，所以要走ESI
        //红包麦田配置
        def iconRes = MaitUtil.getMaitData(12838)
        String imageURL = iconRes?.get(0)?.get("imageURL")
        String linkURL = iconRes?.get(0)?.get("linkURL")

        return new TopBarNormalVO(
                iid: input1?.iid,
                reportUrl: StrategyUpUtil.upUrl("https://h5.mogujie.com/report/add.html?itemId=${input1?.iid}"),
                state: input1?.state,
                canEdit: false,
                eventIcon:(imageURL && linkURL)?new TopBarEventIconVO(
                        imageURL:imageURL,
                        linkURL:linkURL
                ) : new TopBarEventIconVO()
        )
    }
}