package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by pananping on 2020/12/31.
 */
@Translator(id = "topBarNormalV3", defaultValue = DefaultType.NULL)
class TopBarNormalV3 implements IOneDependTranslator<ItemBaseDO, Object> {

    static class TopBarNormalV3VO {
        String reportUrl
        boolean canEdit
        String searchPlaceholder
        String searchUrl
    }

    @Override
    Object translate(ItemBaseDO input1) {
        def vo = new TopBarNormalV3VO(
                reportUrl: StrategyUpUtil.upUrl("https://h5.mogujie.com/report/add.html?itemId=${input1?.iid}"),
                canEdit: false,
                searchPlaceholder: "搜索全站商品",
                searchUrl: "mgj://searchentrance?from=2"
        )
        return vo
    }
}