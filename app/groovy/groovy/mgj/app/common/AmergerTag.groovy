package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator

/**
 * Created by xy on 2017/8/22.
 */
@Translator(id = "amergerTag")
class AmergerTag implements IZeroDependTranslator<Object> {

    @Override
    Object translate() {
        if (DetailContextHolder.get().isDyn()) {
            return null;
        }
        return System.currentTimeMillis();
    }
}
