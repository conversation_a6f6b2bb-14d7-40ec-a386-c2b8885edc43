package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import org.apache.commons.collections4.CollectionUtils


@Translator(id = "iOSSearchData", defaultValue = DefaultType.NULL)
class DetailiOSSearchData implements ITwoDependTranslator<ItemBaseDO, ShopDO, Object> {

    class SearchDataVO {
        Integer cSells
        String desc
        String iid
        String imageURL
        String tags
        String title
    }

    @Override
    SearchDataVO translate(ItemBaseDO input1, ShopDO input2) {
        if (!input1) {
            // 业务上可以返回NULL
            return null
        }
        String defaultImageUrl = null;
        if (CollectionUtils.isNotEmpty(input1?.topImages)){
            defaultImageUrl = input1.topImages?.first();
        }
        return new SearchDataVO(
                cSells: input2?.CSells,
                desc: input1.desc,
                iid: input1.iid,
                imageURL: defaultImageUrl,
                tags: input1.tags,
                title: input1.title
        )
    }
}
