package groovy.mgj.app.common

import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2018/12/20.
 * 集中放置一些判断版本相关的逻辑
 */
class VersionController {
    // 1160后，自营标从summary提到gallery展示，summary里不返回自营标
    static boolean needTranslateSelfIcon() {
        int appVersion = Tools.getAppVersion()
        return appVersion >= 1160
    }

    // lazyload组件在新版本中改为如果后端不返回data，就不会出来，也就不会请求单独接口
    static boolean lazyLoadVisibleControledByDataIsNULL() {
        return Tools.getAppVersion() >= 1160
    }

    // 展示自动计算的优惠价格，在立即购买按钮上和sku上
    static boolean showBuyPromotion() {
        return Tools.getAppVersion() >= 1350
    }

    // 只有1580+的快抢才有优惠券提示
    static boolean showFastBuyPromotion() {
        return Tools.getAppVersion() >= 1580
    }
}
