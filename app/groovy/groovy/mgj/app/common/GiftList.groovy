package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.activity.domain.Alert
import com.mogujie.detail.module.activity.domain.Gift
import com.mogujie.detail.module.activity.domain.GiftType
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

@Translator(id = "giftList")
class GiftList implements ITwoDependTranslator<ActivityDO, ItemBaseDO, Object> {
    class GiftListVO {
        List<GiftListItemVO> list
    }

    class GiftListItemVO {
        String icon
        String title
        String highlightText
        String highlightColor
        String link
        Boolean showArrow
        String arrowDesc
        String type
        Alert alert

        GiftListItemVO() {}

        GiftListItemVO(Gift gift) {
            this.icon = gift.icon
            this.title = gift.title
            this.highlightText = gift.highlightText
            this.highlightColor = gift.highlightColor
            this.link = gift.link
            this.showArrow = gift.showArrow
            this.arrowDesc = gift.arrowDesc
            this.type = gift.type
            this.alert = gift.alert
        }

    }

    @Override
    GiftListVO translate(ActivityDO input1, ItemBaseDO input2) {
        if (input1?.giftList) {
            def giftlist = new GiftListVO(
                    list: input1?.giftList?.collect {
                        def item = new GiftListItemVO(it)
//                        InvokerHelper.setProperties(item, it.properties)
                        return item
                    }
            )

            if (shouldAddInstallmentTag(input2)) {
                addInstallmentTag(giftlist.list, input2)
            }

            return giftlist
        } else if (shouldAddInstallmentTag(input2)) {
            def giftlist = new GiftListVO(
                    list: []
            )
            addInstallmentTag(giftlist.list, input2)
            return giftlist
        } else {
            return new GiftListVO() //保证esi能覆盖缓存数据
        }

    }

    static def shouldAddInstallmentTag(ItemBaseDO item) {
        def res = MaitUtil.getMaitData(29802)
        return res && item?.canApplyInstallment && ItemTag.INSTALMENT in item?.itemTags
    }

    def addInstallmentTag(List<GiftListItemVO> list, ItemBaseDO item) {
        //如果符合分期购资格，则在列表上面加入分期购入口
        //读麦田配置29802
        def res = MaitUtil.getMaitData(29802)
        if (res) {
            String icon = res.get(0)?.get('icon')
            String link = res.get(0)?.get('link')
            String text = res.get(0)?.get('text')
            String accessoryText = res.get(0)?.get('accessoryText')
            def installmentTag = new GiftListItemVO(
                    icon: ImageUtil.img(icon),
                    title: text,
                    link: link,
                    showArrow: link ? true : false,
                    arrowDesc: link ? accessoryText : null,
                    type: "bfm"
            )
            list?.add(0, installmentTag)
        }
    }

}
