package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/8/15.
 */
@Translator(id = "bottomBarNormalV2")
class BottomBarNormalV2 implements ISixDependTranslator<ItemBaseDO, ExtraDO, GroupbuyingDO, PinTuanDO, SkuDO, LiveDO, Object> {
    @Override
    Object translate(ItemBaseDO input1, ExtraDO input2, <PERSON>buyingD<PERSON> groupbuyingDO, PinTuanD<PERSON> pintuanDO, SkuD<PERSON> skuDO, LiveDO aLiveDO) {
        if (Tools.isPintuan(input1, pintuanDO)) {
            return new Object()
        }
        def translator = new BottomBarNormal()
        return translator.translate(input1, input2, groupbuyingDO, skuDO, pintuanDO, aLiveDO)
    }
}
