package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.mgj.app.vo.CountdownVO

/**
 * Created by fufeng on 2017/4/1.
 */
@Translator(id = "countDownNormalV2Group")
class CountDownNormalV2Group implements IFiveDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, CountDownNormalV2GroupVO> {    static class CountDownNormalV2GroupVO {
        CountdownVO countDownNormalV2
    }
    @Override
    CountDownNormalV2GroupVO translate(ActivityDO input1, GroupbuyingDO input2, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO) {        CountDownNormalV2 translator = new CountDownNormalV2()
        CountdownVO countdownVO = translator.translate(input1, input2, itemBaseDO, pinTuanDO, normalCountdownDO)
        if (countdownVO == null || countdownVO?.countdown == null || countdownVO?.countdownBgImg == null) {
            return new CountDownNormalV2GroupVO()
        }
        else {
            return new CountDownNormalV2GroupVO(
                    countDownNormalV2: countdownVO
            )
        }
    }
}
