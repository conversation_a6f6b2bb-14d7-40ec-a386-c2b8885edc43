package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.Tools

/**
 * Created by pananping on 2020/12/14.
 * 位于店铺模块下方的根据店铺标配的 banner
 */

@Translator(id = "commonImage1470", defaultValue = DefaultType.NULL)
class CommonImage1470 implements IOneDependTranslator<ShopDO, Object> {

    class CommonImageVO extends HideControl {
        String imageURL
        String link
        Integer w
        Integer h
    }

    @Override
    Object translate(ShopDO shopDO) {

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(152256)
        if (maitData?.size() > 0) {
            for (Map<String, Object> item in maitData) {

                if ((item.get("shopTag") == 0 || shopDO?.getTags()?.contains(item.get("shopTag")))
                        && item.get("bannerImage") != null) {
                    Integer width, height
                    (width, height) = Tools.getCDNImageWidthAndHeight((String) item.get("bannerImage"))
                    return new CommonImageVO(
                            _extra_control_hide_: false,
                            imageURL: item.get("bannerImage"),
                            link: item.get("bannerJumpURL"),
                            w: width, h: height
                    )
                }
            }
        }

        return new CommonImageVO(_extra_control_hide_: true)
    }
}