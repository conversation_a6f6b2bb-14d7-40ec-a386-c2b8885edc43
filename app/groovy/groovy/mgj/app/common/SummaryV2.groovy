package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.common.common.SummaryRateInfoVO
import groovy.mgj.app.vo.ActivityVO
import groovy.mgj.app.vo.PriceTagVO
import groovy.mgj.app.vo.ShareInfo
import groovy.mgj.app.vo.SummaryVO
import groovy.mgj.app.vo.TextStyle
import org.apache.http.util.TextUtils

import java.text.SimpleDateFormat

/**
 * Created by fufeng on 2017/8/16.
 */
@Translator(id = "summaryV2")
class SummaryV2 implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, RateDO, ShopDO, PinTuanDO, NormalCountdownDO,SkuDO, SummaryVO> {
    @Override
    SummaryVO translate(ItemBaseDO input1, ActivityDO input2, PresaleDO input3, GroupbuyingDO input4, RateDO rateDO, ShopDO shopDO, PinTuanDO pintuanDO, NormalCountdownDO normalCountdownDO,SkuDO skuDO) {
        if (!input1) {
            //如果 ItemBase都是null了，就return null吧，至少可以保持有价格出现
            return null
        }
        def summary = new SummaryVO()

        def defaultBgColor = "#FFE8EE"
        def defaultTextColor = "#FF2255"

        //默认属性
        summary.priceColor = "#333333"
        summary.title = input1.title
        summary.highNowPrice = input1.highNowPrice
        summary.lowNowPrice = input1.lowNowPrice
        summary.highPrice = input1.highPrice
        summary.lowPrice = input1.lowPrice

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        summary.installment = hintWrapper?.hint
        summary.installmentStyle = hintWrapper?.textStyle

        summary.installment = summary.installment ?: ""

        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get("xsbk")

        //优先级 预售>大促>限时爆款>团购>普通
        if (input3 != null) {
            //预售
            summary.with {
                isShowPrice = true
                titleIcon = ""
                price = input3.totalPrice
                oldPrice = input1.lowPrice
                priceTag = new PriceTagVO(
                        text: "预售价",
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                eventPrice = input3.deposit
                eventPriceTag = input3.presaleDesc ? new PriceTagVO(
                        text: "定金",
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                ) : null
            }
        } else if (input2 != null && (input2.warmUpPrice || input2.inActivityItem)) {
            //大促
            summary.with {
                if(input2.activityTitleImage){
                    titleIcon = input2.activityTitleImage
                }

                if(input2.warmUpPrice){
                    isShowPrice = true
                }
                else {
                    isShowPrice = false
                }

                updatePrices()
                priceTag = input2.priceDesc ? new PriceTagVO(
                        text: input2.priceDesc
                ) : null

                //正式期
                //颜色变成红色
                if (input2.inActivityItem) {
                    priceColor = defaultTextColor
                }

                //预热期
                eventPrice = input2.warmUpPrice?.price
                eventPriceColor = input2.warmUpPrice?.color
                if (eventPrice) {
                    eventPriceTag = new PriceTagVO(
                            text: input2.warmUpPrice?.priceDesc,
                            bgColor: null,//无背景色
                            textColor: defaultTextColor
                    )
                }
            }
            //升级拼团信息
            updatePintuanSummary(summary, input1, pintuanDO)
        }
        else if (xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY) {
            // 限时爆款
            summary.updatePrices()
            summary.priceTag = new PriceTagVO(
                    text: MaitUtil.getMaitData(xsbk.maitId)?.get(0)?.get("priceDesc")?:"限时爆款价",
                    bgColor: defaultBgColor,
                    textColor: defaultTextColor
            )
            updatePintuanSummary(summary, input1, pintuanDO)
            summary.isShowPrice = false

            if(input2 != null && !input2.warmUpPrice){
                summary.titleIcon = input2.activityTitleImage
            }
            else{
                Map<String, Object> maitData = MaitUtil.getMaitData(123210)?.get(0)
                summary.titleIcon=maitData?.get("titleIcon")
            }

        } else if (input4 != null && (input4.status == TuanStatus.IN || input4.status == TuanStatus.PRE)) {
            //团购中
            if (input4.status == TuanStatus.IN) {
                if (input1) {
                    summary.updatePrices()
                } else {
                    summary.price = input4.price
                }
                summary.priceTag = new PriceTagVO(
                        text: '团购价',
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                summary.isShowPrice = false
            } else if (input4.status == TuanStatus.PRE) {
                //预热期
                if (input1) {
                    summary.updatePrices()
                }
                summary.eventPrice = input4.price
                summary.eventPriceColor = defaultTextColor
                summary.eventPriceTag = new PriceTagVO(
                        text: '团购价',
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
                summary.isShowPrice = true
                if (input4.bizType == TuanBizType.UZHI) {
                    //U质团的预告期间，如果商品正在招商非渠道拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                    if (Tools.isSystemPintuan(input1, pintuanDO)) {
                        summary.eventPriceTag = new PriceTagVO( text: "", bgColor: "", textColor: "" )
                        summary.eventPrice = ""
                        summary.eventPriceColor = ""

                        Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
                        summary.titleIcon=maitData?.get("titleIcon")
                        summary.isShowPrice = false
                    }
                }
            }
            //升级拼团信息
            updatePintuanSummary(summary, input1, pintuanDO)

            if(input2 != null && !input2.warmUpPrice){
                summary.titleIcon = input2.activityTitleImage
            }
            else{
                Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
                summary.titleIcon=maitData?.get("titleIcon")
            }
        }
        else if(groovy.mgj.app.vo.Tools.isSystemPintuan(input1, pintuanDO)){
            summary.updatePrices()
            updatePintuanSummary(summary, input1, pintuanDO)
            Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
            summary.titleIcon=maitData?.get("titleIcon")
            summary.isShowPrice = false
        }
        else {
            //普通
            summary.updatePrices()
            updatePintuanSummary(summary, input1, pintuanDO)
            if (summary.priceTag == null && !input2?.hideDiscount && input1?.discountDesc) {
                summary.priceTag = new PriceTagVO(
                        text: input1.discountDesc,
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor
                )
            }
            summary.isShowPrice = true
            summary.titleIcon = ""
        }

        if(input2 != null){
            //大促预热、团购预热，预售，需要展示价格，不显示大促标
            if(input2.activityState == 1.intValue() || input3 != null || input4?.status == TuanStatus.PRE){
                summary.isShowPrice = true
                summary.titleIcon = ""
            }
            else {
                //大促，非预热，隐藏价格
                summary.isShowPrice = false
                summary.titleIcon = input2.activityTitleImage
            }
        }


        // 如果是虚拟优惠券商品且eventPriceTag.text为空，就占用下显示个提示
        if (Tools.isVirtualCouponItem() && TextUtils.isEmpty(summary?.eventPriceTag?.text)) {
            summary.eventPriceTag = new PriceTagVO(
                    text:"购买后自动发券，不支持退款"
            )
            summary.eventPriceTag1110 = new PriceTagVO(
                    text:"购买后自动发券，不支持退款"
            )
        }

        // 如果之前都没有设titleIcon，然后这是个跨境电商商品，在标题前展示跨境电商标
        if (input1.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM
                || input1.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
            if (TextUtils.isEmpty(summary.titleIcon)) {
                summary.titleIcon = ImageUtil.img("/mlcdn/c45406/180906_6ljkbb2040493k4bl7de3k76ld54b_136x72.png")
            }
        }


        //其他属性
        summary.with {
            title = input1?.title

            eventTags = input2?.eventTags?.collect {
                new PriceTagVO(
                        text: it.tagText,
                        bgColor: it.tagBgColor,
                        textColor: defaultTextColor
                )
            }

            //去麦田获取分享Icon和Text
            def shareMaitInfo = MaitUtil.getMaitData(38873)
            if (shareMaitInfo?.get(0)) {
                shareInfo = new ShareInfo(
                        shareIcon: shareMaitInfo.get(0)?.get("shareIcon"),
                        shareText: shareMaitInfo.get(0)?.get("shareText")
                )
            }

            //非ESI时才返回
            if (!DetailContextHolder.get().isDyn()) {
                //DSR信息
                if (rateDO != null && input1 != null) {
                    SummaryRateInfoVO summaryRateInfoVO = new SummaryRateInfoVO(input1, rateDO, shopDO)
                    summary.rate = summaryRateInfoVO
                }
            }
        }

        // 跟share组件里的url一样的逻辑
        String url = groovy.mgj.app.vo.Tools.getH5Url(DetailContextHolder.get().getItemDO().getItemId())
        if (pintuanDO?.activityId) {
            url = url + "&businessId=${pintuanDO.activityId}"
        }
        summary.shareUrl = url

        //clean up 为了esi能覆盖
        summary.with {
            //oldPrice
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (priceTag == null) {
                priceTag = new PriceTagVO(
                        text: null,
                        textColor: null,
                        bgColor: null
                )
            }
            //eventPrice
            if (eventPrice == null) {
                eventPrice = ""
            }
            //eventPriceTag
            if (eventPriceTag == null) {
                eventPriceTag = new PriceTagVO(
                        text: null,
                        textColor: null,
                        bgColor: null
                )
            }
            //eventTags
            if (eventTags == null) {
                eventTags = []
            }
            //activity
            if (activity == null) {
                activity = new ActivityVO()
            }
        }
        return summary
    }

    /**
     * 更新拼团商品的价格以及价格标
     * @param summary
     * @param input1
     * @param pintuanDO
     * @return
     */
    def updatePintuanSummary(SummaryVO summary, ItemBaseDO input1, PinTuanDO pintuanDO)
    {
        //是拼团
        if (Tools.isPintuan(input1, pintuanDO)) {
            //price 现价

            summary.lowNowPrice = pintuanDO.skuInfo.lowNowPrice
            summary.highNowPrice = pintuanDO.skuInfo.highNowPrice

            summary.updatePrices()

            //priceTag 现价标
            if (summary?.priceTag == null || summary?.priceTag?.text == null) {
                def defaultBgColor = "#FFE8EE"
                def defaultTextColor = "#FF2255"
                summary.priceTag = new PriceTagVO(
                        text: "拼团价",
                        bgColor: defaultBgColor,
                        textColor: defaultTextColor)
            }
        }
    }
}
