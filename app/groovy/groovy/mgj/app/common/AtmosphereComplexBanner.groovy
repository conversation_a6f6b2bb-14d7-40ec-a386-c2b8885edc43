package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2019/1/30.
 */
@Translator(id = "atmosphereComplexBanner", defaultValue = DefaultType.NULL)
class AtmosphereComplexBanner implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, AtmosphereBannerComplexVO> {

    static class AtmosphereBannerComplexVO {
        String background
        String leftText
        String rightText
        String link
        String acm
        List<String> highlight
        String textColor
        Long countdown

        Map<String, String> imageData
        Map<String, String> leftTextData
        Map<String, String> rightTextData

        static AtmosphereBannerComplexVO getEmpty() {
            return new AtmosphereBannerComplexVO()
        }
    }

    // 活动开始
    // >0	此商品xx元闪购中，仅剩xx件！ 距结束xx：xx：xx>
    // =0	此商品xx元闪购库存已售罄
    @Override
    AtmosphereBannerComplexVO translate(ItemBaseDO itemBaseDO, FastbuyDO fastbuyDO) {
        if (shouldShow(itemBaseDO)) {
            AtmosphereBannerComplexVO VO = new AtmosphereBannerComplexVO()
            VO.link = "mgj://detail?iid=${itemBaseDO?.iid}&fastbuyId=${IdConvertor.idToUrl(itemBaseDO?.limitDiscountInfo?.activityId)}&detailType=FastBuy".toString()
            Map<String, Object> maitData = MaitUtil.getTargetedMaitData(134023)?.get(0)
            VO.background = maitData?.get("guideAtmosphereBackground")
            VO.textColor = maitData?.get("guideAtmosphereTextColor") ?: "#FFFFFF"
            VO.highlight = new ArrayList<>()
            int stock = itemBaseDO?.limitDiscountInfo?.stock ?: 0
            String activityPrice = "${NumUtil.formatPriceDrawer(itemBaseDO?.limitDiscountInfo?.limitPirce)}元"
            VO.highlight.add(activityPrice)
            switch (Tools.limitedDiscountState(itemBaseDO)) {
                case 2:
                    // 活动中
                    // 没库存，不可点击
                    if (0 == stock) {
                        VO.leftText = "此商品${activityPrice}闪购库存已售罄"
                        VO.rightText = ""
                        VO.link = ""
                        VO.countdown = 0
                    } else {
                        VO.leftText = "此商品${activityPrice}闪购中，仅剩${stock}件！"
                        VO.highlight.add("${stock}件".toString())
                        VO.rightText = "快去抢>"
                        VO.countdown = itemBaseDO?.limitDiscountInfo?.endTime - System.currentTimeSeconds()
                    }
                    break
                default:
                    // 结束，不可点击
                    VO.rightText = ""
                    VO.link = ""
                    VO.countdown = 0
                    VO.leftText = ""
                    break
            }

            VO.imageData = new HashMap<>()
            VO.imageData.put("imgUrl", VO.background)
            VO.imageData.put("link", VO.link)

            VO.leftTextData = new HashMap<>()
            VO.leftTextData.put("text", VO.leftText)
            VO.leftTextData.put("link", VO.link)

            VO.rightTextData = new HashMap<>()
            VO.rightTextData.put("text", VO.rightText)
            VO.rightTextData.put("link", VO.link)

            return VO
        } else {
            return AtmosphereBannerComplexVO.getEmpty()
        }
    }

    static boolean shouldShow(ItemBaseDO itemBaseDO) {
        return (Tools.limitedDiscountState(itemBaseDO) == 2 // 活动中才展示
                && (Tools.hasDataId("atmosphereComplexBanner") || Tools.hasDataId("atmosphereComplexBannerGroup")))
    }
}
