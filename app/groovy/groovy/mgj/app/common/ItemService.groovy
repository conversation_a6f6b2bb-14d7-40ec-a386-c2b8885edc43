package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.CodecUtil
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.metabase.spring.client.MetabaseClient
import groovy.mgj.app.common.common.TagItemVO
import org.apache.commons.lang3.StringUtils

import javax.annotation.Resource

@Translator(id = "itemService", defaultValue = DefaultType.NULL )
class ItemService implements IThreeDependTranslator<ExtraDO, ItemBaseDO, ShopDO, Object> {

    class ItemServiceVO {
        TagItemVO[] normalTags //要加密
        TagItemVO[] extraTags
    }

    class ColumnsVOBuilder {
        static def columns(ExtraDO extra) {
            [extra.express, "销量 " + extra.sales.toString()].collect { CodecUtil.encryptWithCache(it) }
        }
    }

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    @Override
    ItemServiceVO translate(ExtraDO input1, ItemBaseDO input3, ShopDO input4) {
        if (input1?.sales == null) {
            // 业务上可以返回NULL
            return null
        }

        def normalTags = [
                new TagItemVO(
                        name: CodecUtil.encryptWithCache("销量 " + input1.sales?.toString()),
                        icon: ImageUtil.img("/p1/161117/idid_ifrdkytcgbstoobummzdambqmeyde_512x512.png")
                ),
                new TagItemVO(
                        name: CodecUtil.encryptWithCache(input1.express ?: ""),
                        icon: ImageUtil.img("/p1/161117/idid_ifqwgnbsmjsdoobummzdambqgyyde_512x512.png")
                )
        ]

        //发货地标
        if (input1.xiaodianAdress) {
            normalTags.add(new TagItemVO(
                    name: CodecUtil.encryptWithCache(input1?.xiaodianAdress),
                    icon: ImageUtil.img("/p1/161117/idid_ie4tgmbsgjsdoobummzdambqgqyde_512x512.png")
            ))
        }

        def extraTags = []

        if (input4?.services) {
            extraTags.addAll(input4?.services?.collect {
                new TagItemVO(
                        name: it.name,
                        icon: it.icon
                )
            })
        }
        //如果extraTags中有标太长，在客户端部分机型上会重叠，所以把长度超过阈值的tag放到后面去，这样可以隐藏起来
        def maxWords = "30天无理由退货".size()
        String text = metabaseClient.get("noRefoundText");
        text = StringUtils.isBlank(text) ? "不支持无理由退货" : text;
        extraTags = extraTags.sort { a, b ->
            if (a.name == text || b.name == text) {
                return 0
            }
            def aLen = a.name.size()
            def bLen = b.name.size()
            if (aLen == bLen || (aLen < maxWords && bLen < maxWords)) {
                return 0
            }
            aLen > bLen ? 1 : -1
        }

        //分期标
        if (ItemTag.INSTALMENT in input3?.itemTags) {
            def intallmentTag = new TagItemVO(
                    name: "白付美分期购",
                    icon: ImageUtil.img("/p1/170215/idid_ie4tonztmjqtcobrmuzdambqgqyde_60x60.png"),
                    content: "白付美分期购：在我的钱包 →白付美页面开通即可使用分期购。本月消费，分3/6/12期还款，每月只还一点点，轻松享受大牌好货。（分12期只对部分用户开放。）"
            )
            if (!extraTags) {
                extraTags = []
            }
            extraTags.add(0, intallmentTag)
        }

        return new ItemServiceVO(
                normalTags: normalTags,
                extraTags: extraTags
        )
    }
}
