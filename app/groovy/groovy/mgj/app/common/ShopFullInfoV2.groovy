package groovy.mgj.app.common

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.common.ShopFullInfoVO
import groovy.mgj.app.common.common.ShopLabel
import groovy.mgj.app.vo.Tools

@Translator(id = "shopFullInfoV2", defaultValue = DefaultType.NULL)
class ShopFullInfoV2 implements IThreeDependTranslator<ShopDO, LiveDO, LiveSimpleDO, Object> {
    static class DescData {
        boolean show
        String desc
    }

    @Override
    ShopFullInfoVO translate(ShopDO shopDO, LiveDO theLiveDO, LiveSimpleDO liveSimpleDO) {

        boolean needHide = false

        // 好店优选屏蔽店铺
        if (Tools.isHaoDianYouXuan(shopDO)) {
            needHide = true
        }
        // 主播推荐屏蔽店铺
        if (Tools.isLiveSource() && Tools.isInLiveRecommend()) {
            needHide = true
        }
        // >= 1490 版本，主播买手店展示时屏蔽店铺
        if (liveSimpleDO?.actUserInfo && Tools.getAppVersion() >= 1490) {
            needHide = true
        }

        boolean needDesc
        String shopDesc = ""
        try {
            DescData descData = JSON.parseObject(MetabaseUtil.get("shopStarDesc"), DescData.class)
            needDesc = descData.show
            shopDesc = descData.desc
        } catch (Exception ignore) {
            needDesc = false
        }
        shopDesc = needDesc ? (shopDesc ?: "") : ""
        def shopInfo = new ShopFullInfo().translate(shopDO)
        if (!shopInfo) {
            // 除了shopDesc，其他走缓存
            return new ShopFullInfoVO (
                shopDesc: shopDesc
            )
        }
        shopInfo.shopDesc = shopDesc
        shopInfo.score = shopInfo.score?.grep {
            it?.name != "价格合理"
        }
        // 处理销量、收藏数展示
        if (shopInfo.cFans >= 100000) {
            shopInfo.cFansString = String.format("%.1f万", shopInfo.cFans/10000.0)
        }
        else {
            shopInfo.cFansString = "" + shopInfo.cFans
        }
        if (shopInfo.cSells >= 100000) {
            shopInfo.cSellsString = String.format("%.1f万", shopInfo.cSells/10000.0)
        }
        else {
            shopInfo.cSellsString = "" + shopInfo.cSells
        }
        shopInfo.labels = shopDO?.labels?.collect {
            new ShopLabel(
                    text: it.name,
                    id: it.sort,
                    link: ""
            )
        }
        shopInfo.dynLabels = shopDO?.dynLabels?.collect {
            new ShopLabel(
                    text: it.name,
                    id: it.sort,
                    link: ""
            )
        }
        String labelPriorityList = MetabaseUtil.get("shopLabelPriority")
        if (labelPriorityList != null) {
            String[] ids = labelPriorityList.split(",")
            List<Integer> priorityList = new ArrayList<>()
            for (int index = 0; index < ids.length; index++) {
                try {
                    int id = Integer.parseInt(ids[index])
                    priorityList.add(id)
                } catch (Exception ignore) {
                }
            }
            shopInfo.labelPriorityList = priorityList
        }

        // for esi merge
        if (shopInfo.dynLabels == null) {
            shopInfo.dynLabels = new ArrayList<>()
        }

        // 因为这个组件有些数据动态请求是不返回的，而且又是有时候展示有时候不展示
        // 如果在非动态请求的时候返回了空数据缓存起来了，缓存失效前又变成要展示了，数据就不完整了
        // 所以缓存的那次通过额外的字段让客户端控制不展示，这样缓存不会出问题
        if (needHide) {
            if (DetailContextHolder.get().isDyn()) {
                return new ShopFullInfoVO()
            } else {
                shopInfo?._extra_control_hide_ = true
            }
        } else {
            shopInfo?._extra_control_hide_ = false
        }

        return shopInfo
    }
}
