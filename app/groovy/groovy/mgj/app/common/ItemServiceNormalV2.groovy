package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.metabase.spring.client.MetabaseClient
import groovy.mgj.app.common.common.ItemServiceProvider
import groovy.mgj.app.vo.Tools
import groovy.mgj.app.common.common.ItemServiceBasicVO

import javax.annotation.Resource

/**
 * Created by fufeng on 2017/3/28.
 */
@Translator(id = "itemServiceNormalV2")
class ItemServiceNormalV2 implements ITwoDependTranslator<ItemBaseDO, ShopDO, ItemServiceBasicVO> {

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private ItemServiceProvider itemServiceProvider = new ItemServiceProvider()

    @Override
    ItemServiceBasicVO translate(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        if (DetailContextHolder.get().isDyn()) {
            //业务上可以返回NULL
            return null
        }

        Boolean isExcellent = Tools.isGoodItem(itemBaseDO, shopDO)
        if (isExcellent) {
            return new ItemServiceBasicVO()
        }

        def vo = itemServiceProvider.getItemServiceBasicVO(itemBaseDO, shopDO, metabaseClient)
        if (vo == null) {
            return new ItemServiceBasicVO()
        }
        for (int i = 0; i < vo.list.length ; i++) {
            def service = vo.list[i]
            service.icon = ImageUtil.img("/mlcdn/e5265e/170412_403c781l4163j6l6bli60fg891070_36x36.png")
        }
        return vo
    }
}