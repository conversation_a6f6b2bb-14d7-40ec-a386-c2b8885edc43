package groovy.mgj.app.common

import com.mogujie.detail.core.util.MetabaseTool

/**
 * Created by wuyi on 2020/3/2.
 */
class Switcher {
    static boolean showBuyPromotion() {
        return VersionController.showBuyPromotion() && MetabaseTool.isOn("showBuyPromotion", true)
    }

    static boolean showFastBuyPromotion() {
        return VersionController.showFastBuyPromotion() && MetabaseTool.isOn("showBuyPromotion", true)
    }

    static boolean csslayoutMiniCardShare() {
        return MetabaseTool.isOn("csslayoutMiniCardShare", true)
    }
}
