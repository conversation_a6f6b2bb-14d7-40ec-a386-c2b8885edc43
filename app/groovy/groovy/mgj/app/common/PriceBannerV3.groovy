package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITenDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.dailyconfig.domain.NewActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.PriceBannerVOV2
import groovy.mgj.app.vo.PriceBannerVOV3
import groovy.mgj.app.vo.PriceTagVO
import org.apache.http.util.TextUtils

/**
 * Created by wuy<PERSON> on 2019/4/8.
 */
@Translator(id = "priceBannerV3")
class PriceBannerV3 implements ITenDependTranslator<NewActivityDO, ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, PresaleDO, ExtraDO, SkuDO, LiveSimpleDO, PriceBannerVOV3> {

    @Override
    PriceBannerVOV3 translate(NewActivityDO newActivityDO, ActivityDO activityDO, GroupbuyingDO groupbuyingDO, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presaleDO, ExtraDO extraDO, SkuDO skuDO, LiveSimpleDO liveSimpleDO) {
        PriceBannerV2 priceBannerV2 = new PriceBannerV2()
        PriceBannerVOV2 v2Ret = priceBannerV2.translate(newActivityDO, activityDO, groupbuyingDO, itemBaseDO, pinTuanDO, normalCountdownDO, presaleDO, extraDO, skuDO, liveSimpleDO)
        PriceBannerVOV3 ret = new PriceBannerVOV3(
                coverBg: v2Ret.coverBg,
                priceColor: v2Ret.priceColor,
                priceTag: v2Ret.priceTag,
                countdown: v2Ret.countdown,
                type: v2Ret.type,
                eventPrice: v2Ret.eventPrice,
                eventTags: v2Ret.eventTags,
                warmUpInfo: v2Ret.warmUpInfo,
                price: v2Ret.price,
                oldPrice: v2Ret.oldPrice,
                discountPrice: v2Ret.discountPrice,
                discountPriceColor: v2Ret.discountPriceColor,
                discountPriceBgColor: v2Ret.discountPriceBgColor,
                biJia: v2Ret.biJia,
                activityType: v2Ret.activityType
        )
        // 除了预售的定金之外，其他的都不要eventTags
        if (!presaleDO) {
            ret.eventTags = []
        }
        List<PriceTagVO> priceTags = new ArrayList<>()
        if (!TextUtils.isEmpty(ret.priceTag)) {
            priceTags.add(new PriceTagVO(
                    text: ret.priceTag,
                    textColor: v2Ret.type == 1 ? (v2Ret?.priceColor ?: "#ffffff") : "#ff4466",
                    bgColor: v2Ret.type == 1 ? "#ffffff" : "#fff1f3"
            ))
        }
        if (skuDO?.freePhases) {
            priceTags.add(new PriceTagVO(
                    text: "${skuDO.freePhases}期免手续费",
                    textColor: v2Ret.type == 1 ? (v2Ret?.priceColor ?: "#ffffff") : "#ff4466",
                    bgColor: v2Ret.type == 1 ? "#ffffff" : "#fff1f3"
            ))
        }
        ret.priceTags = priceTags

        if (extraDO?.sales) {
            String salesStr = "已售" + formatNumber((int) extraDO.sales)
            ret.salesText = salesStr
            ret.biJia?.salesText = salesStr
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        ret.mayHideOldPrice(itemBaseDO)

        //统一处理newActivityDO，为了让图片显示出来：在有coverBg，且type=0的时候，变为type=2 
        // if (!TextUtils.isEmpty(ret.coverBg) && ret.type == 0) {
        //     ret.type = 1
        // }

        return ret
    }

    def formatNumber(int num) {
        if (num >= 10000) {
            return String.format("%.1fw", num / 10000.0)
        } else {
            return "" + num
        }
    }
}

