package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.ActivityKey
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.ItemPriceVO
import groovy.mgj.app.vo.PriceHistoryVO
import org.apache.http.util.TextUtils

/**
 * Created by yanze on 2020-12-29.
 */
@Translator(id = "historyPriceV2", defaultValue = DefaultType.EMPTY_MAP)
class PriceHistoryV2 implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, RateDO, ShopDO, PinTuanDO, NormalCountdownDO, SkuDO, PriceHistoryVOV2> {

    static class PriceHistoryVOV2 extends HideControl {
        String bgImg
        String renderType
        String jumpURL
        Map renderData
    }

    @Override
    PriceHistoryVOV2 translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, PresaleDO presaleDO, GroupbuyingDO groupbuyingDO, RateDO rateDO, ShopDO shopDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, SkuDO skuDO) {

        CountdownInfo comparePrice = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.COMPARE_PRICE)

        if (comparePrice && itemBaseDO) {

            String mgjPrice
            ItemPriceVO itemPriceVO = new ItemPriceVO()
            itemPriceVO.highNowPrice = itemBaseDO?.highNowPrice
            itemPriceVO.lowNowPrice = itemBaseDO?.lowNowPrice
            itemPriceVO.updatePrices(true)
            mgjPrice = itemPriceVO.price

            if (activityDO && itemBaseDO.numTags?.contains("16") && itemBaseDO.numTags?.contains("1589")) {
                if (activityDO.warmUpPrice?.price && activityDO?.activityState == 1) {
                    // 预热期改为使用预热价
                    mgjPrice = activityDO?.warmUpPrice?.price
                    if (mgjPrice.contains("~")) {
                        mgjPrice = mgjPrice.split("~" )[0];
                    }
                }
                // 大促商品，价格低于外网价即透出比价图
                Double outNetPrice = Double.valueOf(comparePrice.outNetPrice ?: "0")
                Double mgjPriceDouble = Double.valueOf(mgjPrice.replaceAll("[^0-9\\.]", "") ?: "0")
                if (outNetPrice == 0 || mgjPriceDouble == 0 || outNetPrice <= mgjPriceDouble) {
                    return new PriceHistoryVOV2(_extra_control_hide_: true)
                }
            } else {
                // 非大促商品，必须低于外网价 20 元或者低于外网价 8 折才透出比价图
                Double outNetPrice = Double.valueOf(comparePrice.outNetPrice ?: "0")
                Double lowNowPrice = Double.valueOf(itemBaseDO.lowNowPrice ?: "0")
                if (outNetPrice == 0 || lowNowPrice == 0 || (outNetPrice - lowNowPrice < 20 && lowNowPrice / outNetPrice > 0.8)) {
                    return new PriceHistoryVOV2(_extra_control_hide_: true)
                }
            }

            String wwPrice = TextUtils.isEmpty(comparePrice.outNetPrice) ? "" : "¥" + comparePrice.outNetPrice
            String mgjImage = itemBaseDO?.topImages?.get(0)

            if (TextUtils.isEmpty(mgjImage) || TextUtils.isEmpty(mgjPrice) || TextUtils.isEmpty(wwPrice)) {
                return new PriceHistoryVOV2(_extra_control_hide_: true)
            }
            String wwImage = TextUtils.isEmpty(comparePrice.outNetImage) ? mgjImage : comparePrice.outNetImage
            return new PriceHistoryVOV2(
                    bgImg: "https://s10.mogucdn.com/mlcdn/c45406/201230_0f5hhijcj3egib51f2d1efld637lg_1125x684.png",
                    renderType: "comparePrice",
                    jumpURL: "",
                    renderData: [
                            "mgjImage": mgjImage,
                            "mgjImageLoc": [ [ 0.125, 0.184 ], [ 0.449, 0.714 ] ],
                            "mgjPrice": mgjPrice,
                            "mgjPriceCenter": [ 0.277, 0.691 ],
                            "wwImage": wwImage,
                            "wwImageLoc": [ [ 0.551, 0.184 ], [ 0.875, 0.857 ] ],
                            "wwPrice": wwPrice,
                            "wwPriceCenter": [ 0.724, 0.691 ]
                    ]
            )
        }

        PriceHistoryVO VOV1 = new PriceHistory().translate(itemBaseDO, activityDO, presaleDO, groupbuyingDO, rateDO, shopDO, pinTuanDO, normalCountdownDO, skuDO)
        PriceHistoryVOV2 VOV2 = new PriceHistoryVOV2()
        if (!TextUtils.isEmpty(VOV1.background)) {
            VOV2.bgImg = VOV1.background
            VOV2.renderType = ""
            VOV2.jumpURL = ""
        }
        return VOV2
    }
}
