package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.floatlayer.domain.FloatLayerDO

@Translator(id = "floatLayer")
class FloatLayer implements IOneDependTranslator<FloatLayerDO, Object>{
    class FloatLayerVO{
         Integer type
         Integer duration
         String[] materials
         String link
         Boolean isShow

        FloatLayerVO() {
        }

        FloatLayerVO(FloatLayerDO floatLayer) {
            if (!floatLayer) {
                return
            }
            this.type = floatLayer.type
            this.duration = floatLayer.duration
            this.materials = floatLayer.materials
            this.link = floatLayer.link
            this.isShow = floatLayer.isShow
//            InvokerHelper.setProperties(this,floatLayer.properties)
        }
    }

    @Override
    FloatLayerVO translate(FloatLayerDO input1) {
        if (!input1 || !input1.isShow){
            return new FloatLayerVO()//保证esi能覆盖缓存数据
        }
        return new FloatLayerVO(input1)
    }
}
