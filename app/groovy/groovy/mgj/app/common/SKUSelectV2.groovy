package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.seckill.domain.SeckillDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.SKUDataVOCommunicationType
import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2019/4/15.
 */
@Translator(id = "skuSelectV2", defaultValue = DefaultType.EMPTY_MAP)
class SKUSelectV2 implements IFiveDependTranslator<SkuDO, ItemBaseDO, SizeHelperDO, SeckillDO, PinTuanDO, Object>{
    static class SKULinkVO {
        String defaultTitle
        /**
         * 对于像拼团这种有两个SKU的商品，用于标志唤醒哪类的SKU
         */
        int activityType
        int skuCommunicationType
    }


    @Override
    SKULinkVO translate(SkuDO input1, ItemBaseDO input2, SizeHelperDO input3, SeckillDO seckillDO, PinTuanDO pintuanDO) {
        SKULinkVO skuLinkVO = superTranslate(input1, input2, seckillDO)
        if (skuLinkVO == null) {
            return null
        }

        if (seckillDO != null) {
            //秒杀优先
            skuLinkVO.activityType = SKUDataVOActivityType.SECKILL
            skuLinkVO.skuCommunicationType = SKUDataVOCommunicationType.DEFAULT
        } else if (Tools.isPintuan(input2, pintuanDO)) {
            //其次拼团
            skuLinkVO.activityType = SKUDataVOActivityType.PINTUAN
            skuLinkVO.skuCommunicationType = SKUDataVOCommunicationType.PINTUAN
        }

        //如果是虚拟商品且不是秒杀，则返回空对象
        if (seckillDO == null && input2?.virtualItemType != VirtualItemType.NORMAL) {
            //因为已经写了 defaultType 所以可以返回 null了，此时实际上给的是 {}
            return null
        }

        if (input3 == null || input1.sizeKey == null) {
            return skuLinkVO
        }

        return skuLinkVO
    }

    static SKULinkVO superTranslate(SkuDO input1, ItemBaseDO input2, SeckillDO seckillDO) {
        if (!input1) {
            return null
        }

        def defaultTitle = null
        if (shouldShowSKUSelect(input2) || seckillDO != null) {
            defaultTitle = "${input1.sizeKey ? input1.sizeKey : ''} ${input1.styleKey ? input1.styleKey : ''}"
        }

        return (defaultTitle != null) ? new SKULinkVO(defaultTitle: defaultTitle) : null

    }

    static def shouldShowSKUSelect(ItemBaseDO item) {
        //正常与待开售需要显示
        return item && (item.state == 0 || item.state == 3)
    }
}
