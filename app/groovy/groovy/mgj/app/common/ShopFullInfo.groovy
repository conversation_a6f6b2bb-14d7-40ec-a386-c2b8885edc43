package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.common.ShopDSRVO
import groovy.mgj.app.common.common.ShopFullInfoVO
import groovy.mgj.app.vo.Tools

@Translator(id = "shopFullInfo", defaultValue = DefaultType.NULL)
class ShopFullInfo implements IOneDependTranslator<ShopDO, Object> {

    @Override
    ShopFullInfoVO translate(ShopDO shopDO) {
        if (shopDO) {
            /**
             * shopFrom
             *
             * 0, 普通商详页 - 店铺入口 √
             * 1, 主播来源商详页 - 供应商店铺入口
             * 2, 普通商详页 - 底部店铺入口
             *
             * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
             */
            ShopFullInfoVO VO = new ShopFullInfoVO(
                    cFans: shopDO?.CFans,
                    cSells: shopDO?.CSells,
                    level: shopDO?.level,
                    star: shopDO?.star,
                    name: shopDO?.name,
                    shopId: shopDO?.shopId,
                    shopLogo: shopDO?.shopLogo,
                    shopUrl: shopDO?.shopId ?
                            Tools.appendRequestAcm("mgj://shop?shopFrom=0&shopId=" + shopDO?.shopId) : null,
                    userId: shopDO?.userId,
                    tag: shopDO?.tag,
                    score: shopDO?.score?.collect { new ShopDSRVO(it) },
            )
            // 非动态请求，而且店铺在架商品数为0(可能是因为降级也可能是因为确实为0)，就不展示
            if (shopDO?.getCGoods() && shopDO?.getCGoods() > 0) {
                String saleDescTemplate = MetabaseUtil.get("shopSaleDesc")
                VO.saleDesc = saleDescTemplate ? saleDescTemplate.replace("\${cGoods}", "${shopDO?.getCGoods()}") : "在架商品数：${shopDO?.getCGoods()}"
            }
            return VO
        }
        // 业务上可以返回NULL
        return null
    }
}
