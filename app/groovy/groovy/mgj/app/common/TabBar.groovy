package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IZeroDependTranslator
import groovy.mgj.app.common.common.AnchorDataProvider

/**
 * Created by pananping on 2020/12/31.
 */
@Translator(id = "tabBar", defaultValue = DefaultType.NULL)
class TabBar implements IZeroDependTranslator<Object> {

    static class TabBarVO {
        List<String> anchors
    }

    @Override
    Object translate() {
        List anchors = AnchorDataProvider.getAnchorDataList().collect {
            return it.text
        }
        anchors.add(0, "商品")
        return new TabBarVO(anchors: anchors)
    }
}