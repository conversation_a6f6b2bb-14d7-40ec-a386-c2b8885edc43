package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.SummaryHintProvider
import org.apache.http.util.TextUtils

import java.text.SimpleDateFormat

/**
 * Created by wuyi on 2019/3/26.
 */
@Translator(id = "hint")
class Hint implements IFourDependTranslator<ItemBaseDO, ExtraDO, SkuDO, ActivityDO, HintVO> {

    @Override
    HintVO translate(ItemBaseDO itemBaseDO, ExtraDO extraDO, SkuDO skuDO, ActivityDO activityDO) {
        // 优先级 待开售 > 延迟发货 > 优惠券 > 2107资源位
        String text = null

        // 待开售
        if (itemBaseDO?.state == 3 && extraDO?.onSaleTime > 0) {
            Date date = new Date(extraDO?.onSaleTime * 1000)
            SimpleDateFormat formatter = new SimpleDateFormat("MM-dd HH:mm")
            String dateString = formatter.format(date)
            text = "${dateString}开售，请提前设置提醒"
        }

        // 延迟发货提示文案
        if (TextUtils.isEmpty(text)) {
            text = SummaryHintProvider.getHint(skuDO, false)?.hint
        }

        // 优惠券
        if (TextUtils.isEmpty(text)) {
            if (Tools.isVirtualCouponItem()) {
                text = "购买后自动发券，不支持退款"
            }
        }

        // 2107资源位配的tags
        if (TextUtils.isEmpty(text)) {
            if (activityDO?.eventTags) {
                for (int index = 0; index < activityDO?.eventTags?.size(); index++) {
                    if (index > 0) text += "，"
                    text += activityDO?.eventTags?.get(index)?.tagText
                }
            }
        }

        if (!TextUtils.isEmpty(text)) {
            return new HintVO(
                    text: text
            )
        } else {
            return new HintVO()
        }
    }

    static class HintVO {
        String text
    }
}
