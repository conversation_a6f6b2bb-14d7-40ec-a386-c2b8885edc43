package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.vo.SKULinkVO

@Translator(id = "skuSelect")
class SKUSelect implements ITwoDependTranslator<SkuDO, ItemBaseDO, Object> {

    @Override
    SKULinkVO translate(SkuDO input1, ItemBaseDO input2) {
        if (!input1 || !shouldShowSKUSelect(input2)) {
            return null
        }

        return new SKULinkVO(
                defaultTitle: "请选择 ${input1.sizeKey ? input1.sizeKey : ''} ${input1.styleKey ? input1.styleKey :  '' } "
        )
    }

    static def shouldShowSKUSelect(ItemBaseDO item) {
        //正常与待开售需要显示
        return item && (item.state == 0 || item.state == 3)
    }
}
