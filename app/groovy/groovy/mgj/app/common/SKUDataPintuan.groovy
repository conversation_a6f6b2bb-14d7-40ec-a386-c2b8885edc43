package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SizePropData
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.SKUDataVOCommunicationType
import groovy.mgj.app.vo.SKUDataVOPintuanInfo
import groovy.mgj.app.vo.Tools

/**
 * Created by fufeng on 2017/8/16.
 */
@Translator(id = "skuDataPintuan")
class SKUDataPintuan implements ISixDependTranslator<PinTuanDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, ExtraDO, Object> {
    @Override
    Object translate(PinTuanDO input1, ItemBaseDO input2, PresaleDO input3, GroupbuyingDO input4, SizeHelperDO input5, ExtraDO extraDO) {
        if (!Tools.isPintuan(input2, input1)) {
            return new Object()
        }
        SKUDataV3 translator = new SKUDataV3()
        def ret = translator.translate(input1.skuInfo, input2, input3, input4, input5)
        ret?.activityType = SKUDataVOActivityType.PINTUAN
        ret?.skuCommunicationType = SKUDataVOCommunicationType.PINTUAN
        ret?.skuInfo?.pinTuanInfo = new SKUDataVOPintuanInfo(
                tuanType: "${input1?.tuanType}",
                icon: ImageUtil.img("/mlcdn/c45406/170830_8f71d3cgg0f2992fdfccjd44i137b_78x42.png")
        )
        return ret
    }
}