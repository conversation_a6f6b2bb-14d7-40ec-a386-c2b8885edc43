package groovy.mgj.app.common

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.collocation.domain.CollocationDO
import com.mogujie.detail.module.extra.domain.CelebrityInfo
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.ExplainInfo
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import org.apache.http.util.TextUtils
import java.util.regex.Pattern
import java.util.regex.Matcher

@Translator(id = "galleryNormal", defaultValue = DefaultType.NULL)
class GalleryNormal implements ISixDependTranslator<ItemBaseDO, ExtraDO, LiveSimpleDO, LiveDO, SkuDO, CollocationDO, Object> {

    static class GalleryNormalVO{
        String[] topImages
        CelebrityInfoVO celebrity
        VideoInfoVO video
        String topRightIcon
        SKUSelectionBarData skuSelect
        LiveInfoVO liveInfo
        GalleryExplainInfo explainInfo
        CollocationVO collocation
    }

    static class CelebrityInfoVO{
        String avatarImg
        String certTagImg
        String certTagName
        String profileUrl
        String uId
        String userName

        CelebrityInfoVO(CelebrityInfo info) {
            if (!info){
                return
            }
            this.avatarImg = info.avatarImg
            this.certTagImg = info.certTagImg
            this.certTagName = info.certTagName
            this.profileUrl = info.profileUrl
            this.uId = info.getUId()
            this.userName = info.userName
//            InvokerHelper.setProperties(this, info.properties)
        }
    }
    static class VideoInfoVO{
        String cover
        Long videoId
        Boolean  hideWhenLive = false
        Boolean autoPlayWhenWifi = false
        Boolean mutePlay = true

        Integer width

        Integer height

        static VideoInfoVO EMPTY = new VideoInfoVO(hideWhenLive: null, autoPlayWhenWifi: null, mutePlay: null)
    }

    static class LiveInfoVO {
        String actorName
        String actorAvatar
        String actorTag
        String sales
    }

    static class SKUSelectionBarData {
        Integer type // 0为颜色，1为图片
        List<SkuShowData> list
        String desc
        String allImg

        static SKUSelectionBarData EMPTY = new SKUSelectionBarData()
    }

    static class SkuShowData {
        String skuId
        String img
        String preview
        String text
    }

    static class GalleryExplainInfo {
        String coverImage
        String jumpUrl
        String firstFrame
        String videoUrl
        String videoH265Url
    }

    static class CollocationVO extends CollocationDO {
        Integer insertIndex = 1
        String buttonJumpURL
    }

    @Override
    Object translate(ItemBaseDO input1, ExtraDO input2, LiveSimpleDO liveSimpleDO, LiveDO liveDO, SkuDO skuDO, CollocationDO collocationDO) {
        if (!input1?.topImages) {
            // 业务上可以返回NULL
            return null
        }

        CollocationVO collocationVO = JSON.parseObject(JSON.toJSONString(collocationDO ?: {}), CollocationVO.class)
        collocationVO.buttonJumpURL = collocationVO.h5Link
        if (!collocationDO) {
            collocationVO.imageAndBorderList = new JSONArray([])  // 客户端是根据这个 list 是否为空判断是否需要展示搭配购，仅把这个置空即可避免缓存问题
        } else {
            collocationVO.imageAndBorderList = new JSONArray(processImageAndBorderList(collocationVO.imageAndBorderList))
        }

        def ret = new GalleryNormalVO(
                topImages: input1.topImages,
                celebrity: input2?.celebrityInfo ? new CelebrityInfoVO(input2?.celebrityInfo) : null,
                collocation: collocationVO,
                video:input1.video ? new VideoInfoVO(
                        cover: input1.video?.cover,
                        videoId: input1.video?.videoId,
                        width: input1.video?.width,
                        height: input1.video?.height,
                        hideWhenLive: false
                ) : null
        )

        // 视频讲解浮窗，和店铺自播、进房浮标互斥
        if (liveSimpleDO?.explainWindowInfo && !liveSimpleDO?.shopLiveInfo && (!liveDO?.liveItemInfos || liveDO?.liveItemInfos?.size() == 0)) {
            ret.explainInfo = new GalleryExplainInfo(
                    coverImage: input1.topImages.first(),
                    jumpUrl: liveSimpleDO.explainWindowInfo.appLink,
                    firstFrame: liveSimpleDO.explainWindowInfo.firstFrame,
                    videoUrl: liveSimpleDO.explainWindowInfo.fileUrl,
                    videoH265Url: liveSimpleDO.explainWindowInfo.videoH265Url
            )
        } else {
            ret.explainInfo = new GalleryExplainInfo(jumpUrl: "") // 客户端据 jumpUrl 判断是否要展示，置为空字符串避免缓存问题
        }

        if (ret.explainInfo.jumpUrl) {
            String acm = DetailContextHolder.get().getParam("acm")
            if (acm) {
                if (ret.explainInfo.jumpUrl.contains("?")) {
                    ret.explainInfo.jumpUrl += "&acm=" + acm
                } else {
                    ret.explainInfo.jumpUrl += "?acm=" + acm
                }
            }
        }



        // 右上角的自营标
        if (Tools.isSelfEmployedItem()) {
            Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
            ret.topRightIcon = maitData?.get("topSelfIcon")
        }
        ret.topRightIcon = ret.topRightIcon ? ret.topRightIcon : ""

        // sku选择
        int type = 0 // 0为颜色，1为图片
        for (SkuData skuData : skuDO?.getSkus()) {
            // 如果有一个sku不是颜色样式的，就都展示默认图
            if (TextUtils.isEmpty(skuData.color)) {
                type = 1
                break
            }
        }
        List<SkuShowData> skuShowDataList = new ArrayList<>()
        Set<Integer> styleIdSet = new HashSet<>()
        for (SkuData skuData : skuDO?.getSkus()) {
            if (styleIdSet.contains(skuData.styleId)) continue

            styleIdSet.add(skuData.styleId)
            skuShowDataList.add(new SkuShowData(
                    skuId: skuData.stockId,
                    img: skuData.img,
                    preview: type == 0 ? skuData.color : skuData.img,
                    text: skuData.style
            ))
        }
        if (styleIdSet.size() > 1) {
            ret.skuSelect = new SKUSelectionBarData(
                    type: type,
                    list: skuShowDataList,
                    desc: type == 0 ? "颜色" : "颜色\n规格",
                    allImg: ret?.topImages?.length > 0 ? ret?.topImages[0] : ""
            )
        } else {
            ret.skuSelect = SKUSelectionBarData.EMPTY
        }

        // 直播进图墙商品，运营配置的主播数据
        if (groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend() && liveSimpleDO?.pickedExplainInfo) {
            ExplainInfo info = liveSimpleDO.pickedExplainInfo
            ret.liveInfo = new LiveInfoVO(
                    actorAvatar: info.avatar,
                    actorName: info.actUserName,
            )
            if (input2?.sales != null) {
                if (input2?.sales > 100000L) {
                    ret.liveInfo.sales = String.format("销量 %.1f万", input2?.sales / 10000.0)
                } else {
                    ret.liveInfo.sales = String.format("销量 %d", input2?.sales)
                }
            }
            if (info.tags && !info.tags.isEmpty()) {
                ret.liveInfo.actorTag = info.tags.get(0).name
            }
            ret.video = new VideoInfoVO(
                    videoId: info.videoId,
                    hideWhenLive: false,
                    autoPlayWhenWifi: true
            )
            if (ret?.topImages?.length > 0) {
                ret.video.cover = ret.topImages[0]
            }
        } else {
            ret.liveInfo = new LiveInfoVO()
        }

        if (ret.video == null) {
            ret.video = VideoInfoVO.EMPTY
        }

        return ret
    }
    
    List processImageAndBorderList(List rawData) {

        List<Map> list = new ArrayList<>()

        if (!rawData) {
            return list
        }

        for (Map imageInfo : rawData) {

            // 图片链接，若为视频则取封面图
            String imageURL = ((boolean) imageInfo.get("isVideo")) ? imageInfo.get("frontImage") : imageInfo.get("image")

            // 处理商品锚点
            List itemAnchors = new ArrayList<>()
            double CDNImageRatio = getCDNImageRatio(imageURL)
            for (Map itemTagInfo : (List<Map>) imageInfo.get("item_borders")) {

                if (((List) itemTagInfo.get('border')).size() < 2) {
                    continue
                }
                
                int x = (int) ((List) itemTagInfo.get('border')).get(0)
                int y = (int) ((List) itemTagInfo.get('border')).get(1)

                Map translated = new HashMap<>()
                safePutMap(translated, "text", "¥" + itemTagInfo.get("price"))
                safePutMap(translated, "link", itemTagInfo.get("link"))
                safePutMap(translated, "itemId", itemTagInfo.get("itemId"))
                safePutMap(translated, "x", x / 750)
                safePutMap(translated, "y", y / (750 * CDNImageRatio))

                itemAnchors.add(translated)
            }

            Map translated = new HashMap<>()
            safePutMap(translated, "image", imageURL ?: "")
            safePutMap(translated, "videoURL", imageInfo.get("videoUrl") ?: "")
            safePutMap(translated, "itemAnchors", itemAnchors)
            
            list.add(translated)
        }
        return list
    }

    void safePutMap(Map map, String key, Object value) {
        if (value != null) {
            map.put(key, value);
        }
    }

    double getCDNImageRatio(String url) {
        if (!url) {
            return 1.0
        }

        Pattern pattern = Pattern.compile("_(\\d+)x(\\d+)\\.")
        Matcher matcher = pattern.matcher(url)

        if (!matcher.find()) {
            return 1.0
        }

        Integer width = matcher.group(1)?.toInteger()
        Integer height = matcher.group(2)?.toInteger()

        if (width && height) {
            return height / (double) width;
        }

        return 1.0
    }
}

