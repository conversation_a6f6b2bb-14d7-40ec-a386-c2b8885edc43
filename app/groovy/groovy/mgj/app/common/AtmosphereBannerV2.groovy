package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.TagUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.ActivityKey
import groovy.mgj.app.common.common.NormalCountdownManager

/**
 * Created by wuyi on 2018/9/10.
 */
@Translator(id = "atmosphereBannerV2", defaultValue = DefaultType.EMPTY_MAP)
class AtmosphereBannerV2 implements ISevenDependTranslator<ActivityDO, GroupbuyingDO,ItemBaseDO,PinTuanDO, NormalCountdownDO,PresaleDO, ExtraDO, AtmosphereBannerVO> {

    static class AtmosphereBannerVO {
        String background
        String link
        String acm

        static getEmpty() {
            return new AtmosphereBannerVO()
        }
    }

    @Override
    AtmosphereBannerVO translate(ActivityDO activityDO, GroupbuyingDO groupbuyingDO, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presaleDO, ExtraDO extraDO) {
        // 如果需要展示前N件立减的氛围，正常的氛围就不展示
        if (AtmosphereComplexBanner.shouldShow(itemBaseDO)) {
                return AtmosphereBannerVO.getEmpty()
        }

        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XSBK)
        CountdownInfo xinpin = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.XINP)
        CountdownInfo shango = normalCountdownDO?.getCountdownInfoMap()?.get(ActivityKey.SHANGO)

        // 通过配置新增的kv标氛围
        CountdownInfo extraPreActivity, extraInActivity
        (extraPreActivity, extraInActivity) = NormalCountdownManager.getActivity(normalCountdownDO)

        def result = new AtmosphereBannerVO()

        // 针对某些商品标的商品，展示专门的氛围，优先于其他所有逻辑
        List<Map<String, Object>> tagMaitDatas = MaitUtil.getMaitData(133471)
        if (tagMaitDatas) {
            for (Map<String, Object> tagMaitData : tagMaitDatas) {
                int tag
                try {
                    tag = Integer.valueOf((String) tagMaitData?.get("tag"))
                } catch (Throwable ignore) {
                    continue
                }
                if (TagUtil.isContainsTag(DetailContextHolder.get()?.getItemDO()?.getItemTags(), tag)) {
                    result.background = tagMaitData.get("activityBanner")
                    result.link = tagMaitData.get("link")
                    result.acm = tagMaitData.get("acm")
                    return result
                }
            }
        }

        // 跟priceBannerV2以及summaryV3里标题前面的icon是配套的，判断条件保持一致

        // 预售
        if (presaleDO) {
            return new AtmosphereBannerVO()
        }
        // 待开售
        else if (itemBaseDO?.state == 3 && extraDO?.onSaleTime) {
            return new AtmosphereBannerVO()
        }
        // 主播推荐
        else if (groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend()) {
            Map<String, Object> maitData = MaitUtil.getMaitData(144975)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 当前是大促(包括品牌日等)正式期并且本商品是活动商品
        else if (activityDO && activityDO?.activityState == 2) {
            result.background = activityDO?.activitySphereImage
            result.link = ""
            return result
        }
        // 限时爆款。限时爆款没有预热，就是正式期
        else if(xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY){
            Map<String, Object> maitData = MaitUtil.getMaitData(xsbk.maitId1110)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 团购正式期
        else if (groupbuyingDO && groupbuyingDO?.status == TuanStatus.IN && groupbuyingDO?.endTime) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 招商非渠道拼团。没有预热期，就是正式期
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 新品正式期
        else if (xinpin && xinpin.startTime <= System.currentTimeSeconds() && xinpin.endTime > System.currentTimeSeconds() && xinpin.state == CountdownState.IN_ACTIVITY) {
            Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 闪购正式期
        else if (shango && shango.startTime <= System.currentTimeSeconds() && shango.endTime > System.currentTimeSeconds() && shango.state == CountdownState.IN_ACTIVITY) {
            Long maitID = shango?.maitId1110
            Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 其他kv标配置的氛围正式期
        else if (extraInActivity) {
            Map<String, Object> maitData = MaitUtil.getMaitData(extraInActivity?.maitId1110)?.get(0)
            result.background = maitData?.get("activityBanner")
            result.acm = maitData?.get("acm")
            result.link = maitData?.get("activityBannerLink") ?: ""
        }
        // 不是正式期，是预热或者普通
        else {
            long lastPreActivityStartTime = Long.MAX_VALUE
            boolean isPre = false

            // 当前是大促(包括品牌日等)预热期并且本商品是活动商品
            if (activityDO && activityDO?.warmUpPrice?.price && activityDO?.activityState == 1
                    && activityDO.startTime < lastPreActivityStartTime) {
                result.background = activityDO?.activitySphereImage
                result.link = ""

                lastPreActivityStartTime = activityDO.startTime
                isPre = true
            }
            // 团购预热期
            if (groupbuyingDO?.status == TuanStatus.PRE && groupbuyingDO?.startTime
                    && groupbuyingDO.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
                result.background = maitData?.get("activityBanner")
                result.acm = maitData?.get("acm")
                result.link = maitData?.get("activityBannerLink") ?: ""

                lastPreActivityStartTime = groupbuyingDO.startTime
                isPre = true
            }
            // 新品预热期，并且比其他预热的开始时间要早
            if (xinpin && xinpin.startTime > System.currentTimeSeconds() && xinpin.state == CountdownState.WARM_UP
                    && xinpin.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
                result.background = maitData?.get("activityBanner")
                result.acm = maitData?.get("acm")
                result.link = maitData?.get("activityBannerLink") ?: ""

                lastPreActivityStartTime = xinpin.startTime
                isPre = true
            }
            // 闪购预热期，并且比其他预热的开始时间要早
            if (shango && shango.startTime > System.currentTimeSeconds() && shango.state == CountdownState.WARM_UP
                    && shango.startTime < lastPreActivityStartTime) {
                Long maitID = shango?.maitId1110
                Map<String, Object> maitData = MaitUtil.getMaitData(maitID)?.get(0)
                result.background = maitData?.get("activityBanner")
                result.acm = maitData?.get("acm")
                result.link = maitData?.get("activityBannerLink") ?: ""

                lastPreActivityStartTime = shango.startTime
                isPre = true
            }
            if (extraPreActivity && extraPreActivity.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(extraPreActivity?.maitId1110)?.get(0)
                result.background = maitData?.get("activityBanner")
                result.acm = maitData?.get("acm")
                result.link = maitData?.get("activityBannerLink") ?: ""

                lastPreActivityStartTime = extraPreActivity.startTime
                isPre = true
            }

            // 普通
            if (!isPre) {
                return new AtmosphereBannerVO()
            }
        }
        return result
    }

}