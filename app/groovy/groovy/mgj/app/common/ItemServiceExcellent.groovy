package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.metabase.spring.client.MetabaseClient
import groovy.mgj.app.vo.Tools
import groovy.mgj.app.common.common.ItemServiceBasicVO
import groovy.mgj.app.common.common.ItemServiceProvider

import javax.annotation.Resource

/**
 * Created by fufeng on 2017/3/27.
 */

@Translator(id = "itemServiceExcellent")
class ItemServiceExcellent implements ITwoDependTranslator<ItemBaseDO, ShopDO, ItemServiceBasicVO> {

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    private ItemServiceProvider itemServiceProvider = new ItemServiceProvider()

    @Override
    ItemServiceBasicVO translate(ItemBaseDO itemBaseDO, ShopDO shopDO) {
        if (DetailContextHolder.get().isDyn()) {
            //业务上可以返回NULL
            return null
        }
        Boolean isExcellent = Tools.isGoodItem(itemBaseDO, shopDO)
        if (!isExcellent) {
            return new ItemServiceBasicVO()
        }

        def vo = itemServiceProvider.getItemServiceBasicVO(itemBaseDO, shopDO, metabaseClient)
        if (vo == null) {
            return new ItemServiceBasicVO()
        }
        vo.icon = ImageUtil.img("/mlcdn/e5265e/170406_5iedlf6d157hh7aedjf424l16ciif_258x210.png")
        return vo
    }
}
