package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.spi.dslutils.Tools

/**
 * Created by fufeng on 2017/4/4.
 */
@Translator(id = "lazyRecommend", defaultValue = DefaultType.NULL)
class LazyRecommend implements IOneDependTranslator<ItemBaseDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO) {
        if (groovy.mgj.app.vo.Tools.isLiveSource() && VersionController.lazyLoadVisibleControledByDataIsNULL()) {
            return new HashMap()
        }

        if (itemBaseDO == null || itemBaseDO?.iid == null) {
            // 业务上可以返回NULL
            return null
        }

        // 1500 起，下面的所有数据，除了 iidE，客户端都已经不用了，但老版本还需要

        Map<String ,String> innerMap = [:]
        innerMap["pid"] = "5002"
        innerMap["iidE"] = itemBaseDO?.iid
        innerMap["pageSize"] = "24"

        def vo = [:]
        vo.type = "MWP"
        vo.info = [:]
        vo.info.api = "mwp.darwin.recommend@4"
        vo.info.param = innerMap
        vo.from = "recommend"
        return vo
    }
}