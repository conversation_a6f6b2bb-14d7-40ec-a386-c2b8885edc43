package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.buyershow.domain.BuyerShowDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.rate.domain.DetailRate
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.rate.domain.RateUserInfo
import com.mogujie.service.rate.domain.tag.RateTag

@Translator(id = "rateNormal", defaultValue = DefaultType.NULL)
class RateNormal implements IThreeDependTranslator<RateDO, ItemBaseDO, BuyerShowDO, Object> {
    static class RateVO {
        String title
        String rateUrl
        Integer imgTotal
        RateEntryVO[] list
        RateTagVO[] rateTags
    }

    static class RateEntryVO {
        Boolean canExplain
        String content
        Long created
        String explain
        List<String> images
        Integer isAnonymous
        Boolean isEmpty
        String level
        String rateId
        String style
        RateUserInfo user
        List<String> extraInfo

        RateEntryVO() {
        }

        RateEntryVO(DetailRate rate) {
            this.canExplain = rate.canExplain
            this.content = rate.content
            this.created = rate.created
            this.explain = rate.explain
            this.images = rate.images
            this.isAnonymous = rate.isAnonymous
            this.isEmpty = rate.isEmpty
            this.level = rate.level
            this.rateId = rate.rateId
            this.style = rate.style
            this.user = rate.user
            this.extraInfo = rate.extraInfo
        }
    }

    static class RateUserVO {
        String avatar
        String profileUrl
        String tagIndex
        String uid
        String uname
    }

    static class RateTagVO {
        String emotion
        Integer num
        String property
        String value
        String link

        RateTagVO(RateTag tag, String iid) {
            if (!tag) {
                return
            }
            this.emotion = tag.emotion
            this.num = tag.num
            this.property = tag.property
            this.value = tag.value

//            InvokerHelper.setProperties(this, tag.properties)
            link = "mgj://ratelist?iid=$iid&property=$property&emotion=$emotion"
        }
    }

    @Override
    Object translate(RateDO input1, ItemBaseDO input2, BuyerShowDO input3) {

        if (input1?.rateTags && input1.rateTags.size() > 0 && input1.imgTotal && input1.imgTotal > 0) {
            RateTag imgTag = new RateTag();
            imgTag.setNum(input1.imgTotal);
            imgTag.setEmotion("positive");
            imgTag.setProperty("图片");
            imgTag.setValue("有图片");
            input1.rateTags.add(0, imgTag);
        }

        new RateVO(
                title: "买家评价 (${input1?.CRate})",
                rateUrl: "mgj://ratelist?iid=${input2?.iid}",
                list: input1?.list?.collect {
                    def rateEntry = new RateEntryVO(it)
//                    InvokerHelper.setProperties(rateEntry, it.properties)
                    return rateEntry
                }?.findAll {
                    it != null
                },
                rateTags: input1?.rateTags ? input1.rateTags.collect {
                    new RateTagVO(it, input2?.iid)
                } : null
        )
    }
}
