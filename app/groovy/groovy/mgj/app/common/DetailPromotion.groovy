package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.coupon.domain.CouponConfigType
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.mycoupon.domain.MyCouponDO
import com.mogujie.detail.module.coupon.domain.PlatformCouponV2
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.mycoupon.domain.MyMoDouCoupon
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.commons.utils.IdConvertor
import org.apache.commons.lang3.StringUtils
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.spi.dslutils.Tools

import java.text.SimpleDateFormat

/*
 * 1450 及之前版本所使用，1460 起用 DetailPromotionV2
 */

@Translator(id = "promotion", defaultValue = DefaultType.NULL)
class DetailPromotion implements ISixDependTranslator<ItemBaseDO,SkuDO,CouponDO,PinTuanDO,MyCouponDO,LiveDO, Object> {

    class DetailModouCouponVO {
        String effect
        String expiryInfo
        String limitDesc
        String campId
        Integer requireModou
        Integer vipLevel
        // 0 可兑换 1 已兑换 2 已抢完
        Integer status

        // 满xx减去xx，在外面预览时使用
        String preview

        // 分为单位
        int cutCent
        int limitCent
        DetailModouCouponVO(int vipLevel, MyMoDouCoupon modouCoupon) {
            if (modouCoupon == null) return
            cutCent = modouCoupon.cutCent?.intValue() ?: 0
            limitCent = modouCoupon.limitCent?.intValue() ?: 0
            // 无门槛券展示比优惠金额多0.01
            limitCent = Math.max(limitCent, cutCent + 1)
            if (modouCoupon != null) {
                effect = "${NumUtil.formatPriceDrawer(cutCent)}"
                if (modouCoupon.et != null && modouCoupon.et > 0) {
                    int secondsOfDay = 24 * 60 * 60
                    expiryInfo = "领取后${modouCoupon.et / secondsOfDay}天内有效"
                } else {
                    expiryInfo = formatTime(modouCoupon.getStartTime(), modouCoupon.getEndTime())
                }
                limitDesc = "满${NumUtil.formatPriceDrawer(limitCent)}元可用"
                requireModou = modouCoupon.costNum
                campId = modouCoupon.getPkgId()
                this.vipLevel = vipLevel
                preview = "满${NumUtil.formatPriceDrawer(limitCent)}减${NumUtil.formatPriceDrawer(cutCent)}"
                if (modouCoupon.getStatus() != null) {
                    // status要跟code对应下
                    switch (modouCoupon.getStatus().code) {
                        case 1:
                            status = 0
                            break
                        case 2000:
                        case 2001:
                            status = 1
                            break
                        case 2002:
                            status = 2
                            break
                        default:
                            status = 0
                    }
                }
            }
        }
    }

    private String formatTime(long startTime, long endTime) {
        Date startDate = new Date(startTime * 1000L)
        Date endDate = new Date(endTime * 1000L)
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm")
        return "${formatter.format(startDate)}-${formatter.format(endDate)}"
    }

    class DetailPlatformCouponVO {
        String effect
        String limitDesc
        String expiryInfo
        String couponDesc
        String bgImage
        String campId
        String tagImage
        Boolean isAlreadGet

        DetailPlatformCouponVO(PlatformCouponV2 platformCoupon, String bgImage, String tagImage) {
            if (!platformCoupon) {
                return
            }

            // 2020-09-28 @yanze
            // 为了不发版支持新人券业务，借用平台券列表来展示新人券
            // 因 UI 细节与平台券不同，根据 PlatformCouponV2 中给到的是否为新人券标记，单独走一套逻辑返回新人券数据
            // TODO: 强行复用平台券很不合理，将来一定要剥离出去
            if (platformCoupon.forNewUser) {

                this.campId = ""

                // 写死的新人券背景图
                this.bgImage = "https://s10.mogucdn.com/mlcdn/c45406/200928_1g7ig8f9jfelh34kh1f8a87fgdiak_1035x225.png"
                // 券面额
                this.effect = "¥" + NumUtil.formatPriceDrawer((int) platformCoupon.cutPrice)
                // 展示在券面额右侧的立减文案，展示位置和平台券不一样所以此处借用 couponDesc 字段
                this.couponDesc = "满" + NumUtil.formatPriceDrawer((int) platformCoupon.limitPrice) + "元立减"
                // 下方文案，借用 expiryInfo 字段
                this.expiryInfo = "App和小程序均可使用"
                // 此券不用领，后端直接写死 true 的
                this.isAlreadGet = platformCoupon.hasReceived

                return
            }
            // =============================================

            // 这下面就是正常平台券的逻辑了

            if (platformCoupon.discount != null && platformCoupon.discount > 0) {
                this.effect = NumUtil.formatPriceDrawer((int) platformCoupon.discount) + "折"
                this.limitDesc = "·折扣券，"+this.effect+"最高抵扣" + NumUtil.formatPriceDrawer((int) platformCoupon.maxDecrease) + "元"

            } else {
                this.effect = "¥" + NumUtil.formatPriceDrawer((int) platformCoupon.cutPrice)
                this.limitDesc = "·满" + NumUtil.formatPriceDrawer((int) platformCoupon.limitPrice) + "元立减"
            }
            this.couponDesc = platformCoupon.terminal
            this.campId = platformCoupon.couponId ? IdConvertor.idToUrl(platformCoupon.couponId) : null
            this.bgImage = bgImage
            this.tagImage = tagImage
            this.isAlreadGet = platformCoupon.hasReceived

            if (platformCoupon.startTime == null || platformCoupon.endTime == null) {
                this.expiryInfo = ""
            } else {
                Date startDate = new Date(((long)platformCoupon.startTime) * 1000)
                String startDateStr = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(startDate)

                Date endDate = new Date(((long)platformCoupon.endTime) * 1000)
                String endDateStr = new SimpleDateFormat("yyyy.MM.dd HH:mm").format(endDate)

                this.expiryInfo = "·" + startDateStr + "-" + endDateStr
            }
        }
    }

    class DetailPromotionBonusVO{
        String title
        String desc1
        String desc2
        Double count
        String countDesc
        String bgImage
        String getStr
        String activityCode
        String canGetStr //"立即领取"
        String useUpStr //"去逛逛"
        String useUpLink //"mgj://wallxxxx"

        DetailPromotionBonusVO() {
        }

        DetailPromotionBonusVO(ItemBaseDO itemBaseDO, CouponDO couponDO,MyCouponDO myCouponDO){
            if (!couponDO.crossShopDiscount) {
                // 业务上可以返回NULL
                return
            }

            this.title = couponDO?.crossShopDiscount?.name
            String limitPrice = NumUtil.formatPriceDrawer((int) couponDO?.crossShopDiscount?.limitPrice)
            String cutPrice = NumUtil.formatPriceDrawer((int) couponDO?.crossShopDiscount?.cutPrice)
            String upperLimit = ""
            if(couponDO?.crossShopDiscount?.upperLimit && couponDO?.crossShopDiscount?.upperLimit < 1000000) {
                upperLimit = "，上限" +  NumUtil.formatPriceDrawer((int) couponDO?.crossShopDiscount?.upperLimit)
            }
            this.desc1 = "·每满" + limitPrice + "减" + cutPrice + upperLimit

            Date startDate = new Date(((long)couponDO?.crossShopDiscount?.startTime) * 1000)
            String startDateStr = new SimpleDateFormat("M.d").format(startDate)

            Date endDate = new Date(((long)couponDO?.crossShopDiscount?.endTime) * 1000)
            String endDateStr = new SimpleDateFormat("M.d").format(endDate)

            String time
            if(startDateStr == endDateStr){
                time = "·仅限" + startDateStr + "当日"
            }
            else {
                time = "·" + startDateStr + "-" + endDateStr
            }
            String teminal = couponDO?.crossShopDiscount?.terminal ?  "，"+couponDO?.crossShopDiscount?.terminal : ""
            String crossShop = "，可跨店使用"
            this.desc2 = time + teminal + crossShop
            this.countDesc = "我的购物金："
            if(myCouponDO?.bonusCount && myCouponDO?.bonusCount > 0){
                this.count =  myCouponDO?.bonusCount/100.0
            }
            else {
                this.count = 0
            }
            this.getStr = couponDO?.crossShopDiscountBtnText
            Map<String, Object> maitData = MaitUtil.getMaitData(134291)?.get(0)
            this.activityCode = maitData?.get("activityCode")
            this.bgImage = couponDO.crossShopDiscountBgImg

            this.canGetStr = maitData?.get("canGetStr") ?: "立即领取"
            this.useUpStr = maitData?.get("useUpStr") ?: "去逛逛"
            String tuqiangTitle = "每满" + limitPrice + "减" + cutPrice + "，可跨店使用"
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm")
            String startTime = formatter.format(new Date(couponDO?.crossShopDiscount?.startTime * 1000L))
            String endTime = formatter.format(new Date(couponDO?.crossShopDiscount?.endTime * 1000L))
            String validTime = "使用时间：" + startTime + "-" + endTime
            String state = System.currentTimeSeconds() >= couponDO?.crossShopDiscount?.startTime ? "0" : "1" // 0正式 1预热
            this.useUpLink = "mgj://coudanwaterfall?promotionCode=${couponDO?.crossShopDiscount?.promotionCode}&promotionId=${couponDO?.crossShopDiscount?.promotionId}&tip1=${tuqiangTitle}&wallTopDecorateTitle=${tuqiangTitle}&wallTopDecorateSubTitle=${validTime}&primeStatus=${state}"
            this.useUpLink += "&coudanStatus=${(state == "0") ? 1 : 0}" // 0预热 1正式，展示计价条
            this.useUpLink += "&_nchoice=1&fId=${itemBaseDO?.iid}"

            // 1200开始背景图跟老版本区分
            if (groovy.mgj.app.vo.Tools.getAppVersion() >= 1200) {
                this.bgImage = maitData?.get("crossShopDiscountBgImg1200")
            }
        }
    }


    class DetailPopoverVO {
        String couponPkgTitle
        String couponPkgImg
        String relationKey
        String couponListTitle
        List<DetailPlatformCouponVO> couponList
        DetailPromotionBonusVO promotionBonus
        List<DetailModouCouponVO> modouCouponList
        // 用户拥有的蘑豆数
        Long modouAmount
        // 蘑豆兑券标题
        String modouCouponTitle

        DetailPopoverVO(){

        }
        DetailPopoverVO(ItemBaseDO itemBaseDO, CouponDO couponDO, MyCouponDO myCouponDO) {
            if (!couponDO) {
                // 业务上可以返回NULL
                return
            }

            int appVersion = groovy.mgj.app.vo.Tools.getAppVersion()

            if (couponDO.couponConfigType == CouponConfigType.PACKAGE) {
                this.couponPkgTitle = couponDO.couponPkgTitle
                this.couponPkgImg = couponDO.couponPkgImg
                this.relationKey = couponDO.relationKey
            } else {
                this.couponList = new ArrayList<>()
                for (int i = 0; i < couponDO.platformCouponV2?.size(); i++) {
                    DetailPlatformCouponVO coupon = new DetailPlatformCouponVO(couponDO.platformCouponV2[i], couponDO.couponBgImg, couponDO.couponTagImg)
                    this.couponList.add(coupon)
                }
                if(couponDO.platformCouponV2?.size()>0){
                    this.couponListTitle = couponDO.couponPkgTitle
                }
            }
            if(couponDO.crossShopDiscount && Tools.isBonusItem() && appVersion >= 1170){
                this.promotionBonus = new DetailPromotionBonusVO(itemBaseDO, couponDO, myCouponDO)
                this.couponListTitle = couponDO.couponPkgTitle
            }
            else {
                this.promotionBonus =  new DetailPromotionBonusVO()
            }

            dynModouProcess(myCouponDO)
        }

        void dynModouProcess(MyCouponDO myCouponDO) {
            modouAmount = myCouponDO?.balanceModouAmount
            modouCouponTitle = "蘑豆专享券"
            modouCouponList = myCouponDO?.modouCoupons?.collect {
                return new DetailModouCouponVO(myCouponDO?.userVipLevel ?: 0, it)
            }
        }
    }

    class DetailPlatformVO {

        // 1450 新增，展示在详情页上的券预览相关 UI 样式可配（为了新人券的特殊样式）
        String pickBgImg
        String pickTextColor
        String couponBgImg
        String couponTextColor

        String title
        List<String> couponList
        DetailPopoverVO popover

        // 因为蘑豆专享券是实时的，要在没有其他优惠时才展示，所以要跟购物金、平台券这些走缓存的区分
        String secondaryTitle
        List<String> secondaryCouponList

        DetailPlatformVO() {
        }

        DetailPlatformVO(ItemBaseDO itemBaseDO, CouponDO couponDO,MyCouponDO myCouponDO) {
            if (!couponDO) {
                return
            }

            this.popover = new DetailPopoverVO(itemBaseDO, couponDO,myCouponDO)
            this.couponList = new ArrayList<>()

            int appVersion = groovy.mgj.app.vo.Tools.getAppVersion()

            if(couponDO?.crossShopDiscount && appVersion >= 1170){
                this.title = "跨店满减"
                String limitPrice = NumUtil.formatPriceDrawer((int) couponDO.crossShopDiscount?.limitPrice)
                String cutPrice = NumUtil.formatPriceDrawer((int) couponDO.crossShopDiscount?.cutPrice)
                String merged = "领购物金|每满" + limitPrice + "减" + cutPrice
                couponList.add(merged)
            }
            else {
                this.title = couponDO.couponPkgTitle

                if (couponDO.platformCouponV2?.forNewUser) {
                    // 新人券的特殊样式
                    this.pickBgImg = "https://s10.mogucdn.com/mlcdn/c45406/201012_0ecgdj5g7b0j1bdihcia4hg9c8c0g_195x53.png"
                    this.pickTextColor = "#FFFFFF"
                    this.couponBgImg = "https://s10.mogucdn.com/mlcdn/c45406/201012_2kg2b2e88574i3igj0dh08c3k4gg2_203x53.png"
                    this.couponTextColor = "#F5515F"
                }

                for (int i = 0; i < couponDO.platformCouponV2?.size(); i++) {
                    if (i >= 2) {
                        break
                    }

                    String merged
                    if (couponDO.platformCouponV2[i]?.discount != null && couponDO.platformCouponV2[i]?.discount > 0) {
                        merged = NumUtil.formatPriceDrawer((int) couponDO.platformCouponV2[i]?.discount) + "折券"
                    } else {
                        String limitPrice = NumUtil.formatPriceDrawer((int) couponDO.platformCouponV2[i]?.limitPrice)
                        String cutPrice = NumUtil.formatPriceDrawer((int) couponDO.platformCouponV2[i]?.cutPrice)
                        merged = "满 " + limitPrice + "减 " + cutPrice
                    }
                    couponList.add(merged)
                }
            }
        }
    }

    class DetailInstallment {
        String promotionIconText
        String couponInfo

        DetailInstallment(SkuDO skuDO) {
            if (!skuDO?.freePhases) {
                return
            }
            this.promotionIconText = "免手续费"
            this.couponInfo = "最高享" + skuDO?.freePhases?.toString() + "期免手续费"

        }
    }

    class DetailEventVO {
        String sellerId
        String iid
        Map<String, Object> request
        DetailInstallment installment
        DetailPlatformVO platform
        Boolean showShopPromotion = true
    }

    boolean isPreSale(ItemBaseDO itemBaseDO) {
        //4状态预售
        return (itemBaseDO?.saleType == 1)
    }

    @Override
    Object translate(ItemBaseDO input1, SkuDO skuDO, CouponDO couponDO,PinTuanDO pintuanDO,MyCouponDO myCouponDO,LiveDO liveDO) {
        if (!input1) {
            // 业务上可以返回NULL
            return null
        }

        Map<String, Object> request = new HashMap<>()
        request.put("sellerId", input1.userId)
        request.put("itemIds", input1.iid)

        Integer itemPrice
        if (Tools.isPintuan(input1, pintuanDO)) {
            itemPrice = (pintuanDO.skuInfo.lowNowPrice?.toBigDecimal() * 100).toInteger()
        } else {
            itemPrice = (input1.lowNowPrice?.toBigDecimal() * 100).toInteger()
        }
        request.put("itemPrice", itemPrice)
        request.put("preSale", isPreSale(input1))


        DetailEventVO ret = new DetailEventVO(
                sellerId: input1.userId,

                iid: input1.iid,

                request: request,

                installment: new DetailInstallment(skuDO),

                showShopPromotion: true

        )

        if (groovy.mgj.app.vo.Tools.isLiveSource()) {
            ret.showShopPromotion = false
        }

        String av = DetailContextHolder.get().getParam("_av");
        av = StringUtils.isEmpty(av) ? "0" : av
        av = av.isNumber() ? av : "0"
        Integer avInt = av.toInteger()
        //1040 之后才有平台券列表，1040之前只有券包才展示
        if(couponDO && (avInt >= 1140 || couponDO?.couponConfigType == CouponConfigType.PACKAGE)) {
            ret.platform = new DetailPlatformVO(input1, couponDO,myCouponDO)
        }
        else {
            //couponDO有缓存，myCouponDO不取缓存，在没有返回couponDO的时候需要更新myCouponDO
            ret.platform = new DetailPlatformVO()
            ret.platform.popover = new DetailPopoverVO()
            ret.platform.title = ""
            ret.platform.couponList = new ArrayList<>()
            ret.platform.popover.couponList = new ArrayList<>()
            ret.platform.popover.couponListTitle = ""

            // 蘑豆相关的要处理成不用缓存
            ret.platform.popover.dynModouProcess(myCouponDO)
            ret.platform.popover.promotionBonus = new DetailPromotionBonusVO()
            if (myCouponDO?.bonusCount && myCouponDO?.bonusCount > 0) {
                ret.platform.popover.promotionBonus.count = myCouponDO?.bonusCount / 100.0
            } else {
                ret.platform.popover.promotionBonus.count = 0
            }
            // 1200开始背景图跟老版本区分
            if (groovy.mgj.app.vo.Tools.getAppVersion() >= 1200) {
                Map<String, Object> maitData = MaitUtil.getMaitData(134291)?.get(0)
                ret.platform.popover.promotionBonus.bgImage = maitData?.get("crossShopDiscountBgImg1200")
            } else {
                Map<String, Object> maitData = MaitUtil.getMaitData(134291)?.get(0)
                ret.platform.popover.promotionBonus.bgImage = maitData?.get("crossShopDiscountBgImg")
            }
        }

        List<DetailModouCouponVO> list = ret.platform.popover.modouCouponList
        if (list != null && !list.isEmpty()) {
            ret.platform.secondaryTitle = ret.platform.popover.modouCouponTitle
            ret.platform.secondaryCouponList = list.take(2).collect { it.preview }
        }

        if (ret.platform.secondaryCouponList == null) {
            ret.platform.secondaryCouponList == new ArrayList()
        }
        if (ret.platform.secondaryTitle == null) {
            ret.platform.secondaryTitle = ""
        }
        if (ret.platform.popover?.modouCouponList == null) {
            ret.platform.popover?.modouCouponList = new ArrayList<>()
        }

        return ret
    }
}

