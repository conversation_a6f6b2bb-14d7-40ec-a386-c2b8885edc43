package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.myCouponBubble.domain.MyCouponBubbleDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.metabase.spring.client.MetabaseClient
import com.mogujie.metabase.utils.StringUtils
import groovy.mgj.app.common.common.BottomBarUtil
import groovy.mgj.app.common.common.ShareIntegralProvider
import groovy.mgj.app.vo.*
import org.apache.http.util.TextUtils

import javax.annotation.Resource

import static groovy.mgj.app.vo.NormalBottombarVO.*

@Translator(id = "bottomBarNormalV4")
class BottomBarNormalV4 implements INineDependTranslator<ItemBaseDO, ShopDO, ExtraDO, LiveSimpleDO, PinTuanDO, SkuDO, LiveDO, PresaleDO, MyCouponBubbleDO, Object> {
    // 商品状态
    public static final int STATUS_INVALID = -1 //非正常情况
    public static final int STATUS_NORMAL = 0  // 正常
    public static final int STATUS_OUT_OF_SHELF = 1 // 下架
    public static final int STATUS_SOLD_OUT = 2 // 卖完
    public static final int STATUS_WAIT_SALE = 3  // 待开售

    // 销售类型
    public static final int SALE_TYPE_NORMAL = 0// 正常销售
    public static final int SALE_TYPE_PRESALE = 1// 预售

    public static final int BOTTOMBAR_STATUS_INVALID = -1 //非正常情况
    public static final int BOTTOMBAR_STATUS_NORMAL = 0  // 正常
    public static final int BOTTOMBAR_STATUS_OUT_OF_SHELF = 1 // 下架
    public static final int BOTTOMBAR_STATUS_SOLD_OUT = 2 // 卖完
    public static final int BOTTOMBAR_STATUS_WAIT_SALE = 3  // 待开售
    public static final int BOTTOMBAR_STATUS_PRESALE = 4  // 预售
    public static final int BOTTOMBAR_STATUS_PINTUAN = 5  // 拼团

    public static final long IMTIP_MAIT_ID = 138669L

    /**
     * metabase配置client
     * 目前获取虚拟商品开关
     */
    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient

    @Override
    Object translate(ItemBaseDO itemBaseDO, ShopDO shopDO, ExtraDO extraDO, LiveSimpleDO liveSimpleDO, PinTuanDO pintuanDO, SkuDO skuDO, LiveDO theLiveDO, PresaleDO presaleDO, MyCouponBubbleDO myCouponBubbleDO) {
        NormalBottombarVO VO = new NormalBottombarVO(
                shopId: itemBaseDO?.shopId,
                isFaved: itemBaseDO?.isFaved,
                iid: itemBaseDO?.iid,
                addCartTips: itemBaseDO?.addCartTips,
                addCartSkuCommunicationType: SKUDataVOCommunicationType.PINTUAN,
        )
        String buyButtonText = "立即购买"
        boolean didShowPromotionBuyPrice = false
        // 如果有促销计算出的优惠后价格，购买按钮要展示「领券购买xxx」
        if (Switcher.showBuyPromotion()
                && skuDO?.promotionPrice != null
                && skuDO.promotionPrice != DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal
                && !(groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend())) {
            if (DetailContextHolder.get()?.getItemDO()?.lowNowPriceVal != DetailContextHolder.get()?.getItemDO()?.highNowPriceVal) {
                buyButtonText = "领券购买\n¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}起"
            } else {
                buyButtonText = "领券购买\n¥${NumUtil.formatPriceDrawer(skuDO.promotionPrice.intValue())}"
            }
            didShowPromotionBuyPrice = true
        }
        VO.buyButtonText = buyButtonText
        try {
            /**
             * 店铺来源 - shopFrom
             *
             * 0, 普通商详页 - 店铺入口
             * 1, 主播来源商详页 - 供应商店铺入口
             * 2, 普通商详页 - 底部店铺入口 √
             *
             * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
             */
            VO.shopUrl = itemBaseDO?.shopId ?
                    groovy.mgj.app.vo.Tools.appendRequestAcm("mgj://shop?shopFrom=2&shopId=${itemBaseDO?.shopId}")
                    : null
            /**
             * 买手店来源 - buyerShopFrom
             *
             * 0, 商详页底部入口 √
             * 1, 主播 DSR 入口
             *
             * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
             */
            String detailChildCid = DetailContextHolder.get()?.itemDO?.getCategoryId()?.toString() ?: ""
            if (groovy.mgj.app.vo.Tools.getAppVersion() < 1540) {
                //1540之前，跳转买手店落地页
                VO.msdUrl = liveSimpleDO?.actUserInfo?.userId ?
                        groovy.mgj.app.vo.Tools.appendRequestAcm("mgj://buyershop?buyerShopFrom=0&uid=${IdConvertor.idToUrl(liveSimpleDO?.actUserInfo?.userId)}&detailChildCid=${detailChildCid}")
                        : null
            }
            else {
                //1540以及之后，跳转主播个人主页
                VO.msdUrl = liveSimpleDO?.actUserInfo?.userId ?
                        groovy.mgj.app.vo.Tools.appendRequestAcm("mgj://user?uid=${IdConvertor.idToUrl(liveSimpleDO?.actUserInfo?.userId)}")
                        : null
            }
            VO.saleStartTime = extraDO?.onSaleTime ? extraDO?.onSaleTime : 0
            VO.imUrl = (itemBaseDO?.shopId && itemBaseDO?.iid && itemBaseDO?.userId) ? "mgjim://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&login=1" : null

            if (!TextUtils.isEmpty(VO.imUrl) && !VO.imUrl.contains("channelId")) {
                if ("live".equals(DetailContextHolder.get().getParam("from"))) {
                    VO.imUrl += "&channelId=channel_from_live"
                }
            }
            VO.itemURL = groovy.mgj.app.vo.Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid))
        } catch (Throwable ignore) {
        }
        try {
            VO.normalBuy = new BottomBarPintuanButtonVO(
                    price: "¥${itemBaseDO?.lowNowPrice}",
                    text: "单独购买",
                    skuCommunicationType: SKUDataVOCommunicationType.DEFAULT
            )
        } catch (Throwable ignore) {
        }

        try {
            VO.pintuanBuy = new BottomBarPintuanButtonVO(
                    price: "¥${pintuanDO?.skuInfo?.lowNowPrice}",
                    text: "${pintuanDO?.tuanNum}人拼团",
                    skuCommunicationType: SKUDataVOCommunicationType.PINTUAN
            )
        } catch (Throwable ignore) {
        }

        try {
            VO.shareIntegralOpen = ShareIntegralProvider.canGainIntegral(itemBaseDO)
        } catch (Throwable throwable) {
            VO.shareIntegralOpen = false
        }

        //待开售商品
        if (itemBaseDO?.state == STATUS_WAIT_SALE && extraDO?.onSaleTime) {
            VO.waitForSaleNoticeTitle = "开售提醒：${itemBaseDO?.title}"
            VO.waitForSaleNoticeContent = "【蘑菇街开售提醒】：${itemBaseDO?.title} ${VO.itemURL}"
        }

        // 虚拟商品
        boolean isVirtualItem = (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL)
        VO.buyBaseUrl = BottomBarUtil.getBuyBaseUrl(itemBaseDO)
        VO.normalBuyParams = BottomBarUtil.getBuyParamsString(skuDO, itemBaseDO, null)
        VO.pintuanBuyParams = BottomBarUtil.getBuyParamsString(skuDO, itemBaseDO, pintuanDO)
        VO.ptpPlaceHolder = BottomBarUtil.getPtpPlaceHolder(itemBaseDO)
        VO.liveParamsPlaceHolder = BottomBarUtil.getLiveParamsPlaceHolder(itemBaseDO)
        VO.fashionPlaceHolder = BottomBarUtil.getFashionParamsPlaceHolder(itemBaseDO)
        // 虚拟商品是否跳转到Native下单，先搞一个meatabase，防止有问题
        boolean shouldJumpToNativeVirtualOrder = true
        try {
            shouldJumpToNativeVirtualOrder = metabaseClient.getBoolean("dsl_shouldJumpToNativeVirtualOrder")
        } catch (Exception e) {
            shouldJumpToNativeVirtualOrder = true
        }
        if (shouldJumpToNativeVirtualOrder) {
            VO.buyBaseUrl = ""
        }

        boolean isPintuan = Tools.isPintuan(itemBaseDO, pintuanDO)
        boolean isPresale = isPreSale(itemBaseDO)

        // 处理addCartTips
        VO.addCartTips = VO.addCartTips && itemBaseDO?.state == STATUS_NORMAL && itemBaseDO?.saleType == SALE_TYPE_NORMAL && !isVirtualItem

        /**
         * 下面开始处理布局样式
         */
        int bottombarStatus
        if (isPintuan) {
            bottombarStatus = BOTTOMBAR_STATUS_PINTUAN
        } else if (isPresale) {
            bottombarStatus = BOTTOMBAR_STATUS_PRESALE
        } else {
            bottombarStatus = itemBaseDO?.state
        }

        /**
         * 左边的ButtonList
         */
        def leftTypeList = [LEFT_SHOP, LEFT_FAVED]

        // 有主播信息，需要把店铺入口替换为新版买手店入口（1480）
        if (liveSimpleDO?.actUserInfo && groovy.mgj.app.vo.Tools.getAppVersion() >= 1480) {
            leftTypeList = leftTypeList.collect {
                if (it == LEFT_SHOP) {
                    return LEFT_BUYER_SHOP
                }
                return it
            }
        }

        // 拼团商品，需要在左边可以加入购物车
        if (bottombarStatus == BOTTOMBAR_STATUS_PINTUAN) {
            leftTypeList << LEFT_ADDCART
        }
        // 虚拟、医美商品、优惠券商品需要去掉加入购物车
        if (isVirtualItem || Tools.isMedicalBeautyItem() || Tools.isVirtualCouponItem()) {
            leftTypeList = leftTypeList.findAll {
                it != LEFT_ADDCART
            }
        }
        // 如果最后左边的按钮数量少于3个，那么把私聊添加到第二个位置
        if (leftTypeList.size() < 3) {
            leftTypeList.add(leftTypeList.size() > 1 ? 1 : 0, LEFT_IM)
        }
        VO.leftButtonList = composeLeftList(leftTypeList)

        // 好店优选屏蔽店铺入口
        if (groovy.mgj.app.vo.Tools.isHaoDianYouXuan(shopDO)) {
            VO.leftButtonList = VO.leftButtonList.findAll {
                it.type != LEFT_SHOP
            }
        }

        // sourParams为live，也就是主播推荐链路进来的，屏蔽店铺入口，并在imurl上添加channelId
        if (groovy.mgj.app.vo.Tools.isLiveSource()) {
            VO.leftButtonList = VO.leftButtonList.findAll {
                it.type != LEFT_SHOP
            }
            if (VO.imUrl) {
                VO.imUrl += "&channelId=channel_live_huayra"
            }
        }

        /**
         * 右边的ButtonList
         */
        List<BottomBarItemData> rightButtonList = new ArrayList<>()
        switch (bottombarStatus) {
            case BOTTOMBAR_STATUS_PINTUAN:
                // 拼团一定是能正常购买的非预售商品
                rightButtonList = composeRightList([RIGHT_NORMAL_BUY, RIGHT_PINTUAN_BUY])
                break
            case BOTTOMBAR_STATUS_NORMAL:
                BottomBarItemData rightBuy = new BottomBarItemData(type: RIGHT_BUY)
                if (didShowPromotionBuyPrice) {
                    // 展示了领券购买的打点
                    rightBuy.eventId = "016001343"
                }
                rightButtonList = composeRightList([RIGHT_NORMAL_ADDCART, rightBuy])
                break
            case BOTTOMBAR_STATUS_WAIT_SALE:
                rightButtonList = composeRightList([RIGHT_NORMAL_ADDCART, RIGHT_WAIT_FOR_SALE])
                break
            case BOTTOMBAR_STATUS_OUT_OF_SHELF:
            case BOTTOMBAR_STATUS_SOLD_OUT:
                // 下架 & 卖光都用这个文案
                rightButtonList = composeRightList([new BottomBarItemData(
                        type: RIGHT_NORMAL_ADDCART,
                        disable: true
                ), new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "卖光啦",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
            case BOTTOMBAR_STATUS_PRESALE:
                BottomBarItemData presale = new BottomBarItemData(
                        type: RIGHT_PRE_SALE,
                )
                if (!TextUtils.isEmpty(presaleDO?.expandMoney)) {
                    presale.title = String.format("立即付定金\n定金 %s 抵 %s", presaleDO.deposit, presaleDO.expandMoney)
                } else {
                    presale.title = "付定金"
                }
                rightButtonList = composeRightList([presale])
                break
            default:
                break
        }
        // 如果是虚拟、医美商品、优惠券商品，不展示加购按钮
        if (isVirtualItem || Tools.isMedicalBeautyItem() || Tools.isVirtualCouponItem()) {
            rightButtonList = rightButtonList.findAll {
                it.type != RIGHT_NORMAL_ADDCART
            }
        }
        VO.rightButtonList = rightButtonList

        /**
         * Float
         */
        List<BottomBarItemData> floatButtonList = new ArrayList<>()

        if (!leftTypeList.contains(LEFT_IM)) {
            // 左边没有IM按钮
            String iconUrl = MetabaseUtil.get("imIcon")
            if (StringUtils.isBlank(iconUrl)) {
                iconUrl = "/mlcdn/c45406/180403_372af4gfg40i29708jh5a23a0191a_116x116.png"
            }
            String imIcon = ImageUtil.img(iconUrl)
            floatButtonList.add(new BottomBarItemData(
                    type: FLOAT_IM,
                    icon: imIcon))
        }

        String icon = ShareIntegralProvider.getShareIconUrl(itemBaseDO)
        if (!TextUtils.isEmpty(icon)) {
            BottomBarItemData shareItemData = new BottomBarItemData(
                    type: FLOAT_SHARE,
                    icon: icon)
            floatButtonList.add(shareItemData)
        }

        // 如果是虚拟商品，不展示私聊按钮  暂时不需要这个逻辑
//        if (isVirtualItem) {
//            floatButtonList = floatButtonList.findAll{
//                it.type != FLOAT_IM
//            }
//        }
        VO.floatButtonList = floatButtonList

        boolean needImTip = VO.leftButtonList.any { item ->
            LEFT_IM == item.type
        }
        if (needImTip && shopDO?.imTips) {
            Map<String, Object> maitData = MaitUtil.getMaitData(IMTIP_MAIT_ID)?.get(0)
            VO.imTips = new IMTips(
                    canShow: shopDO?.imTips,
                    imgUrl: maitData?.get("imgUrl"),
                    imgUrl_old: maitData?.get("imgUrl_old"),
                    timeThreshold: groovy.mgj.app.vo.Tools.parseInt(maitData?.get("timeThreshold")?.toString(), 0),
                    goodsThreshold: groovy.mgj.app.vo.Tools.parseInt(maitData?.get("goodsThreshold")?.toString(), 0),
            )
        } else {
            VO.imTips = IMTips.EMPTY
        }
        VO.IMText = ["客服", "客服"] // 为了安卓不越界。。
        if (myCouponBubbleDO) {
            VO.couponBubble = new CouponBubbleVO(
                    dubbleBgImg: myCouponBubbleDO.dubbleBgImg,
                    couponId: myCouponBubbleDO.couponId,
                    couponDesc: myCouponBubbleDO.couponDesc,
                    endTime: myCouponBubbleDO.endTime,
                    type: myCouponBubbleDO.type,
                    jumpUrl: myCouponBubbleDO.jumpUrl
            )
        } else {
            VO.couponBubble = CouponBubbleVO.EMPTY
        }

        return VO
    }

    boolean isPreSale(ItemBaseDO itemBaseDO) {
        return (itemBaseDO?.saleType == SALE_TYPE_PRESALE
                && itemBaseDO?.state == STATUS_NORMAL)
    }
}
