package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import groovy.mgj.app.vo.CountdownVO
import groovy.mgj.app.vo.TextStyle

@Translator(id = "countdown")
class CountDownNormal implements ITwoDependTranslator<ActivityDO, GroupbuyingDO, Object> {
    @Override
    CountdownVO translate(ActivityDO input1, GroupbuyingDO input2) {
        //现在有两种倒计时banner，大促和团购，同一个页面只显示一个倒计时
        //优先级：大促>团购

        //先判断大促
        if (input1?.countdown) {
            def result = new CountdownVO(
                    countdownTitle: input1.countdownTitle,
                    countdown: input1.countdown,
                    countdownBgImg: input1.countdownBgImg,
                    warmUpTitle: input1.warmUpTitle,
            )
            // activityState = 1 是预热阶段 2是大促中
            if (input1.activityState == 1){
                result.type = 1
                result.textStyle = new TextStyle(textColor: "#FFFFFF", fontSize: 18)
            }
            else{
                result.type = 0
            }

            return result
        }
        //团购中
        else if (input2?.status == TuanStatus.IN && input2?.endTime) {
            //读取团购倒计时背景麦田资源
            def backgroundRes = MaitUtil.getMaitData(4020);
            String countdownBgImg = backgroundRes?.get(0)?.get("countdownBgImgV2")

            return new CountdownVO(
                    countdownTitle: '距团购结束还剩',
                    countdownBgImg: ImageUtil.img(countdownBgImg),
                    countdown: input2.endTime - System.currentTimeSeconds(),
                    type: 0,
            )
        }
        //团购预热
        else if (input2?.status == TuanStatus.PRE && input2?.startTime) {
            //读取团购倒计时背景麦田资源
            def backgroundRes = MaitUtil.getMaitData(4020);
            String countdownBgImg = backgroundRes?.get(0)?.get("countdownBgImgV2")

            return new CountdownVO(
                    countdownTitle: '距团购开始还剩',
                    countdownBgImg: ImageUtil.img(countdownBgImg),
                    countdown: input2.startTime - System.currentTimeSeconds(),
                    type: 0,
            )
        }

        return new CountdownVO()//保证esi能覆盖缓存数据
    }
}
