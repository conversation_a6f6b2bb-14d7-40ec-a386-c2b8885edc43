package groovy.mgj.app.common


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IZeroDependTranslator

/**
 * Created by w<PERSON><PERSON> on 2019-09-24.
 * 包括商品讲解和直播透出
 * since 1300
 */
@Translator(id = "liveAndExplain", defaultValue = DefaultType.EMPTY_MAP)
class LiveAndExplain implements IZeroDependTranslator<Object> {
    @Override
    Object translate() {
        // 已废弃
        return null
    }
}
