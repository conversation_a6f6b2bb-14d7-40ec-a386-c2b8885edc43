package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.HideControl
import groovy.mgj.app.vo.Tools

/**
 * Created by pananping on 2020-07-01.
 */

/**
 * 虽然叫做 OfficialSelect，但对应组件已作为一组通用可配的 banner 使用
 * H5、xCx 同位置 banner 的数据也是来自下方代码，注意判断来源
 */

@Translator(id = "officialSelect", defaultValue = DefaultType.NULL)
class OfficialSelect implements IOneDependTranslator<ShopDO, OfficialSelectVO> {

    class OfficialSelectVO extends HideControl {
        String imageURL
        String link
        Integer w
        Integer h
    }

    @Override
    OfficialSelectVO translate(ShopDO shopDO) {

        List<Map<String, Object>> maitData = MaitUtil.getTargetedMaitData(148808)
        if (maitData?.size() > 0) {
            for (Map<String, Object> item in maitData) {
                if (shopDO?.getTags()?.contains(item.get("shopTag")) && item.get("bannerImage") != null) {
                    Integer width, height
                    (width, height) = Tools.getCDNImageWidthAndHeight((String) item.get("bannerImage"))
                    return new OfficialSelectVO(
                            _extra_control_hide_: false,
                            imageURL: item.get("bannerImage"),
                            link: item.get("bannerJumpURL"),
                            w: width, h: height
                    )
                }
            }
        }

        return new OfficialSelectVO(_extra_control_hide_: true)
    }
}