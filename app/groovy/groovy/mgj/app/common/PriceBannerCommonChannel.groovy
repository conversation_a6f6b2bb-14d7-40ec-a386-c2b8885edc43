package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.PriceBannerVOV2
import groovy.mgj.app.vo.PriceTagVO

/**
 * Created by wuy<PERSON> on 2020/3/12.
 *
 * 通用渠道的价格，非常简单。
 */
@Translator(id = "priceBannerCommonChannel")
class PriceBannerCommonChannel implements IOneDependTranslator<ItemBaseDO, PriceBannerVOV3> {
    static class PriceBannerVOV3 extends PriceBannerVOV2 {
        List<PriceTagVO> priceTags
    }

    @Override
    PriceBannerVOV3 translate(ItemBaseDO itemBaseDO) {
        PriceBannerVOV3 ret = new PriceBannerVOV3()

        ret.highNowPrice = itemBaseDO?.highNowPrice
        ret.lowNowPrice = itemBaseDO?.lowNowPrice
        ret.highPrice = itemBaseDO?.highPrice
        ret.lowPrice = itemBaseDO?.lowPrice
        ret.updatePrices()

        List<PriceTagVO> priceTags = new ArrayList<>()
        if (itemBaseDO.discountDesc) {
            priceTags.add(new PriceTagVO(
                    text: itemBaseDO.discountDesc,
                    textColor: "#ff4466",
                    bgColor: "#fff6F6"
            ))
            ret.priceTag = itemBaseDO?.discountDesc
        }
        ret.priceTags = priceTags

        // 处理区间价
        if (ret.price?.contains("~")) {
            ret.price = ret.price?.replace("~¥", "~")
            // 比较一言难尽的处理
            // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
            if (ret.price?.contains(".00~") && ret.price?.endsWith(".00")) {
                ret.price = ret.price?.replace(".00", "")
            }
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        ret.mayHideOldPrice(itemBaseDO)

        return ret
    }

}
