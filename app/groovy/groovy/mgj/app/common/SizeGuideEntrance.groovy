package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO

/**
 * Created by s<PERSON><PERSON> on 4/26/18.
 */
@Translator(id = "sizeGuideEntrance", defaultValue = DefaultType.EMPTY_MAP)
class SizeGuideEntrance implements ITwoDependTranslator<ItemBaseDO, SizeHelperDO, Object> {

    class SizeGuideEntranceIconVO {
        int type
        String bgImg
    }
    class SizeGuideEntranceVO {
        SizeGuideEntranceIconVO icon
        String title
        String titleColor
        String accessoryTitle
        String accessoryTitleColor
        Boolean showArrow
    }

    @Override
    Object translate(ItemBaseDO itemBaseDO, SizeHelperDO sizeHelperDO) {
        if (!showGuide(itemBaseDO, sizeHelperDO)) {
            return new SizeGuideEntranceVO()
        }
        SizeGuideEntranceIconVO iconVO = new SizeGuideEntranceIconVO(
                type: 1,
                bgImg: ImageUtil.img("/mlcdn/c45406/180426_5676fbc516dkc41fkc3jdf4k92cgj_30x30.png")
        )
        return new SizeGuideEntranceVO(
                icon: iconVO,
                title: "设置身材信息，享受精准尺码推荐",
                titleColor: "#666666",
                accessoryTitle: "立即设置",
                accessoryTitleColor: "#FF5777",
                showArrow: true
        )
    }

    boolean showGuide(ItemBaseDO itemBaseDO, SizeHelperDO sizeHelperDO) {
        boolean ret = false
        def cids = ["#705#", "#706#", "#684#", "#710#"]
        cids.each {
            if (itemBaseDO?.cids.contains(it)) {
                ret = true
            }
        }

        if (DetailContextHolder.get().getLoginUserId() == null || (sizeHelperDO?.userInfoFilled && ret)) {
            ret = false
        }
        return ret
    }
}