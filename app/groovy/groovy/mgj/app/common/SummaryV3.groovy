package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import groovy.mgj.app.common.common.SummaryRateInfoVO
import groovy.mgj.app.common.common.Utils
import groovy.mgj.app.vo.ActivityVO
import groovy.mgj.app.vo.PriceTagVO
import groovy.mgj.app.vo.SummaryVO
import groovy.mgj.app.vo.TextStyle
import org.apache.commons.lang3.StringEscapeUtils
import org.apache.http.util.TextUtils

import java.text.SimpleDateFormat

/**
 * Created by wuyi on 2018/9/6.
 */
@Translator(id = "summaryV3")
class SummaryV3 implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, RateDO, ShopDO, PinTuanDO, NormalCountdownDO,SkuDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, PresaleDO presaleDO, GroupbuyingDO groupbuyingDO, RateDO rateDO, ShopDO shopDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, SkuDO skuDO) {
        if (isClientSupportOfficialRecommend() && OfficialRecommendTranslator.shouldShow(itemBaseDO)) {
            // 客户端支持，并且有官方推荐，那么summary返回null
            return new HashMap()
        }

        SummaryVO summaryVO = new SummaryVO()

        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get("xsbk")
        CountdownInfo xinpin = normalCountdownDO?.getCountdownInfoMap()?.get("xinp")

        // 氛围标 跟priceBannerV2以及AtmosphereBannerV2里标题前面的icon是配套的，判断条件保持一致
        // 预售
        if (presaleDO) {
            summaryVO.titleIcon = ""
        }
        // 待开售
        else if (itemBaseDO?.state == 3) {
            summaryVO.titleIcon = ""
        }
        // 当前是大促(包括品牌日等)正式期并且本商品是活动商品
        else if (activityDO && activityDO?.activityState == 2) {
            if(activityDO.activityTitleImage) {
                summaryVO.titleIcon = activityDO.activityTitleImage
            }
        }
        // 限时爆款。限时爆款没有预热，就是正式期
        else if(xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY){
            Map<String, Object> maitData = MaitUtil.getMaitData(xsbk?.maitId1110)?.get(0)
            summaryVO.titleIcon = maitData?.get("titleIcon")
        }
        // 团购正式期
        else if (groupbuyingDO && groupbuyingDO?.status == TuanStatus.IN && groupbuyingDO?.endTime) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
            summaryVO.titleIcon = maitData?.get("titleIcon")
        }
        // 招商非渠道拼团。没有预热期，就是正式期
        else if (Tools.isSystemPintuan(itemBaseDO, pinTuanDO)) {
            Map<String, Object> maitData = MaitUtil.getMaitData(123206)?.get(0)
            summaryVO.titleIcon = maitData?.get("titleIcon")
        }
        // 新品正式期
        else if (xinpin && xinpin.startTime <= System.currentTimeSeconds() && xinpin.endTime > System.currentTimeSeconds() && xinpin.state == CountdownState.IN_ACTIVITY) {
            Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
            summaryVO.titleIcon = maitData?.get("titleIcon")
        }
        // 不是正式期，是预热或者普通
        else {
            long lastPreActivityStartTime = Long.MAX_VALUE
            boolean isPre = false

            // 当前是大促(包括品牌日等)预热期并且本商品是活动商品，并且比其他预热的开始时间要早
            if (activityDO && activityDO?.warmUpPrice?.price && activityDO?.activityState == 1
                    && activityDO.startTime < lastPreActivityStartTime) {
                if (activityDO.activityTitleImage) {
                    summaryVO.titleIcon = activityDO.activityTitleImage
                }

                isPre = true
            }
            // 团购预热期，并且比其他预热的开始时间要早
            if (groupbuyingDO?.status == TuanStatus.PRE && groupbuyingDO?.startTime
                    && groupbuyingDO.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(123204)?.get(0)
                summaryVO.titleIcon = maitData?.get("titleIcon")

                isPre = true
            }
            // 新品预热期，并且比其他预热的开始时间要早
            if (xinpin && xinpin.startTime > System.currentTimeSeconds() && xinpin.state == CountdownState.WARM_UP
                    && xinpin.startTime < lastPreActivityStartTime) {
                Map<String, Object> maitData = MaitUtil.getMaitData(xinpin?.maitId1110)?.get(0)
                summaryVO.titleIcon = maitData?.get("titleIcon")

                isPre = true
            }

            // 普通
            if (!isPre) {
                summaryVO.titleIcon = ""
            }
        }

        // 评价，非ESI时才返回
        if (!DetailContextHolder.get().isDyn()) {
            // DSR信息
            if (rateDO != null && itemBaseDO != null) {
                SummaryRateInfoVO summaryRateInfoVO = new SummaryRateInfoVO(itemBaseDO, rateDO, shopDO)
                summaryVO.rate = summaryRateInfoVO
            }
        }

        // 标的优先级 自营 > 促销/渠道 > 跨境
        summaryVO.titleIconsList = []
        // 自营，1160后上移到头图组件里展示
        if (Tools.isSelfEmployedItem() && !VersionController.needTranslateSelfIcon()) {
            Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
            String icon = maitData?.get("selfIcon")
            if (icon) {
                summaryVO.titleIconsList.add(icon)
            }
        }
        // 上面处理过的促销/渠道标
        if (!TextUtils.isEmpty(summaryVO.titleIcon)) {
            summaryVO.titleIconsList.add(summaryVO.titleIcon)
        }
        // 跨境
        if (itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM
                || itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
            Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
            String icon = maitData?.get("overseaIcon")
            if (icon) {
                summaryVO.titleIconsList.add(icon)
            }
        }

        // 如果之前都没有设titleIcon，然后这是个跨境电商商品，在标题前展示跨境电商标
        if (itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM
                || itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
            if (TextUtils.isEmpty(summaryVO.titleIcon)) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("overseaIcon")
                if (icon) {
                    summaryVO.titleIcon = icon
                }
            }
        }

        // 跟share组件里的url一样的逻辑
        String url = groovy.mgj.app.vo.Tools.getH5Url(DetailContextHolder.get().getItemDO().getItemId())
        if (pinTuanDO?.activityId) {
            url = url + "&businessId=${pinTuanDO.activityId}"
        }
        summaryVO.shareUrl = url

        // 标题
        summaryVO.title = itemBaseDO?.title ? StringEscapeUtils.unescapeHtml4(itemBaseDO?.title) : itemBaseDO?.title

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        summaryVO.installment = hintWrapper?.hint
        summaryVO.installmentStyle = hintWrapper?.textStyle

        summaryVO.installment = summaryVO.installment ?: ""

        summaryVO.with {
            // 新版summary一定不展示价格，价格都展示在priceBanner
            isShowPrice = false
            // pricetag和eventpricetag都移到pricebanner了，这里不要展示
            priceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
            // eventPrice
            eventPrice = ""
            // eventPriceTag
            eventPriceTag = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
            // eventTags
            eventTags = []
            // activity
            activity = new ActivityVO()

            eventPriceTag1110 = new PriceTagVO(
                    text: null,
                    textColor: null,
                    bgColor: null
            )
        }

        // for esimerge
        summaryVO.with {
            if (titleIconsList == null) {
                titleIconsList = []
            }
        }
        
        return summaryVO
    }

    /**
     * 客户端从1150开始新增的官方推荐模块
     */
    private static boolean isClientSupportOfficialRecommend() {
        return Utils.getClientVersion() >= 1150
    }
}
