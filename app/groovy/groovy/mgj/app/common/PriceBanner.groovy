package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownInfo
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.mgj.app.vo.CountdownVO
import groovy.mgj.app.vo.PriceBannerVO
import groovy.mgj.app.vo.Tools

@Translator(id = "priceBanner")
class PriceBanner  implements ISixDependTranslator<ActivityDO, GroupbuyingDO,ItemBaseDO,PinTuanDO, NormalCountdownDO,PresaleDO, PriceBannerVO> {

    @Override
    PriceBannerVO translate(ActivityDO input1, GroupbuyingDO input2,ItemBaseDO itemBase, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presale) {

        PriceBannerVO result = new PriceBannerVO()
        result.highNowPrice = itemBase?.highNowPrice
        result.lowNowPrice = itemBase?.lowNowPrice
        result.highPrice = itemBase?.highPrice
        result.lowPrice = itemBase?.lowPrice
        result.updatePrices()

        //现在有两种倒计时banner，大促和团购，同一个页面只显示一个倒计时
        //优先级：大促>限时爆款>团购
        CountdownInfo xsbk = normalCountdownDO?.getCountdownInfoMap()?.get("xsbk")
        //预售，大促预热期、团购预热不返回,
        if(presale != null || (input1 && input1?.activityState == 1.intValue()) || input2?.status == TuanStatus.PRE)
        {
            return null
        }
        //先判断大促
        else if (input1?.countdown && input1?.activityState == 2.intValue()) {
            CountdownVO countDown = new CountdownVO()
            countDown.countdown = input1?.countdown
            countDown.countdownTitle = "距结束仅剩"
            countDown.countdownTitleColor = input1?.endTimeHintColor
            result.countdown = countDown

            result.coverBg = input1?.activityInImage1110?input1?.activityInImage1110:""
            result.priceTag = input1?.priceDesc
            result.priceColor = input1?.endTimeHintColor

            if(!input1.warmUpPrice && !input1.inActivityItem && !input1?.priceDesc){
                //非活动商品，没有价格标
                if(xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY){
                    result.priceTag = MaitUtil.getMaitData(xsbk.maitId)?.get(0)?.get("priceDesc")?:"限时爆款价"}
                else if (input2 != null && (input2.status == TuanStatus.IN || input2.status == TuanStatus.PRE)){
                    if (input2.status == TuanStatus.IN) {
                        result.priceTag = MaitUtil.getMaitData(123204)?.get(0)?.get("priceTag")
                    } else if (input2.status == TuanStatus.PRE) {
                        result.priceTag = ''
                    }
                }
                else if (result.priceTag == null && !input1?.hideDiscount && itemBase?.discountDesc) {
                    updatePintuanSummary(result, itemBase, pinTuanDO)
                    if(result.priceTag == null) {
                        result.priceTag = itemBase.discountDesc
                    }
                }
            }

            updatePintuanSummary(result, itemBase, pinTuanDO)

            return result
        }
        // 限时爆款
        else if (xsbk && xsbk.startTime <= System.currentTimeSeconds() && xsbk.endTime > System.currentTimeSeconds() && xsbk.state == CountdownState.IN_ACTIVITY) {
            def xsbkMait = MaitUtil.getMaitData(123210)?.get(0)
            CountdownVO countDown = new CountdownVO()
            countDown.countdown = xsbk.countdown
            countDown.countdownTitle = "距结束仅剩"
            countDown.countdownTitleColor = xsbkMait?.get("countdownColor")
            result.countdown = countDown

            result.priceTag = MaitUtil.getMaitData(xsbk.maitId)?.get(0)?.get("priceDesc")?:"限时爆款价"
            result.coverBg = xsbkMait?.get("coverBg")
            result.priceColor = xsbkMait?.get("priceColor")

            updatePintuanSummary(result, itemBase, pinTuanDO)
            return result
        }
        //团购中
        else if (input2?.status == TuanStatus.IN && input2?.endTime) {
            //读取团购倒计时背景麦田资源
            def maitData = MaitUtil.getMaitData(123204)?.get(0)
            CountdownVO countDown = new CountdownVO()
            countDown.countdown = input2?.endTime - System.currentTimeSeconds()
            countDown.countdownTitle = "距结束仅剩"
            countDown.countdownTitleColor = maitData?.get("countdownColor")
            result.countdown = countDown

            result.priceTag = maitData?.get("priceTag")
            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")

            updatePintuanSummary(result, itemBase, pinTuanDO)
            return result
        }
        //招商非渠道拼团商品
        //U质团的预告期间，如果商品正在招商非渠道拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
        else if (Tools.isSystemPintuan(itemBase, pinTuanDO)
                || (input2?.status == TuanStatus.PRE && input2?.startTime && input2?.bizType == TuanBizType.UZHI && Tools.isSystemPintuan(itemBase, pinTuanDO) && input2?.endTime)  ) {
            def maitData = MaitUtil.getMaitData(123206)?.get(0)
            CountdownVO countDown = new CountdownVO()
            countDown.countdown = pinTuanDO.remainTime
            countDown.countdownTitle = "距结束仅剩"
            countDown.countdownTitleColor = maitData?.get("countdownColor")
            result.countdown = countDown

            result.coverBg = maitData?.get("coverBg")
            result.priceColor = maitData?.get("priceColor")

            updatePintuanSummary(result, itemBase, pinTuanDO)
            return result
        }

        // 为了esimerge能正确覆盖的操作
        result.with {
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (countdown == null) {
                countdown = new CountdownVO()
            }
            if (priceTag == null) {
                priceTag = ""
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (coverBg == null) {
                coverBg = ""
            }
            if (priceColor == null) {
                priceColor == ""
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
        }

        return null
    }


    /**
     * 更新拼团商品的价格以及价格标
     * @param summary
     * @param input1
     * @param pintuanDO
     * @return
     */
    def updatePintuanSummary(PriceBannerVO priceBannerVO, ItemBaseDO input1, PinTuanDO pintuanDO)
    {
        //是拼团
        if (com.mogujie.detail.spi.dslutils.Tools.isPintuan(input1, pintuanDO)) {
            //price 现价

            priceBannerVO.lowNowPrice = pintuanDO.skuInfo?.lowNowPrice
            priceBannerVO.highNowPrice = pintuanDO.skuInfo?.highNowPrice

            priceBannerVO.updatePrices()

            //priceTag 现价标
            if (priceBannerVO?.priceTag == null) {
                priceBannerVO.priceTag = "拼团价"
            }
        }
    }

}
