package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.common.HintWrapperVO
import groovy.mgj.app.common.common.SummaryHintProvider
import org.apache.http.util.TextUtils

@Translator(id = "officialRecommend")
class OfficialRecommendTranslator implements ISevenDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, NormalCountdownDO, PinTuanDO, GroupbuyingDO, SkuDO, Object> {

    class OfficialRecommendVO {

        String title
        String desc
        String highlightDesc
        List<String> tags
    }

    @Override
    OfficialRecommendVO translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, PresaleDO presaleDO, NormalCountdownDO normalCountdownDO, PinTuanDO pinTuanDO, GroupbuyingDO groupbuyingDO, SkuDO skuDO) {
        if (!shouldShow(itemBaseDO)) {
            return new OfficialRecommendVO()
        }

        def tags = []
        // 氛围标 不展示氛围标了，起因是运营不想官方推荐的商品在大促期间展示大促标
//        String titleIcon = TitleIconAndPriceHistoryUtil.getTitleIcon(itemBaseDO, activityDO, groupbuyingDO, pinTuanDO, normalCountdownDO, presaleDO)
//        if (!TextUtils.isEmpty(titleIcon)) tags << titleIcon
        // 官方推荐标
        OfficialRecommend or = itemBaseDO.getOfficialRecommend()
        def tag = MaitUtil.getMaitData(127253)?.get(0)?.get("officialRecommendIcon")
        if (tag) tags << tag

        HintWrapperVO hintWrapper = SummaryHintProvider.getHint(skuDO, true)
        String highlightDesc = hintWrapper?.hint

        highlightDesc = highlightDesc ?: ""

        return new OfficialRecommendVO(
                title: or.title ?: "",
                desc: or.desc ?: "",
                highlightDesc: highlightDesc ?: "",
                tags: tags)
    }

    static hasValidOfficialRecommend(ItemBaseDO itemBaseDO) {
        if (itemBaseDO == null || itemBaseDO.getOfficialRecommend() == null) {
            return false
        }

        OfficialRecommend or = itemBaseDO.getOfficialRecommend()
        return !TextUtils.isEmpty(or.title) || !TextUtils.isEmpty(or.desc)
    }

    static boolean shouldShow(ItemBaseDO itemBaseDO) {
        return hasValidOfficialRecommend(itemBaseDO) && groovy.mgj.app.vo.Tools.hasDataId("officialRecommend")
    }
}
