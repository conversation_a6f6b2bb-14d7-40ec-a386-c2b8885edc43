package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.CodecUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools

import java.text.SimpleDateFormat

/**
 * Created by pananping on 2020/12/31.
 */
@Translator(id = "allInOneExpress", defaultValue = DefaultType.NULL)
class AllInOneExpress implements IThreeDependTranslator<ExtraDO, ShopDO, SkuDO, Object> {

    static class AllInOneExpressVO {
        String express
        String address
        String destAddress
        String defaultShippingTime
        Map<String, String> shippingTimeMap
    }

    @Override
    Object translate(ExtraDO extraDO, ShopDO shopDO, SkuDO skuDO) {
        AllInOneExpressVO vo = new AllInOneExpressVO()

        if (extraDO?.isFreePost()) {
            vo.express = "免邮费"
        } else if (extraDO?.postPrice != null) {
            def price = String.format("%.2f", extraDO?.postPrice / 100.0f)
            vo.express = "快递 ${price}元"
        } else {
            vo.express = "默认快递"
        }

        if (extraDO?.address) { // encryptWithCache 入参必须判空，否则会抛异常
            vo.address = CodecUtil.encryptWithCache(extraDO.address)
        }

        if (extraDO?.recvAddress) {
            vo.destAddress = "配送至 " + extraDO?.recvAddress
        } else {
            vo.destAddress = "配送至 默认地址"
        }

        fillShippingInfo(vo, shopDO, skuDO)

        return vo
    }

    static def fillShippingInfo(AllInOneExpressVO vo, ShopDO shopDO, SkuDO skuDO) {

        Integer itemPromiseShipTime = shopDO?.itemPromiseDeliveryTime
        Integer itemPromiseShipDay = 0
        if (itemPromiseShipTime) {
            itemPromiseShipDay = itemPromiseShipTime < 86400 ? 1 : itemPromiseShipTime / 86400
        }

        Long shopAvgShipTime = shopDO?.shopAvgDeliveryTime
        String avgShipDay = ""
        if (shopAvgShipTime) {
            avgShipDay = String.format("%.1f", (double) shopAvgShipTime / 3600 / 24)
            if (avgShipDay == "0.0") {
                avgShipDay = "1.0"
            }
        }

        String itemPromiseShipTimeDesc = ""
        if (itemPromiseShipTime) {
            itemPromiseShipTimeDesc = springFestivalShipTimeDesc(itemPromiseShipTime) // 春节期间用的发货文案，优先级最高
            if (!itemPromiseShipTimeDesc) {
                if (shopAvgShipTime && shopAvgShipTime < itemPromiseShipTime) {
                    itemPromiseShipTimeDesc = "${itemPromiseShipDay}天内发货(平均${avgShipDay}天发货)".toString()
                } else {
                    itemPromiseShipTimeDesc = "${itemPromiseShipDay}天内发货".toString()
                }
            }
        }

        Map<String, String> shippingTimeMap = new HashMap<>()
        vo.shippingTimeMap = shippingTimeMap

        skuDO?.skus?.each {
            if (it.stockId) {
                if (it.delayHours) {
                    String springFestivalDesc = springFestivalShipTimeDesc(it.delayHours * 3600) // 春节期间用的发货文案，优先级最高
                    if (springFestivalDesc) {
                        shippingTimeMap.put(it.stockId, springFestivalDesc)
                    } else {
                        Integer delayShipDay = it.delayHours < 24 ? 1 : it.delayHours / 24
                        if (shopAvgShipTime && shopAvgShipTime < it.delayHours * 3600) {
                            shippingTimeMap.put(it.stockId, "${delayShipDay}天内发货(平均${avgShipDay}天发货)".toString())
                        } else {
                            shippingTimeMap.put(it.stockId, "${delayShipDay}天内发货".toString())
                        }
                    }
                } else {
                    shippingTimeMap.put(it.stockId, itemPromiseShipTimeDesc)
                }
            }
        }

        Integer slowestSku = skuDO?.skus?.collect { it.delayHours }?.sort()?.last()
        if (!slowestSku || (slowestSku * 3600 < itemPromiseShipTime && skuDO?.skus?.find {!it.delayHours})) {
            vo.defaultShippingTime = itemPromiseShipTimeDesc
        } else  {
            vo.defaultShippingTime = shippingTimeMap.get(skuDO?.skus?.find {it.delayHours == slowestSku }?.stockId)
        }
    }

    // 春节期间的发货文案
    static String springFestivalShipTimeDesc(long shipSecs) {
        if (Tools.inSpringFestival() && Tools.isSpringFestivalShutdownItem()) {
            long springDeliveryEndTime = Tools.springDeliveryEndTime()
            if (System.currentTimeSeconds() + shipSecs < springDeliveryEndTime) {
                return new SimpleDateFormat("最晚M月d日发货").format(new Date(springDeliveryEndTime * 1000))
            }
        }
        return null
    }
}