package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.SwitchKey
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.CommonSwitchUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.metabase.utils.StringUtils
import org.apache.commons.collections.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired

@Translator(id = "salePromotionBanner")
class SalePromotionBanner implements IZeroDependTranslator<Object> {

    private static final long TARGET_RESOURCE_ID = 26877L;

    @Autowired
    protected CommonSwitchUtil commonSwitchUtil;

    class SalePromotionBannerVO {
        String background
        String link
        String acm
    }

    @Override
    SalePromotionBannerVO translate() {

        List<Map<String, Object>> ret = null;
        ret = MaitUtil.getTargetedMaitData(TARGET_RESOURCE_ID)
        if (!CollectionUtils.isEmpty(ret)) {
            SalePromotionBannerVO VO =  new SalePromotionBannerVO(
                    background: ret.get(0).get("bannerImg"),
                    link: ret.get(0).get("bannerLink"),
                    acm: ret.get(0).get("acm")
            )
            if (!StringUtils.isEmpty(VO.link) && !StringUtils.isEmpty(VO.acm)) {
                if (VO.link.contains("?")) {
                    VO.link = VO.link + "&acm=" + VO.acm
                } else {
                    VO.link = VO.link + "?acm=" + VO.acm
                }
            }
            return VO
        }
        return new SalePromotionBannerVO()//保证esi能覆盖缓存数据
    }
}
