package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO

/**
 * Created by pananping on 2021/1/3.
 */
@Translator(id = "parameters", defaultValue = DefaultType.NULL)
class Parameters implements ITwoDependTranslator<ItemBaseDO, ItemParamsDO, Object> {

    @Override
    Object translate(ItemBaseDO itemBaseDO, ItemParamsDO itemParamsDO) {
        ItemParamsAppV2 oldTranslator = new ItemParamsAppV2()
        return oldTranslator.translate(itemBaseDO, itemParamsDO)
    }
}
