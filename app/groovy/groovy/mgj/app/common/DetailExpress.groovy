package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.CodecUtil
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.ShareGiftProvider

/**
 * Created by fufeng on 2017/3/24.
 */

@Translator(id = "detailExpress")
class DetailExpress implements IOneDependTranslator<ExtraDO, DetailExpressVO> {
    static class DetailExpressVO {
        String express
        String address
        String shareIcon
        String shareTitle
        String modou
    }

    @Override
    DetailExpressVO translate(ExtraDO inputExtraDO) {
        DetailExpressVO detailExpressVO = new DetailExpressVO(
                shareTitle: "分享",
                shareIcon: ImageUtil.img("/mlcdn/e5265e/170407_48ei9g2gc8jebcb758kaeeded381f_33x33.png")
        )
        if (inputExtraDO?.isFreePost()) {
            detailExpressVO.express = "免邮费"
        } else if (inputExtraDO?.postPrice != null) {
            def price = String.format("%.2f", inputExtraDO?.postPrice / 100.0f)
            detailExpressVO.express = "快递 ${price}元"
        } else {
            detailExpressVO.express = "默认快递"
        }
        if (inputExtraDO?.address) {
            //encryptWithCache的input必须判空,否则会抛异常
            detailExpressVO.address = CodecUtil.encryptWithCache(inputExtraDO.address)
        }
        //去麦田获取分享Icon和Text
        //这个资源位是商品定投,动态请求期间不返回,降低麦田的请求量
        if (DetailContextHolder.get().isDyn()) {
            //置 null,表示使用ATS中的缓存数据
            detailExpressVO.shareIcon = null
            detailExpressVO.shareTitle = null
        }
        else {
            def maitData = ShareGiftProvider.getShareGiftMaitData()
            if (maitData != null) {
                detailExpressVO.with {
                    shareIcon = maitData.shareIcon
                    shareTitle = maitData.shareText
                }
            }
        }

        if(inputExtraDO?.modouDiscount){
            detailExpressVO.modou = "蘑豆抵"+inputExtraDO.modouDiscount+"%"
        }

        return detailExpressVO
    }
}