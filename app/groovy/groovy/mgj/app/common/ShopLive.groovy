package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.mgj.app.vo.HideControl

/**
 * Created by pananping on 2020-08-14.
 */
@Translator(id = "shopLive", defaultValue = DefaultType.NULL)
class ShopLive implements IOneDependTranslator<LiveSimpleDO, ShopLiveVO> {

    static class ShopLiveVO extends HideControl {
        String jumpUrl
        String avatar
    }

    @Override
    ShopLiveVO translate(LiveSimpleDO liveSimpleDO) {
        if (liveSimpleDO?.shopLiveInfo) {
            return new ShopLiveVO(
                    jumpUrl: liveSimpleDO.shopLiveInfo.appJumpUrl,
                    avatar: liveSimpleDO.shopLiveInfo.actAvatar,
                    _extra_control_hide_: false
            )
        }
        return new ShopLiveVO(_extra_control_hide_: true)
    }
}