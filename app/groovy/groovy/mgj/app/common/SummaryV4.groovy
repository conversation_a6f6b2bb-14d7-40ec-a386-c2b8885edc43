package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OverseaItemEnum
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.app.common.common.TitleIconAndPriceHistoryUtil
import groovy.mgj.app.common.common.Utils
import org.apache.commons.lang3.StringEscapeUtils
import org.apache.http.util.TextUtils

/**
 * Created by wuyi on 2019/4/4.
 */
@Translator(id = "summaryV4")
class SummaryV4 implements IEightDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, ExtraDO, PinTuanDO, NormalCountdownDO, LiveSimpleDO, Object> {
    final static long HELP_ME_CHOOSE_MAIT_ID = 143035L

    static class GDSummarySimpleVO{
        String title
        String price
        String priceColor
        String oldPrice
        String shareUrl
        List<String> titleIconsList
        HelpMeChooseEntrance helpMeChooseEntrance
    }

    static class HelpMeChooseEntrance {
        String acm
        String link
    }

    @Override
    Object translate(ItemBaseDO itemBaseDO, ActivityDO activityDO, PresaleDO presaleDO, GroupbuyingDO groupbuyingDO, ExtraDO extraDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, LiveSimpleDO liveSimple) {
        if (isClientSupportOfficialRecommend() && OfficialRecommendTranslator.shouldShow(itemBaseDO)) {
            // 客户端支持，并且有官方推荐，那么summary返回null
            return new HashMap()
        }

        GDSummarySimpleVO summaryVO = new GDSummarySimpleVO()

        summaryVO.titleIconsList = []
        //https://mogu.feishu.cn/docs/doccnMEOAFlPBFWzAH7Ds0oYwMg 双十一预售标
        if (itemBaseDO?.numTags?.contains("1538")) {
            Map<String, Object> maitData = MaitUtil.getMaitData(156421)?.get(0)
            String icon = maitData?.get("tagImg")
            if (!TextUtils.isEmpty(icon)) {
                summaryVO.titleIconsList.add(icon)
            }
        }
        if (!(groovy.mgj.app.vo.Tools.isLiveSource() && groovy.mgj.app.vo.Tools.isInLiveRecommend())) {
            // 直播秒杀中标
            if (extraDO?.isLiveSeckill()) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("liveSeckillIcon")
                if (!TextUtils.isEmpty(icon)) {
                    summaryVO.titleIconsList.add(icon)
                }
            }
            // 活动中 
            else if (!liveSimple?.actUserInfo?.isLiving() && itemBaseDO?.numTags?.contains("1733") && groovy.mgj.app.vo.Tools.getAppVersion() >= 1410) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("liveActivityIcon")
                if (!TextUtils.isEmpty(icon)) {
                    summaryVO.titleIconsList.add(icon)
                }
            }
        }

        String titleIcon
        String priceHistory
        (titleIcon, priceHistory) = TitleIconAndPriceHistoryUtil.getTitleIcon(itemBaseDO, activityDO, groupbuyingDO, pinTuanDO, normalCountdownDO, presaleDO)

        // 上面处理过的促销/渠道标
        if (!TextUtils.isEmpty(titleIcon)) {
            summaryVO.titleIconsList.add(titleIcon)
        }

        // 没有其他标，才展示自营跨境
        if (summaryVO.titleIconsList.isEmpty()) {
            // 自营
            if (Tools.isSelfEmployedItem()) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("selfIcon")
                if (icon) {
                    summaryVO.titleIconsList.add(icon)
                }
            }

            // 跨境
            if (itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.BONDED_ITEM
                    || itemBaseDO?.overseaItemInfo?.overseaType == OverseaItemEnum.OVERSEA_DIRECT_MAIL) {
                Map<String, Object> maitData = MaitUtil.getMaitData(127253)?.get(0)
                String icon = maitData?.get("overseaIcon")
                if (icon) {
                    summaryVO.titleIconsList.add(icon)
                }
            }
        }

        // 跟share组件里的url一样的逻辑
        String url = groovy.mgj.app.vo.Tools.getH5Url(DetailContextHolder.get().getItemDO().getItemId())
        if (pinTuanDO?.activityId) {
            url = url + "&businessId=${pinTuanDO.activityId}"
        }
        summaryVO.shareUrl = url

        // 标题
        summaryVO.title = itemBaseDO?.title ? StringEscapeUtils.unescapeHtml4(itemBaseDO?.title) : itemBaseDO?.title

        // for esimerge
        summaryVO.with {
            if (titleIconsList == null) {
                titleIconsList = []
            }
        }

        // 根据类目决定是否要展示帮我选入口
        Map<String, Object> maitData = MaitUtil.getMaitData(HELP_ME_CHOOSE_MAIT_ID)?.get(0)
        List<String> shouldShowCids = maitData?.get("cids")?.toString()?.split(",")
        String acm = maitData?.get("acm")
        boolean matchCid = shouldShowCids?.any { itemBaseDO?.cids?.contains(String.format("#%s#", it)) ?: false } ?: false
        if (matchCid) {
            summaryVO.helpMeChooseEntrance = new HelpMeChooseEntrance(
                    acm: acm,
                    link: String.format("mgj://helpmechoose?acm=%s", acm)
            )
        } else {
            summaryVO.helpMeChooseEntrance = new HelpMeChooseEntrance()
        }

        // 如果是普通渠道页，不展示所有标
        if (DetailContextHolder.get()?.getRouteInfo()?.bizType == BizType.CHANNEL) {
            summaryVO.titleIconsList = new ArrayList<>()
        }

        return summaryVO
    }

    /**
     * 客户端从1150开始新增的官方推荐模块
     */
    private static boolean isClientSupportOfficialRecommend() {
        return Utils.getClientVersion() >= 1150
    }
}
