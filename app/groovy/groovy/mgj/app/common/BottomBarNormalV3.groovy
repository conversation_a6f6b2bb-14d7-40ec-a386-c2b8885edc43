package groovy.mgj.app.common

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.metabase.spring.client.MetabaseClient
import com.mogujie.metabase.utils.StringUtils
import groovy.mgj.app.common.common.BottomBarUtil
import groovy.mgj.app.common.common.ShareIntegralProvider
import groovy.mgj.app.vo.*
import org.apache.http.util.TextUtils

import javax.annotation.Resource

import static groovy.mgj.app.vo.NormalBottombarVO.*

@Translator(id = "bottomBarNormalV3")
class BottomBarNormalV3 implements ISevenDependTranslator<ItemBaseDO, ExtraDO, GroupbuyingDO, PinTuanDO, SkuDO, LiveDO, PresaleDO, Object> {
    // 商品状态
    public static final int STATUS_INVALID = -1 //非正常情况
    public static final int STATUS_NORMAL = 0  // 正常
    public static final int STATUS_OUT_OF_SHELF = 1 // 下架
    public static final int STATUS_SOLD_OUT = 2 // 卖完
    public static final int STATUS_WAIT_SALE = 3  // 待开售

    // 销售类型
    public static final int SALE_TYPE_NORMAL = 0// 正常销售
    public static final int SALE_TYPE_PRESALE = 1// 预售

    public static final int BOTTOMBAR_STATUS_INVALID = -1 //非正常情况
    public static final int BOTTOMBAR_STATUS_NORMAL = 0  // 正常
    public static final int BOTTOMBAR_STATUS_OUT_OF_SHELF = 1 // 下架
    public static final int BOTTOMBAR_STATUS_SOLD_OUT = 2 // 卖完
    public static final int BOTTOMBAR_STATUS_WAIT_SALE = 3  // 待开售
    public static final int BOTTOMBAR_STATUS_PRESALE = 4  // 预售
    public static final int BOTTOMBAR_STATUS_PINTUAN = 5  // 拼团

    /**
     * metabase配置client
     * 目前获取虚拟商品开关
     */
    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient

    @Override
    Object translate(ItemBaseDO itemBaseDO, ExtraDO extraDO, GroupbuyingDO groupbuyingDO, PinTuanDO pintuanDO, SkuDO skuDO, LiveDO theLiveDO, PresaleDO presaleDO) {
        NormalBottombarVO VO = new NormalBottombarVO(
                shopId: itemBaseDO?.shopId,
                isFaved: itemBaseDO?.isFaved,
                iid: itemBaseDO?.iid,
                addCartTips: itemBaseDO?.addCartTips,
                buyButtonText: "立即购买",
                addCartSkuCommunicationType: SKUDataVOCommunicationType.PINTUAN,
        )
        try {
            VO.shopUrl = itemBaseDO?.shopId ? "mgj://shop?shopId=${itemBaseDO?.shopId}" : null
            VO.saleStartTime = extraDO?.onSaleTime ? extraDO?.onSaleTime : 0
            VO.imUrl = (itemBaseDO?.shopId && itemBaseDO?.iid && itemBaseDO?.userId) ? "mgjim://talk?bid=${itemBaseDO?.shopId}&goodsId=${itemBaseDO?.iid}&userId=${itemBaseDO?.userId}&shopid=${itemBaseDO?.shopId}&login=1" : null
            VO.itemURL = groovy.mgj.app.vo.Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid))
        } catch (Throwable ignore) {
        }
        try {
            VO.normalBuy = new BottomBarPintuanButtonVO(
                    price: "¥${itemBaseDO?.lowNowPrice}",
                    text: "单独购买",
                    skuCommunicationType: SKUDataVOCommunicationType.DEFAULT
            )
        } catch (Throwable ignore) {
        }

        try {
            VO.pintuanBuy = new BottomBarPintuanButtonVO(
                    price: "¥${pintuanDO?.skuInfo?.lowNowPrice}",
                    text: "${pintuanDO?.tuanNum}人拼团",
                    skuCommunicationType: SKUDataVOCommunicationType.PINTUAN
            )
        } catch (Throwable ignore) {
        }

        try {
            VO.shareIntegralOpen = ShareIntegralProvider.canGainIntegral(itemBaseDO)
        } catch (Throwable throwable) {
            VO.shareIntegralOpen = false
        }

        //待开售商品
        if (itemBaseDO?.state == STATUS_WAIT_SALE && extraDO?.onSaleTime) {
            VO.waitForSaleNoticeTitle = "开售提醒：${itemBaseDO?.title}"
            VO.waitForSaleNoticeContent = "【蘑菇街开售提醒】：${itemBaseDO?.title} ${VO.itemURL}"
        }

        // 虚拟商品
        boolean isVirtualItem = (itemBaseDO?.virtualItemType != VirtualItemType.NORMAL)
        VO.buyBaseUrl = BottomBarUtil.getBuyBaseUrl(itemBaseDO)
        VO.normalBuyParams = BottomBarUtil.getBuyParamsString(skuDO, itemBaseDO, null)
        VO.pintuanBuyParams = BottomBarUtil.getBuyParamsString(skuDO, itemBaseDO, pintuanDO)
        VO.ptpPlaceHolder = BottomBarUtil.getPtpPlaceHolder(itemBaseDO)
        VO.liveParamsPlaceHolder = BottomBarUtil.getLiveParamsPlaceHolder(itemBaseDO)
        VO.fashionPlaceHolder = BottomBarUtil.getFashionParamsPlaceHolder(itemBaseDO)
        // 虚拟商品是否跳转到Native下单，先搞一个meatabase，防止有问题
        boolean shouldJumpToNativeVirtualOrder = true
        try {
            shouldJumpToNativeVirtualOrder = metabaseClient.getBoolean("dsl_shouldJumpToNativeVirtualOrder")
        } catch (Exception e) {
            shouldJumpToNativeVirtualOrder = true
        }
        if (shouldJumpToNativeVirtualOrder) {
            VO.buyBaseUrl = ""
        }

        boolean isPintuan = Tools.isPintuan(itemBaseDO, pintuanDO)
        boolean isPresale = isPreSale(itemBaseDO)

        // 处理addCartTips
        VO.addCartTips = VO.addCartTips && itemBaseDO?.state == STATUS_NORMAL && itemBaseDO?.saleType == SALE_TYPE_NORMAL && !isVirtualItem

        /**
         * 下面开始处理布局样式
         */
        int bottombarStatus
        if (isPintuan) {
            bottombarStatus = BOTTOMBAR_STATUS_PINTUAN
        } else if (isPresale) {
            bottombarStatus = BOTTOMBAR_STATUS_PRESALE
        } else {
            bottombarStatus = itemBaseDO?.state
        }

        /**
         * 左边的ButtonList
         */
        def leftTypeList = [LEFT_FAVED, LEFT_SHOP]

        // 拼团商品，需要在左边可以加入购物车
        if (bottombarStatus == BOTTOMBAR_STATUS_PINTUAN) {
            leftTypeList << LEFT_ADDCART
        }
        // 虚拟、医美商品、优惠券商品需要去掉加入购物车
        if (isVirtualItem || Tools.isMedicalBeautyItem() || Tools.isVirtualCouponItem()) {
            leftTypeList = leftTypeList.findAll {
                it != LEFT_ADDCART
            }
        }
        VO.leftButtonList = composeLeftList(leftTypeList)

        /**
         * 右边的ButtonList
         */
        List<BottomBarItemData> rightButtonList = new ArrayList<>()
        switch (bottombarStatus) {
            case BOTTOMBAR_STATUS_PINTUAN:
                // 拼团一定是能正常购买的非预售商品
                rightButtonList = composeRightList([RIGHT_NORMAL_BUY, RIGHT_PINTUAN_BUY])
                break
            case BOTTOMBAR_STATUS_NORMAL:
                rightButtonList = composeRightList([RIGHT_NORMAL_ADDCART, RIGHT_BUY])
                break
            case BOTTOMBAR_STATUS_WAIT_SALE:
                rightButtonList = composeRightList([RIGHT_NORMAL_ADDCART, RIGHT_WAIT_FOR_SALE])
                break
            case BOTTOMBAR_STATUS_OUT_OF_SHELF:
                // 下架
                rightButtonList = composeRightList([new BottomBarItemData(
                        type: RIGHT_NORMAL_ADDCART,
                        disable: true
                ), new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "已下架",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
            case BOTTOMBAR_STATUS_SOLD_OUT:
                // 卖光
                rightButtonList = composeRightList([new BottomBarItemData(
                        type: RIGHT_NORMAL_ADDCART,
                        disable: true
                ), new BottomBarItemData(
                        type: RIGHT_COMMON,
                        title: "卖光啦",
                        bgColor: "#999999",
                        textColor: "#ffffff",
                        disable: true
                )])
                break
            case BOTTOMBAR_STATUS_PRESALE:
                BottomBarItemData presale = new BottomBarItemData(
                        type: RIGHT_PRE_SALE,
                )
                if (!TextUtils.isEmpty(presaleDO?.expandMoney)) {
                    presale.title = String.format("立即付定金\n定金 %s 抵 %s", presaleDO.deposit, presaleDO.expandMoney)
                } else {
                    presale.title = "付定金"
                }
                rightButtonList = composeRightList([presale])
                break
            default:
                break
        }
        // 如果是虚拟、医美商品、优惠券商品，不展示加购按钮
        if (isVirtualItem || Tools.isMedicalBeautyItem() || Tools.isVirtualCouponItem()) {
            rightButtonList = rightButtonList.findAll{
                it.type != RIGHT_NORMAL_ADDCART
            }
        }
        VO.rightButtonList = rightButtonList

        /**
         * Float
         */
        List<BottomBarItemData> floatButtonList = new ArrayList<>()

        String iconUrl = MetabaseUtil.get("imIcon")
        if (StringUtils.isBlank(iconUrl)) {
            iconUrl = "/mlcdn/c45406/180403_372af4gfg40i29708jh5a23a0191a_116x116.png"
        }
        String imIcon = ImageUtil.img(iconUrl)
        floatButtonList.add(new BottomBarItemData(
                type: FLOAT_IM,
                icon: imIcon))

        String icon = ShareIntegralProvider.getShareIconUrl(itemBaseDO)
        if (!TextUtils.isEmpty(icon)) {
            BottomBarItemData shareItemData = new BottomBarItemData(
                    type: FLOAT_SHARE,
                    icon: icon)
            floatButtonList.add(shareItemData)
        }

        // 如果是虚拟商品，不展示私聊按钮  暂时不需要这个逻辑
//        if (isVirtualItem) {
//            floatButtonList = floatButtonList.findAll{
//                it.type != FLOAT_IM
//            }
//        }
        VO.floatButtonList = floatButtonList

        return VO
    }

    boolean isPreSale(ItemBaseDO itemBaseDO) {
        return (itemBaseDO?.saleType == SALE_TYPE_PRESALE
                && itemBaseDO?.state == STATUS_NORMAL)
    }
}
