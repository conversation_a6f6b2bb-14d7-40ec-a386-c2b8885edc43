package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.common.TagItemVO

@Translator(id = "groupBuy")
class GroupBuyInfo implements ITwoDependTranslator<GroupbuyingDO, ShopDO, Object>{
    class GroupBuyInfoVO{
        String topImage
        String backgroundColor
        TagItemVO[] services
    }

    static def isInTuan(GroupbuyingDO groupbuy) {
        return groupbuy && (groupbuy.status == TuanStatus.IN)
    }

    @Override
    GroupBuyInfoVO translate(GroupbuyingDO input1, ShopDO input2) {
        if (!isInTuan(input1)){
            return new GroupBuyInfoVO() //这样可以使esi覆盖缓存数据
        }
        def imgUrlMap = [:]
        imgUrlMap.put(TuanType.NORMAL, ImageUtil.img("/p1/160805/idid_ie3wimzwmyzwizrsmezdambqgayde_264x44.png"))
        imgUrlMap.put(TuanType.STORE, ImageUtil.img("/p1/160728/idid_ifrdcnrtgjqwcnzqmezdambqmeyde_268x44.png"))

        return new GroupBuyInfoVO(
                topImage: imgUrlMap[input1.type],
                backgroundColor: "#FFF9F9",
                services: input2?.services?.collect {
                    new TagItemVO(
                            link: it.link,
                            name: it.name,
                            icon: it.icon
                    )
                }
        )
    }
}
