package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveItemRecommendInfo
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.mgj.app.vo.HideControl
import org.apache.http.util.TextUtils

/**
 * Created by pananping on 2/23/21.
 */
@Translator(id = "buyerShopRecommend", defaultValue = DefaultType.NULL)
class BuyerShopRecommend implements IOneDependTranslator<LiveSimpleDO, Object> {

    class BuyerShopSlicePreloadItem {
        String type = "liveSlice"
        String key
        String firstFrameURL
        String videoURL
    }

    class BuyerShopRecommendItem {
        String image
        String title
        String price
        String sales
        String jumpURL
        Boolean showPlayIcon
    }

    class BuyerShopRecommendVO extends HideControl {
        List<String> acms
        List<BuyerShopRecommendItem> list
        List<BuyerShopSlicePreloadItem> preloadInfo
    }

    @Override
    Object translate(LiveSimpleDO liveSimpleDO) {
        if (liveSimpleDO?.liveItemRecommendInfos?.size() < 3) {
            return new BuyerShopRecommendVO(_extra_control_hide_: true)
        }

        List<LiveItemRecommendInfo> recommendList = liveSimpleDO.liveItemRecommendInfos.subList(0, 3)

        List<String> acms = recommendList.collect {
            it.acm
        }

        List<BuyerShopRecommendItem> translatedList = recommendList.collect {
            new BuyerShopRecommendItem(
                    image: it.itemImage,
                    title: it.title,
                    price: it.discountPrice,
                    sales: it.sale,
                    jumpURL: it.link,
                    showPlayIcon: it.videoUrl ? true : false
            )
        }

        List<BuyerShopSlicePreloadItem> preloadList = recommendList.collect {
            if (TextUtils.isEmpty(it.link) || TextUtils.isEmpty(it.videoUrl) || TextUtils.isEmpty(it.firstFrame)) {
                return null
            }

            return new BuyerShopSlicePreloadItem(
                    key: it.link,
                    firstFrameURL: it.firstFrame,
                    videoURL: it.videoUrl
            )
        }

        return new BuyerShopRecommendVO(
                _extra_control_hide_: false,
                acms: acms ?: [],
                list: translatedList ?: [],
                preloadInfo: preloadList ?: []
        )
    }
}