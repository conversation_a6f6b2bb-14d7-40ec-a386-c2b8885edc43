package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.TextStyle

import java.text.SimpleDateFormat

/**
 * Created by fufeng on 2017/2/8.
 */
@Translator(id = "waitForSaleInfo")
class WaitForSaleInfo implements ITwoDependTranslator<ItemBaseDO, ExtraDO, Object> {
    class WaitForSaleInfoVO {
        String title
        String signedTitle
        TextStyle textStyle
    }
    @Override
    Object translate(ItemBaseDO itemBaseDO, ExtraDO extraDO){
        def result = new WaitForSaleInfoVO()
        if (itemBaseDO?.state == 3 && extraDO?.onSaleTime) {
            result.textStyle = new TextStyle(textColor: "#5ECA2A", fontSize: 14, backgroundColor: "#E8F7E1")

            Date date = new Date(extraDO?.onSaleTime*1000)
            SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日HH:mm")
            String dateString = formatter.format(date)

            result.title = "${dateString}开售，请提前设置提醒"
            result.signedTitle = "${dateString}开售"

        }
        return result
    }
}
