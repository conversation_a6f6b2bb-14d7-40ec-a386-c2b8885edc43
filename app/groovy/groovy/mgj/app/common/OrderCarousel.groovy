package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import groovy.mgj.app.vo.HideControl

/**
 * Created by pananping on 2020/12/31.
 */
@Translator(id = "orderCarousel", defaultValue = DefaultType.NULL)
class OrderCarousel implements IOneDependTranslator<ExtraDO, Object> {

    static class OrderCarouselBuyerInfo {
        String avatar
        String text
    }

    static class OrderCarouselVO extends HideControl {
        List<OrderCarouselBuyerInfo> list
        Integer delayTime
        Integer fadeTime
        Integer displayTime
        Integer gapTime
    }

    @Override
    Object translate(ExtraDO extraDO) {
        Map<String, Integer> maitData = MaitUtil.getMaitData(152692)?.get(0)
        OrderCarouselVO vo = new OrderCarouselVO(
                _extra_control_hide_: false,
                delayTime: maitData?.get("delayTime") ?: 2000,
                fadeTime: maitData?.get("fadeTime") ?: 800,
                displayTime: maitData?.get("displayTime") ?: 2500,
                gapTime: maitData?.get("gapTime") ?: 500
        )

        if (extraDO?.buyerInfoList?.size() >= 3) {
            vo.list = extraDO.buyerInfoList.collect {
                new OrderCarouselBuyerInfo(
                        avatar: it.logo,
                        text: it.desc
                )
            }
        }
        return vo
    }
}