package groovy.mgj.app.common

/**
 * Created by fufeng on 2017/3/29.
 * 客户端932版本开始使用
 * 模板 >=1.0.3
 */


import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.StrategyUpUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.common.common.AnchorDataProvider

@Translator(id = "topBarNormalV2", defaultValue = DefaultType.NULL)
class TopBarNormalV2 implements IOneDependTranslator<ItemBaseDO,Object>{

    static class TopBarNormalV2VO{
        String iid
        String reportUrl
        int state
        boolean canEdit
        List<String> anchors
    }

    @Override
    Object translate(ItemBaseDO input1) {
        if (!input1){
            //业务上可以return null
            return null
        }
        def vo = new TopBarNormalV2VO(
                iid: input1?.iid,
                reportUrl: StrategyUpUtil.upUrl("https://h5.mogujie.com/report/add.html?itemId=${input1?.iid}"),
                state: input1?.state,
                canEdit: false,
                anchors: ["商品"]
        )
        String[] tmpAnchors = AnchorDataProvider.getAnchorDataList().collect {
            return it.text
        }
        vo.anchors.addAll(tmpAnchors)
        return vo
    }
}