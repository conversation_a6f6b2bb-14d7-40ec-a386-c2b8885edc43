package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.VirtualItemType
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sizeHelper.domain.SizeHelperDO
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SizePropData
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUDataVOActivityType
import groovy.mgj.app.vo.SKUVO
import org.apache.commons.collections4.CollectionUtils

@Translator(id = "skuDataV3")
class SKUDataV3 implements IFiveDependTranslator<SkuDO, ItemBaseDO, PresaleDO, GroupbuyingDO, SizeHelperDO, Object> {

    @Override
    SKUDataVO translate(SkuDO input1, ItemBaseDO input2, PresaleDO input3, GroupbuyingDO input4, SizeHelperDO input5) {
        if (!input1 || !input2) {
            //业务上可以返回NULL
            return null
        }
        def sizeTitle
        def matchedSize
        def sizeHelperEntrance
        //有尺码助手内容,需要在sku里添加号型数据
        if (input5 != null) {
            Map<String, String> sizeTypeMap = new HashMap<>();
            for (SkuData skuData : input1.skus) {
                if (skuData.sizeType != null) {
                    sizeTypeMap.put(skuData.size, skuData.sizeType);
                    skuData.size = "${skuData.size} (${skuData.sizeType})"
                }
            }
            String sizeKey = input1?.sizeKey;
            for (PropInfo prop : input1.getProps()) {
                if (prop.label.equals(sizeKey) && prop.list != null && prop.list.size() > 0) {
                    for (SizePropData sizePropData : prop.list) {
                        String sizeType = sizeTypeMap.get(sizePropData.name);
                        if (sizeType != null) {
                            sizePropData.name = "${sizePropData.name} (${sizeType})"
                        }
                    }
                    break;
                }
            }

            //默认展示查看尺码表
            sizeHelperEntrance = "查看尺码表"
            sizeTitle = "尺码推荐: 暂无"
            // 已登录,但是未填写尺码参数,应该显示完善尺码入口
            if (DetailContextHolder.get().getLoginUserId() != null && !input5.userInfoFilled) {
                sizeHelperEntrance = "完善尺码"
                sizeTitle = "尺码推荐: 暂无，请先完善尺码"
            } else if (input5.matchedSizeType != null) {
                // 已登录, 有填写尺码参数
                for (int i = 0; i < input1?.skus?.size(); i++) {
                    SkuData skuData = input1.skus[i]
                    // 推荐的号型与当前商品sku匹配
                    if (skuData?.sizeType == input5.matchedSizeType) {
                        matchedSize = skuData.size
                        sizeTitle = "尺码推荐"
                        break
                    }
                }
            }
        }

        String defaultImageUrl = null
        if (CollectionUtils.isNotEmpty(input2?.topImages)){
            defaultImageUrl = input2.topImages?.first();
        }
        def vo = new SKUDataVO(
                skuInfo: new SKUVO(input1),
                iid: input2?.iid,
                isPresale: input3 != null,
                defaultImageUrl: defaultImageUrl,
                mainPriceStr: input1.mainPriceStr,
                subPriceStr: input1.subPriceStr,
                activityType: SKUDataVOActivityType.DEFAULT,
                sizeTitle: sizeTitle ? sizeTitle : "",
                matchedSize: matchedSize ? matchedSize : "",
                sizeHelperEntrance: sizeHelperEntrance ? sizeHelperEntrance : "",
                maxFreePhases: input1?.freePhases?.intValue() ?: 0
        )
        if("940" == (DetailContextHolder.get().getParam("_av")) && DetailContextHolder.get().getParam("template").startsWith("1-3")) {
            if (input1?.styleKey == null && input1?.sizeKey) {
                vo?.skuInfo?.props?.add(0, new PropInfo())
            }
        }
        if (input3 != null) {
            vo.activityType = SKUDataVOActivityType.PRESALE
        } else if (input4?.status == TuanStatus.IN) {
            vo.activityType = SKUDataVOActivityType.TUANGOU
        }

        // 虚拟商品需要锁定SKU数量为1
        boolean isVirtualItem = (input2.virtualItemType != VirtualItemType.NORMAL)
        if (isVirtualItem) {
            vo.isFreezing = true
        } else {
            vo.isFreezing = false
        }

        return vo
    }
}
