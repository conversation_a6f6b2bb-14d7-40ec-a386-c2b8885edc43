package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

@Translator(id = "certInfo", defaultValue = DefaultType.NULL)
class CertInfo implements IOneDependTranslator<DetailDO, Object> {

    @Override
    Object translate(DetailDO detailDO) {
        if (!detailDO) {
            return null
        }

        return detailDO.getCertInfo()
    }
}
