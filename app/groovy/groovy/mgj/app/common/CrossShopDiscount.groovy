package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.coupon.domain.CouponDO
import groovy.mgj.app.vo.CrossShopDiscountVO
import groovy.mgj.app.vo.Tools

/**
 * Created by pananping on 2020/9/22.
 */
@Translator(id = "crossShopDiscount", defaultValue = DefaultType.NULL)
class CrossShopDiscount implements IOneDependTranslator<CouponDO, CrossShopDiscountVO> {

    @Override
    CrossShopDiscountVO translate(CouponDO couponDO) {

        String imgUrl = couponDO?.crossShopDiscountBanner?.imageUrl
        if (imgUrl) {
            Integer width, height
            (width, height) = Tools.getCDNImageWidthAndHeight(imgUrl)
            return new CrossShopDiscountVO(
                    _extra_control_hide_: false,
                    imageURL: imgUrl,
                    link: couponDO.crossShopDiscountBanner.appUrl,
                    w: width, h: height
            )
        }

        return new CrossShopDiscountVO(_extra_control_hide_: true)
    }
}