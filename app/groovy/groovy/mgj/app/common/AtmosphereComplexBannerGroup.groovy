package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by wuy<PERSON> on 2019/2/15.
 */
@Translator(id = "atmosphereComplexBannerGroup", defaultValue = DefaultType.NULL)
class AtmosphereComplexBannerGroup implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, AtmosphereComplexBannerGroupVO> {
    static class AtmosphereComplexBannerGroupVO {
        def atmosphereComplexBanner
    }

    @Override
    AtmosphereComplexBannerGroupVO translate(ItemBaseDO itemBaseDO, FastbuyDO fastbuyDO) {
        if (AtmosphereComplexBanner.shouldShow(itemBaseDO)) {
            AtmosphereComplexBanner translator = new AtmosphereComplexBanner()
            def VO = translator.translate(itemBaseDO, fastbuyDO)
            return new AtmosphereComplexBannerGroupVO(
                    atmosphereComplexBanner: VO
            )
        } else {
            return new AtmosphereComplexBannerGroupVO()
        }
    }
}
