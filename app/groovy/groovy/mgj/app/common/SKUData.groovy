package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.*
import groovy.mgj.app.vo.SKUDataVO
import groovy.mgj.app.vo.SKUVO
import org.apache.commons.collections4.CollectionUtils

@Translator(id = "skuData")
class SKUData implements IThreeDependTranslator<SkuDO, ItemBaseDO,PresaleDO, Object> {

    @Override
    SKUDataVO translate(SkuDO input1, ItemBaseDO input2, PresaleDO input3) {
        if (!input1 || !input2){
            return null
        }
        decorateOldSku(input1);

        String defaultImageUrl = null;
        if (CollectionUtils.isNotEmpty(input2?.topImages)){
            defaultImageUrl = input2.topImages?.first();
        }
        new SKUDataVO(
                skuInfo: new SKUVO(input1),
                iid: input2?.iid,
                isPresale: input3 != null,
                defaultImageUrl: defaultImageUrl,
                mainPriceStr: input1.mainPriceStr,
                subPriceStr: input1.subPriceStr
        )
    }

    void decorateOldSku(SkuDO skuDO) {
        if (null == skuDO) {
            return
        }
        for (SkuData skuData : skuDO.getSkus()) {
            if (!skuData.getSize()) {
                skuData.setSize("均码")
            }
            if (!skuData.getStyle()) {
                skuData.setStyle("默认")
            }
        }
        List<PropInfo> props = skuDO.getProps();
        if (CollectionUtils.isEmpty(props)) {
            List<PropInfo> newProps = new ArrayList<>();
            newProps.add(getDefaultStyle());
            newProps.add(getDefaultSize());
            skuDO.setProps(newProps);
            skuDO.setStyleKey("款式");
            skuDO.setSizeKey("尺码");
        } else if (props.size() == 1) {
            PropInfo prop = props.get(0);
            if (prop.getList().get(0) instanceof StylePropData) {
                props.add(getDefaultSize());
                skuDO.setSizeKey("尺码");
            } else {
                props.add(0,getDefaultStyle());
                skuDO.setStyleKey("款式");
            }
            skuDO.setProps(props);
        }
    }

    PropInfo<StylePropData> getDefaultStyle() {
        PropInfo<StylePropData> stylePropInfo = new PropInfo()
        stylePropInfo.setLabel("款式");
        StylePropData styleProp = new StylePropData();
        styleProp.setDefault(false);
        styleProp.setType("style");
        styleProp.setName("默认");
        styleProp.setIndex(1);
        styleProp.setStyleId(1);
        stylePropInfo.setList(Arrays.asList(styleProp));
        //默认sku能加库存的需求
        stylePropInfo.setDefault(false);
        return stylePropInfo;
    }

    PropInfo<SizePropData> getDefaultSize() {
        SizePropData sizeProp = new SizePropData();
        sizeProp.setDefault(false);//StringUtils.isEmpty(realSize));
        sizeProp.setType("size");
        sizeProp.setName("均码");
        sizeProp.setIndex(100);
        sizeProp.setSizeId(100);
        PropInfo<SizePropData> sizePropInfo = new PropInfo();
        sizePropInfo.setLabel("尺码");
        sizePropInfo.setList(Arrays.asList(sizeProp));
        sizePropInfo.setDefault(false);
        return sizePropInfo;
    }

}
