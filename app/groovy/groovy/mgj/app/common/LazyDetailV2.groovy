package groovy.mgj.app.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by xvxvxxx on 2017/5/22.
 */
@Translator(id = "lazyDetailV2", defaultValue = DefaultType.NULL)
class LazyDetailV2 implements IOneDependTranslator<ItemBaseDO, Object> {
    @Override
    Object translate(ItemBaseDO itemBaseDO) {
        //ESI请求没有必要返回
        if (DetailContextHolder?.get()?.isDyn()) {
            // 业务上可以返回NULL
            return null
        }
        if (itemBaseDO == null || itemBaseDO?.iid == null) {
            // 业务上可以返回NULL
            return null
        }
        def vo = [:]
        vo.type = "HTTP"
        vo.info = [:]
        vo.info.api = "http://d.mogujie.com/detail/api?template=1-4-detailinfo-1.0.1"
        vo.info.param = ["iid":itemBaseDO?.iid]
        vo.from = "detail"
        return vo
    }

}