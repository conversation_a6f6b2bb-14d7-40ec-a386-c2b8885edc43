package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.qualitycheck.domain.QualityCheckDO
import groovy.mgj.app.common.common.TagItemVO

@Translator(id = "qualityCheck", defaultValue = DefaultType.NULL)
class QualityCheck implements IOneDependTranslator<QualityCheckDO, Object>{
    class QualityCheckVO{
        TagItemVO[] reports
        String title = '质检报告'
    }
    static def nameIconMap = [
            "面料合格": ImageUtil.img('/p1/161117/idid_ifrdcyzygm4tgojummzdambqmeyde_512x512.png'),
            "外观合格": ImageUtil.img('/p1/161117/idid_ie4tkmbxgzrtgojummzdambqgqyde_512x512.png'),
            "版型一致": ImageUtil.img('/p1/161117/idid_ifrwkobymvrtgojummzdambqhayde_512x512.png'),
            "做工合格": ImageUtil.img('/p1/161117/idid_ifqwgmbrmvstoojummzdambqgyyde_512x512.png'),
            "着色牢固": ImageUtil.img('/p1/161117/idid_ie4wmmzsgvtdoojummzdambqgqyde_512x512.png'),
            "无异味" : ImageUtil.img('/p1/161117/idid_ifrdgmdbmntdoojummzdambqmeyde_512x512.png'),
            "无色差" : ImageUtil.img('/p1/161117/idid_ifrgiyrvguydqojummzdambqmeyde_512x512.png'),
            "尺码准确": ImageUtil.img('/p1/161117/idid_ifrtqnbymiydqojummzdambqhayde_512x512.png'),
    ]

    @Override
    Object translate(QualityCheckDO input1) {
        if (!input1){
            // 业务上可以返回NULL
            return null
        }
        return new QualityCheckVO(
                reports: input1.checkInfo.infoList.collect {
                    new TagItemVO(
                            name: it.content,
                            icon: nameIconMap[it.content],
                            content: null
                )}
        )
    }
}
