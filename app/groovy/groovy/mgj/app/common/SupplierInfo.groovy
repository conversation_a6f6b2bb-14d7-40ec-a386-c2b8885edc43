package groovy.mgj.app.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.groovy.util.MetabaseUtil
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.common.SupplierInfoVO
import groovy.mgj.app.vo.Tools

@Translator(id = "supplierInfo", defaultValue = DefaultType.NULL)
class SupplierInfo implements ITwoDependTranslator<LiveSimpleDO, ShopDO, Object> {

    @Override
    Object translate(LiveSimpleDO liveSimpleDO, ShopDO shopDO) {
        if (!liveSimpleDO?.actUserInfo) {
            return new SupplierInfoVO(_extra_control_hide_: true)
        }
        if (shopDO) {
            /**
             * shopFrom
             *
             * 0, 普通商详页 - 店铺入口
             * 1, 主播来源商详页 - 供应商店铺入口 √
             * 2, 普通商详页 - 底部店铺入口
             *
             * @see {http://pa.mogu-inc.com/dep/demandEstimate?demandId=3326}
             */
            SupplierInfoVO VO = new SupplierInfoVO(
                    name: shopDO?.name,
                    tag: "供应商",
                    level: shopDO?.level,
                    star: shopDO?.star,
                    link: shopDO?.shopId ?
                            Tools.appendRequestAcm("mgj://shop?shopFrom=1&shopId=" + shopDO?.shopId) : null)
            VO._extra_control_hide_ = false
            // 非动态请求，而且店铺在架商品数为0(可能是因为降级也可能是因为确实为0)，就不展示
            if (shopDO?.getCGoods() && shopDO?.getCGoods() > 0) {
                String saleDescTemplate = MetabaseUtil.get("shopSaleDesc")
                VO.saleDesc = saleDescTemplate ?
                        saleDescTemplate.replace("\${cGoods}", "${shopDO?.getCGoods()}")
                        : "在架商品数：${shopDO?.getCGoods()}"
            }
            return VO
        }
        return null
    }
}
