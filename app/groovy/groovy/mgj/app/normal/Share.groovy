package groovy.mgj.app.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.common.common.ShareGiftProvider
import groovy.mgj.app.common.common.ShareIntegralProvider
import groovy.mgj.app.vo.ShareVO
import groovy.mgj.app.vo.Tools
import org.apache.commons.collections4.CollectionUtils

@Translator(id = "share", defaultValue = DefaultType.NULL)
class Share implements IFourDependTranslator<ItemBaseDO, ShopDO, ActivityDO, ExtraDO, Object> {
    @Override
    ShareVO translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, ExtraDO extraDO) {
        if (!input1 || !input2) {
            //业务上可以return null
            return null
        }
        def res = MaitUtil.getMaitData(11754)
        def shareText = res?.get(0)?.get('sharetext') as String ?: '亲爱的，这件宝贝不错，进来看看有机会领现金券哦～ '

        def itemId = DetailContextHolder.get().getItemDO().getItemId()

        def shareVO = new ShareVO(
                itemTitle: input1.title,
                shareText: shareText,
                url: Tools.getH5Url(itemId),
                imUrl: (input1?.iid && input1?.shopId) ? "mgjim://share?iid=${input1.iid}&shopId=${input1.shopId}&type=1" : null,
                itemDesc: input1.desc,
                shopLogo: input2.shopLogo,
                shopId: input1.shopId,
                eventPrice: input3?.warmUpPrice?.price,
                ownerName: input2.name,
                iid: input1.iid,
                miniProgramPath: input1?.iid ? "pages/vxdetail/normal/index?itemId=${input1.iid}" : null
        )

        shareVO.highNowPrice = input1.highNowPrice
        shareVO.lowNowPrice = input1.lowNowPrice
        shareVO.highPrice = input1.highPrice
        shareVO.lowPrice = input1.lowPrice

        boolean forceNoRange = Tools.getAppVersion() >= 1440
        shareVO.updatePrices(forceNoRange)

        //IM那边需求传给IM的短链中加上一下参数
        shareVO.imUrl = getUpdatedIMUrl(shareVO.imUrl, input1, shareVO.url)

        shareVO.shareIntegralOpen = ShareIntegralProvider.canGainIntegral(input1)
        // 会返回空map，不会用缓存
        shareVO.shareIntegral = ShareIntegralProvider.getShareIntegralVO(input1)
        // 分享积分优先分享有赏
        shareVO.shareGiftOpen = !shareVO.shareIntegralOpen
        // 虽然shareGiftOpen是false的时候按说可以不用返回shareGift的数据，但是因为老版本不会处理分享积分shareIntegral的数据，所以shareGift的数据也得返回给老版本用
        //这个资源位是商品定投,动态请求期间不返回,降低麦田的请求量
        if (DetailContextHolder.get().isDyn() == false) {
            shareVO.shareBanner = ShareGiftProvider.getShareGiftMaitData()
        }

        // 区间价比较一言难尽的处理
        // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
        if (shareVO.price?.contains("~")) {
            shareVO.price = shareVO.price?.replace("~¥", "~")
            // 比较一言难尽的处理
            // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
            if (shareVO.price?.contains(".00~") && shareVO.price?.endsWith(".00")) {
                shareVO.price = shareVO.price?.replace(".00", "")
            }
        }

        if (extraDO?.sales && extraDO?.sales >= 100L) {
            if (extraDO?.sales > 100000L) {
                shareVO.saleCountDesc = String.format("销量 %.1f万", extraDO?.sales / 10000.0)
            } else {
                shareVO.saleCountDesc = String.format("销量 %d", extraDO?.sales)
            }
        }
        shareVO.activityIcon = input3?.activityIcon

        shareVO.with {
            if (activityIcon == null) {
                activityIcon = ""
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
        }

        return shareVO
    }

    static String getUpdatedIMUrl(String originalUrl, ItemBaseDO itemBaseDO, String h5Url) {
        String ret = originalUrl
        if (ret) {
            if (itemBaseDO.lowNowPrice) {
                //这里不用传价格区间了，确定传最低价就OK
                ret = ret + "&price=${itemBaseDO.lowNowPrice}"
            }
            if (itemBaseDO.title) {
                def encodedTitle = URLEncoder.encode(itemBaseDO.title)
                ret = ret + "&title=${encodedTitle}"
            }
            if (CollectionUtils.isNotEmpty(itemBaseDO.topImages) && itemBaseDO.topImages?.first()) {
                def encodedImageUrl = URLEncoder.encode(itemBaseDO.topImages?.first())
                ret = ret + "&imgUrl=${encodedImageUrl}"
            }
            if (h5Url) {
                def encodedLinkURL = URLEncoder.encode(h5Url)
                ret = ret + "&linkUrl=${encodedLinkURL}"
            }
        }
        return ret
    }
}