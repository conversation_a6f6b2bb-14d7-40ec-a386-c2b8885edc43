package groovy.mgj.app.normal

import com.mogujie.commons.utils.IdConvertor
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.RedirectorVO
import groovy.mgj.app.vo.Tools

/**
 * Created by wuyi on 2020/4/3.
 */
@Translator(id = "normalRedirector", defaultValue = DefaultType.EMPTY_MAP)
class RedirectorNormal implements IOneDependTranslator<ItemBaseDO, RedirectorVO> {
    @Override
    RedirectorVO translate(ItemBaseDO itemBaseDO) {
            if (Tools.isLiveSource() && !Tools.isInLiveRecommend()) {
                // 如果当前来源是新的直播商品，也就是有sourceParams并且指定type是1
                // 并且活动结束了，跳到不指定来源的普通详情页
                return new RedirectorVO(
                        toast: "活动已结束",
                        duration: 2,
                        url: Tools.getH5Url(IdConvertor.urlToId(itemBaseDO?.iid)),
                        closeSelf: true
                )
        }
        return new RedirectorVO()
    }
}
