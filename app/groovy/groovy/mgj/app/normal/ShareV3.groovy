package groovy.mgj.app.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITenDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.dailyconfig.domain.NewActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.common.PriceBannerV3
import groovy.mgj.app.common.Switcher
import groovy.mgj.app.common.common.Utils
import groovy.mgj.app.vo.PriceBannerVOV3
import groovy.mgj.app.vo.ShareVO
import groovy.mgj.app.vo.Tools
import org.apache.http.util.TextUtils

/**
 * Created by fufeng on 2017/12/8.
 */
@Translator(id = "shareV3", defaultValue = DefaultType.NULL)
class ShareV3 implements ITenDependTranslator<ShopDO, NewActivityDO, ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, NormalCountdownDO, PresaleDO, ExtraDO, SkuDO, Object> {
    @Override
    ShareVO translate(ShopDO shopDO, NewActivityDO newActivityDO, ActivityDO activityDO, GroupbuyingDO groupbuyingDO, ItemBaseDO itemBaseDO, PinTuanDO pinTuanDO, NormalCountdownDO normalCountdownDO, PresaleDO presaleDO, ExtraDO extraDO, SkuDO skuDO) {
        ShareV2 superTranslator = new ShareV2()
        def ret = superTranslator.translate(itemBaseDO, shopDO, activityDO, pinTuanDO, extraDO)
        if (!ret) {
            return ret
        }

        if (Utils.getClientVersion() >= 1150 && !TextUtils.isEmpty(itemBaseDO?.getOfficialRecommend()?.title)) {
            // 客户端1150以上支持官方推荐模块，并且有官方推荐的title，那么分享使用这个title
            ret.itemTitle = itemBaseDO.getOfficialRecommend().title
        }
        //拼团打点需要的businessId
        if (pinTuanDO?.activityId) {
            ret.url = ret.url + "&businessId=${pinTuanDO.activityId}"
            ret.businessId = pinTuanDO.activityId
            ret.miniProgramPath = ret.miniProgramPath + "&businessId=${pinTuanDO.activityId}"
        } else {
            ret.businessId = ""
        }

        boolean csslayoutShareOpen = Switcher.csslayoutMiniCardShare()
        ShareCssDataWrap cssDataWrap = null
        // 处理csslayout的相关数据
        if (csslayoutShareOpen) {
            PriceBannerV3 priceBannerV3 = new PriceBannerV3()
            PriceBannerVOV3 priceBannerVOV3 = priceBannerV3.translate(newActivityDO, activityDO, groupbuyingDO, itemBaseDO, pinTuanDO, normalCountdownDO, presaleDO, extraDO, skuDO, null)
            List<ShareCssDataProducer> producers = ShareCssDataProducers.FOR_NORMAL
            for (ShareCssDataProducer producer : producers) {
                cssDataWrap = producer.produce(itemBaseDO, extraDO, priceBannerVOV3, ret)
                if (cssDataWrap != null) {
                    break
                }
            }
        }
        if (cssDataWrap != null) {
            ret.miniCardTempMaitID = cssDataWrap.maitId
            ret.miniCardData = cssDataWrap.data
            ret.miniCardTempMaitParams = ["iid": "" + DetailContextHolder.get()?.getItemId()]
            ret.miniCardType = cssDataWrap.cardtype
        }

        long currentTime = System.currentTimeSeconds()
        if (extraDO?.flashBuyInfo?.npx2 != null && /* 有牛皮癣图 */
                extraDO?.flashBuyInfo?.st < currentTime && currentTime < extraDO?.flashBuyInfo?.et && /* 在活动正式期内 */
                !Tools.isLiveSource() /* 非直播商品进商城 */
        ) {
            ret.coverUrlForQRCard = extraDO.flashBuyInfo.npx2
        }

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        ret.mayHideOldPrice(itemBaseDO)

        return ret
    }
}
