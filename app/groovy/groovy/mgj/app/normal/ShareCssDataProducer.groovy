package groovy.mgj.app.normal

import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.PriceBannerVOV3
import groovy.mgj.app.vo.ShareVO

import javax.annotation.Nullable


/**
 * Created by wuyi on 2020/3/17.
 */
interface ShareCssDataProducer {
    /**
     * 一定要注意，快抢这种，给的priceBannerVOV3为空
     */
    ShareCssDataWrap produce(ItemBaseDO itemBaseDO, ExtraDO extraDO, @Nullable PriceBannerVOV3 priceBannerVOV3, ShareVO shareVO)
}