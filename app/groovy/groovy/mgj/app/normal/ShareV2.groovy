package groovy.mgj.app.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFiveDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.mgj.app.vo.ShareVO
import groovy.mgj.app.vo.Tools
import org.apache.commons.collections4.CollectionUtils

/**
 * Created by fufeng on 2017/8/24.
 */
@Translator(id = "shareV2")
class ShareV2 implements IFiveDependTranslator<ItemBaseDO, ShopDO, ActivityDO, PinTuanDO, ExtraDO, Object> {
    @Override
    ShareVO translate(ItemBaseDO input1, ShopDO input2, ActivityDO input3, PinTuanD<PERSON> pintuanDO, ExtraDO extraDO) {
        def superTranslator = new Share()
        def ret = superTranslator.translate(input1, input2, input3, extraDO)
        if (!ret) {
            return ret
        }

        ret.imUrl = getIMUrl(input1, ret.url, pintuanDO)

        if (Tools.isPintuan(input1, pintuanDO)) {
            //拼团商品
            //price 现价
            updatePintuanSummary(ret, input1, pintuanDO)

            return ret
        }
        else {
            //普通商品
            return ret
        }
    }

    static String getIMUrl (ItemBaseDO itemBaseDO, String h5Url, PinTuanDO pintuanDO) {
        String ret = "mgjim://share?iid=${itemBaseDO.iid}&shopId=${itemBaseDO.shopId}&type=1"

        String lowPrice = itemBaseDO?.lowNowPrice
        if (Tools.isPintuan(itemBaseDO, pintuanDO)) {
            lowPrice = pintuanDO?.skuInfo?.lowNowPrice
        }
        if (lowPrice) {
            //这里不用传价格区间了，确定传最低价就OK
            ret = ret + "&price=${lowPrice}"
        }
        if (itemBaseDO.title) {
            def encodedTitle = URLEncoder.encode(itemBaseDO.title)
            ret = ret + "&title=${encodedTitle}"
        }
        if (CollectionUtils.isNotEmpty(itemBaseDO.topImages) && itemBaseDO.topImages?.first()) {
            def encodedImageUrl = URLEncoder.encode(itemBaseDO.topImages?.first())
            ret = ret + "&imgUrl=${encodedImageUrl}"
        }
        if (h5Url) {
            def encodedLinkURL = URLEncoder.encode(h5Url)
            ret = ret + "&linkUrl=${encodedLinkURL}"
        }
        ret = ret.replaceAll("\\+", "%20")
        return ret
    }

    /**
     * 更新拼团商品的价格以及价格标
     * @param summary
     * @param input1
     * @param pintuanDO
     * @return
     */
    def updatePintuanSummary(ShareVO shareVO, ItemBaseDO input1, PinTuanDO pintuanDO) {
        //是拼团
        if (com.mogujie.detail.spi.dslutils.Tools.isPintuan(input1, pintuanDO)) {
            //price 现价

            shareVO.lowNowPrice = pintuanDO.skuInfo?.lowNowPrice
            shareVO.highNowPrice = pintuanDO.skuInfo?.highNowPrice

            shareVO.updatePrices()

            // 区间价比较一言难尽的处理
            // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
            if (shareVO.price?.contains("~")) {
                shareVO.price = shareVO.price?.replace("~¥", "~")
                // 比较一言难尽的处理
                // 如果是区间价并且最高价的最低价都是整数，去掉小数部分
                if (shareVO.price?.contains(".00~") && shareVO.price?.endsWith(".00")) {
                    shareVO.price = shareVO.price?.replace(".00", "")
                }
            }
        }
    }
}
