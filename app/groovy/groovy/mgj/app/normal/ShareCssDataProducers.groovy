package groovy.mgj.app.normal

import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.app.vo.ActivityType
import groovy.mgj.app.vo.PriceBannerVOV3
import groovy.mgj.app.vo.ShareCssData
import groovy.mgj.app.vo.ShareVO
import org.apache.http.util.TextUtils

import javax.annotation.Nullable

/**
 * Created by wuyi on 2020/3/17.
 */
@SuppressWarnings("DuplicatedCode")
class ShareCssDataProducers {
    // 对应https://mogu.feishu.cn/docs/doccntW8Ub6DjWjanhBpKr6cZGu
    // 中的1，其中的快抢商品
    static final ShareCssDataProducer PROMOTION_FASTBUY = new ShareCssDataProducer() {
        @Override
        ShareCssDataWrap produce(ItemBaseDO itemBaseDO, ExtraDO extraDO, @Nullable PriceBannerVOV3 priceBannerVOV3, ShareVO shareVO) {
            ShareCssDataWrap wrap = new ShareCssDataWrap()
            ShareCssData data = new ShareCssData(itemBaseDO, extraDO, shareVO)
            wrap.maitId = data.isRangePrice ? "144652" : "144658"
            wrap.cardtype = data.isRangePrice ? "1" : "2"
            data.discount = "快抢价"
            wrap.data = data
            return wrap
        }
    }

    // 对应https://mogu.feishu.cn/docs/doccntW8Ub6DjWjanhBpKr6cZGu
    // 中的1，闪购系列商品
    static final ShareCssDataProducer PROMOTION_SHANGO = new ShareCssDataProducer() {
        @Override
        ShareCssDataWrap produce(ItemBaseDO itemBaseDO, ExtraDO extraDO, @Nullable PriceBannerVOV3 priceBannerVOV3, ShareVO shareVO) {
            ActivityType activityType = priceBannerVOV3?.activityType
            if (ActivityType.SHANGO_IN == activityType) {
                ShareCssDataWrap wrap = new ShareCssDataWrap()
                ShareCssData data = new ShareCssData(itemBaseDO, extraDO, shareVO)
                wrap.maitId = data.isRangePrice ? "144652" : "144658"
                wrap.cardtype = data.isRangePrice ? "1" : "2"
                data.discount = priceBannerVOV3.priceTag
                wrap.data = data
                return wrap
            } else {
                return null
            }
        }
    }

    // 对应https://mogu.feishu.cn/docs/doccntW8Ub6DjWjanhBpKr6cZGu
    // 中的2，普通的有促销活动的商品
    static final ShareCssDataProducer NORMAL = new ShareCssDataProducer() {
        @Override
        ShareCssDataWrap produce(ItemBaseDO itemBaseDO, ExtraDO extraDO, @Nullable PriceBannerVOV3 priceBannerVOV3, ShareVO shareVO) {
            ActivityType activityType = priceBannerVOV3?.activityType
            if (ActivityType.NORMAL == activityType) {
                ShareCssDataWrap wrap = new ShareCssDataWrap()
                ShareCssData data = new ShareCssData(itemBaseDO, extraDO, shareVO)
                wrap.maitId = data.isRangePrice ? "144592" : "144567"
                wrap.cardtype = data.isRangePrice ? "4" : "3"
                data.discount = priceBannerVOV3.priceTag
                wrap.data = data
                // 确实有优惠才用这样的分享
                if (!TextUtils.isEmpty(data.discount) && !TextUtils.isEmpty(data.cutPrice)) {
                    return wrap
                } else {
                    return null
                }
            } else {
                return null
            }
        }
    }

    // 对应https://mogu.feishu.cn/docs/doccntW8Ub6DjWjanhBpKr6cZGu
    // 中的2，其中的新人价商品
    static final ShareCssDataProducer XINREN = new ShareCssDataProducer() {
        @Override
        ShareCssDataWrap produce(ItemBaseDO itemBaseDO, ExtraDO extraDO, @Nullable PriceBannerVOV3 priceBannerVOV3, ShareVO shareVO) {
            ShareCssDataWrap wrap = new ShareCssDataWrap()
            ShareCssData data = new ShareCssData(itemBaseDO, extraDO, shareVO)
            wrap.maitId = data.isRangePrice ? "144592" : "144567"
            wrap.cardtype = data.isRangePrice ? "4" : "3"
            data.discount = "新人价"
            wrap.data = data
            // 确实有优惠才用这样的分享
            if (!TextUtils.isEmpty(data.discount) && !TextUtils.isEmpty(data.cutPrice)) {
                return wrap
            } else {
                return null
            }
        }
    }

    // 普通详情页用的
    final static List<ShareCssDataProducer> FOR_NORMAL = [PROMOTION_SHANGO, NORMAL]
    // 快抢详情页用的
    final static List<ShareCssDataProducer> FOR_FASTBUY = [PROMOTION_FASTBUY]
    // 新人价详情页用的
    final static List<ShareCssDataProducer> FOR_XINREN = [XINREN]
}
