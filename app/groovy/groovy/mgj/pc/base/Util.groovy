package groovy.mgj.pc.base

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.MetabaseTool
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.spi.dslutils.Tools

/**
 * Created by changsheng on 13/09/2017.
 */

class Util  {

    static void setEsiDataForPrice (ItemPriceVO itemPrice) {
        itemPrice.with {
            if (nowPrice == null) {
                nowPrice = ""
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (highNowPrice == null) {
                highNowPrice = ""
            }
            if (priceTags == null) {
                priceTags = []
            }
            if (prePriceTag == null) {
                prePriceTag = new PriceTagVO()
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (eventPriceDesc == null) {
                eventPriceDesc = new PriceTagVO()
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (mobilePrice == null) {
                mobilePrice = ""
            }
            if (mobileDownloadLink == null) {
                mobileDownloadLink = ""
            }
            if (extraDesc == null) {
                extraDesc = ""
            }
            if (eventDesc == null) {
                eventDesc = ""
            }

            if (highNowPrice && nowPrice?.endsWith(".00") && highNowPrice?.endsWith(".00")) {
                nowPrice = nowPrice?.split(/\./)[0]
                highNowPrice = highNowPrice?.split(/\./)[0]
            }
        }
    }

    static Boolean getSwitchConfig(String configName) {
        return "true".equalsIgnoreCase(MetabaseTool.getValue(configName))
    }
    static Boolean isPintuanItem(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        // pintuanDO不为null，并且拼团库存不为0，并且商品不是下架、待开售、预售
        return pinTuan && (!Tools.isJdItem() && pinTuan?.skuInfo?.totalStock > 0 || Tools.isJdItem()) && itemBase?.state != 1 && itemBase?.state != 3 && presale == null && !Boolean.valueOf(DetailContextHolder.get().getParam("noPintuan"))
    }

}