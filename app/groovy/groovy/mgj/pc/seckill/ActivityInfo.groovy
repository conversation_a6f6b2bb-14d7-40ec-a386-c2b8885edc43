package groovy.mgj.pc.seckill

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.seckill.domain.SeckillDO

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-秒杀详情页-价格
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
public class ActivityInfo implements IOneDependTranslator<SeckillDO, SeckillDO> {

    @Override
    SeckillDO translate(SeckillDO input1) {
        return input1;
    }
}