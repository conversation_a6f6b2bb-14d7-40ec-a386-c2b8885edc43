package groovy.mgj.pc.livesupplychain

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IZeroDependTranslator

@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IZeroDependTranslator<ActivityInfoVO>{

    static class ActivityInfoVO {
    }

    @Override
    ActivityInfoVO translate() {
        return new ActivityInfoVO();
    }

}
