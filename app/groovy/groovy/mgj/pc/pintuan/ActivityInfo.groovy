package groovy.mgj.pc.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.pintuan.domain.PinTuanDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<PinTuanDO, PinTuanDO>{

    @Override
    PinTuanDO translate(PinTuanDO input1) {
        return input1;
    }

}
