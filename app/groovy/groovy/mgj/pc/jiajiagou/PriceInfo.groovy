package groovy.mgj.pc.jiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.pc.base.ItemPriceVO
import groovy.mgj.pc.base.PriceTagVO
import groovy.mgj.pc.base.Util

/**
 * Created by changsheng on 19/09/2017.
 */

@Translator(id = "priceInfo")
class PriceInfo implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                if (itemBase?.discountDesc) {
                    priceTags = [
                            new PriceTagVO(
                                    text: itemBase?.discountDesc
                            )
                    ]
                }
            }

            Util.setEsiDataForPrice(itemPrice)

            // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
            itemPrice.isHideOldPrice(itemBase)

            return itemPrice
        }
    }
}