package groovy.mgj.pc.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<FastbuyDO, FastbuyDO>{

    @Override
    FastbuyDO translate(FastbuyDO input1) {
        return input1;
    }

}
