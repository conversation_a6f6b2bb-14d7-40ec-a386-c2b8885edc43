package groovy.mgj.pc.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISevenDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.pc.base.TopCountdownVO
import groovy.mgj.pc.normal.utils.ActivityManager
import groovy.mgj.pc.normal.utils.ActivityType

/**
 * Created by chang<PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-活动氛围&倒计时
 */

@Translator(id = "countdownInfo")
class CountdownInfo implements ISevenDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, PresaleDO, ExtraDO, SkuDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ActivityDO activity, GroupbuyingDO groupbuying, ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale, ExtraDO extra, SkuDO sku) {

        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku)

        switch (activityType) {
            case ActivityType.DACU_PRE:
            case ActivityType.DACU_IN:
                Boolean isWarmUp = activityType == ActivityType.DACU_PRE;

                String priceDesc = activity?.priceDesc;
                if (isWarmUp) {
                    priceDesc = "(${priceDesc}${activity?.warmUpPrice?.price?:''})" // "(316价¥100～¥101)"
                }

                // http://hd.mogujie.org/internal-tyrael/detail?id=331&type=CAMPRULE&tempId=274 风车配置
                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: activity?.countdown,
                        image: activity.countdownBgImg,
                        priceColor: activity?.priceColor,
                        priceDesc: priceDesc,
                        priceGuarantee: activity?.priceGuarantee, // 30天价格保护
                        countdownTitle: activity?.countdownTitle,
                        warmUpTitle: activity?.warmUpTitle,
                        isWarmUp: isWarmUp
                )


            case ActivityType.TUANGOU_PRE:
            case ActivityType.TUANGOU_IN:
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(3990);
                Map<String, Object> md = maitData?.get(0);

                def tuanType = groupbuying?.getBizType();
                String imageBase = '';

                if (tuanType == TuanBizType.UZHI) {
                    imageBase = md?.get('utg_countdownBgImg')
                } else if (tuanType == TuanBizType.PINPAI) {
                    imageBase = md?.get('ptg_countdownBgImg')
                }

                String image = imageBase != '' ? ImageUtil.img(imageBase) : null;

                Boolean isWarmUp = activityType == ActivityType.TUANGOU_PRE


                String priceDesc = '';
                String countdownTitle = '';
//                String warmUpTitle = '';
                String priceColor = '';

                if (!isWarmUp) {
                    countdownTitle = "距活动结束还剩";
                    priceDesc = "质选价";
                    priceColor = "#ff0000";
                } else {
                    if (md?.size() > 0) {
                        countdownTitle = "距活动开抢仅剩";
//                        priceDesc = "现价";
                        priceColor = "#333333";
                        priceDesc = "质选价 " + groupbuying.getPrice();
                    }
                }


                Long nowTime = System.currentTimeSeconds();

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: (isWarmUp ? groupbuying.startTime : groupbuying.endTime) - nowTime,
                        image: image,
                        priceDesc: priceDesc,
                        countdownTitle: countdownTitle,
                        priceColor: priceColor,
                        isWarmUp: isWarmUp
                )


                // ********** pc拼团没有氛围背景图和倒计时的
//            case ActivityType.PINTUAN_PRE:
//            case ActivityType.PINTUAN_IN:
//                /**
//                 * 非渠道拼团-招商拼团(非招商的没有氛围)
//                 * 不存在非渠道拼团预热这种情况，不过代码先放着
//                 */
//                List<Map<String, Object>> maitData = MaitUtil.getMaitData(123206);
//                Map<String, Object> md = maitData?.get(0);
//
//                Long nowTime = System.currentTimeSeconds();
//                Long startTime = pinTuan?.startTime;
//                Boolean isWarmUp = nowTime < startTime;
//                Long countdown = isWarmUp ? startTime - nowTime : pinTuan?.remainTime
//
//                return new TopCountdownVO(
//                        businessType: activityType.toString(),
//                        countdown: countdown,
//                        image: md?.get("coverBg"),
//                        titleColor: md?.get("countdownColor"),
//                        priceColor:md?.get("priceColor"),
//                        titleIcon: md?.get("titleIcon"),
//                        activityBanner: md?.get("activityBanner"),
//                        isWarmUp: isWarmUp
//                )


            default:
                return new TopCountdownVO(
                        businessType: activityType.toString()
                )
        }

    }
}