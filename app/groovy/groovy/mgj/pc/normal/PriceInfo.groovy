package groovy.mgj.pc.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.mobileprice.domain.MobilePriceDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.pc.base.ItemPriceVO
import groovy.mgj.pc.base.PriceTagVO
import groovy.mgj.pc.base.Util
import groovy.mgj.pc.normal.utils.ActivityManager
import groovy.mgj.pc.normal.utils.ActivityType

/**
 * Created by changsheng on 14/03/2017.
 * H5私有模块-普通详情页-价格
 */

@Translator(id = "priceInfo")
class PriceInfo implements IEightDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, ExtraDO, SkuDO, MobilePriceDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku, MobilePriceDO moblie) {
        if (!itemBase) {
            return null;
        }


        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku)

        switch (activityType) {
            case ActivityType.PRESALE:
                itemPrice.with {
                    nowPrice = presale?.totalPrice?.replace("¥", "");
                    setOldPriceByRange(itemBase);
                    priceTags = [
                            new PriceTagVO(
                                    text: "预售价"
                            )
                    ];
                    eventPrice = presale?.deposit;
                    eventPriceDesc = new PriceTagVO(
                            text:  "定金"
                    )
                    if (presale?.expandMoney) {
                        eventTags = [
                                new PriceTagVO(
                                        text: "抵" + presale?.expandMoney
                                )
                        ]
                    }

                }
                break


            case ActivityType.DACU_IN:
            case ActivityType.DACU_PRE:
                itemPrice.with {
                    setNowPriceByRange(itemBase);
                    setOldPriceByRange(itemBase);
                    priceTags = activity?.priceDesc ? [
                            new PriceTagVO(
                                    text: activity?.priceDesc
                            )
                    ] : []
                    eventPrice = activity?.warmUpPrice?.price?.replace("¥", "")
                    eventPriceColor = activity?.warmUpPrice?.color
                    if (eventPrice) {
                        eventPriceDesc = new PriceTagVO(
                                text: activity?.warmUpPrice?.priceDesc,
                                bgColor: "#FFFFFF" //无背景色
                        )
                    }
                }
                break


            case ActivityType.TUANGOU_IN:
                String groupbuyingText = "团购价"
                if (groupbuying.status == TuanStatus.IN) {
                    // 团购正式
                    itemPrice.with {
                        if (itemBase) {
                            setNowPriceByRange(itemBase);
                            setOldPriceByRange(itemBase);
                        } else {
                            nowPrice = groupbuying?.price;
                        }

                        priceTags = [
                                new PriceTagVO(
                                        text: groupbuyingText
                                )
                        ]
                    }
                }
                break


            case ActivityType.TUANGOU_PRE:
                String groupbuyingText = "团购价"
//            if (groupbuying.bizType == TuanBizType.UZHI) {
//                 groupbuyingText = "U质团"
//            }
//            else if (groupbuying.bizType == TuanBizType.PINPAI) {
//                 groupbuyingText = "品牌团"
//            }

                if (groupbuying.status == TuanStatus.PRE) {
                    // 团购预热
                    itemPrice.with {
                        if (itemBase) {
                            setNowPriceByRange(itemBase);
                            setOldPriceByRange(itemBase);
                        }
                        // priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                        eventPrice = groupbuying?.price;
                        eventPriceDesc = new PriceTagVO(
                                text:  groupbuyingText
                        )

                        // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                        if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                            eventPriceDesc.text = ""
                        }
                    }
                }
                break


            case ActivityType.PINTUAN_IN:
            case ActivityType.PINTUAN_PRE:
            case ActivityType.SALEONTIME:
            default:
                itemPrice.with {
                    setNowPriceByRange(itemBase);
                    setOldPriceByRange(itemBase);
                    priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                }
                break
        }



        // 有拼团就要多一个展示拼团价（前端对应会有两个按钮，一个是拼团的按钮，一个是普通按钮［团购／限时爆款］），但氛围还是以团购、限时爆款优先
        // 比如：如果一个商品既是拼团又是限时爆款，那会展示拼团价和单独购买价（限时爆款价），然后氛围展示的是限时爆款的
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            itemPrice.with {
                nowPrice = pinTuan?.skuInfo?.lowNowPrice
                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                oldPrice = getOldPriceForce(itemBase)
            }
        }

        List<PriceTagVO> items = []

        if (activity?.eventTags) {
            items = activity.eventTags.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }
        }

        if (itemPrice.eventTags) {
            itemPrice.eventTags.addAll(items)
        } else {
            // 统一的活动Tag
            itemPrice.eventTags = items
        }

        itemPrice.with {
            // 手机专享价
            mobilePrice = moblie?.mobilePrice
            mobileDownloadLink = moblie?.mobileDownloadLink
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        itemPrice.isHideOldPrice(itemBase)


        return itemPrice;
    }

    static List<PriceTagVO> getDefaultPriceTags(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            return [
                    new PriceTagVO(
                            text: "拼团价"
                    )
            ]
        } else if (itemBase?.discountDesc) {
            return [
                    new PriceTagVO(
                            text: itemBase?.discountDesc
                    )
            ]
        } else {
            return []
        }

    }

}