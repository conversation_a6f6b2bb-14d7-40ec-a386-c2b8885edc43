package groovy.mgj.pc.normal

import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.collcationset.domain.CollcationSetDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetCampaignForDetailDTO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetItemGroupDTO

/**
 * Created by changsheng on 21/03/2017.
 * H5私有模块-普通详情页-搭配购
 */

@Translator(id = "collcationSet")
class CollcationSet implements IThreeDependTranslator<CollcationSetDO, ItemBaseDO, ShopDO, CollocationSetVO> {

    static class CollocationSetVO {
        String itemId;
        String sellerId;
        List<CollocationSetCampaignForDetailDTO> collcationSetList;
    }

    @Override
    CollocationSetVO translate(CollcationSetDO collcationSet, ItemBaseDO itemBase, ShopDO shop) {
        if (!collcationSet) {
            return null;
        }

        List<CollocationSetCampaignForDetailDTO> collcationSetList = collcationSet
        collcationSetList.indexed().collect { index, item ->
            for (int i = 0; i < item?.collocationSetItemGroupDTOs?.size(); i++) {
                CollocationSetItemGroupDTO element = item?.collocationSetItemGroupDTOs[i]
                // !!!!!!! 这里的代码maybe有坑，这里是引用式的修改，此处的defaultPrice修改了
                // !!!!!!! 其他的h5.translator使用的collcationSet都会被修改（目前来说，h5没有其他地方会用到这个值）
                // !!!!!!! 如果想彻底解决，只能深度拷贝一个对象来修改
                element.defaultPrice = "¥" + NumUtil.formatPriceDrawer((int)element.proPrice)
            }
        }
        return new CollocationSetVO(
                itemId: itemBase?.iid,
                sellerId: shop?.userId,
                collcationSetList: collcationSetList
        );
    }
}