package groovy.mgj.pc.normal.utils;

import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.pc.base.Util

/**
 * Create by changsheng on 2018/9/13 18:36
 * Package groovy.xcx.h5.normal.utils
 */
class ActivityManager {

    ActivityType activityType

    ActivityManager(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku) {
        this.activityType = getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku)
    }


    static ActivityType getActivityType(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, ExtraDO extra, SkuDO sku) {

        List<StartTimeData> startTimeList = new ArrayList<>()
        Long nowTime = System.currentTimeSeconds()

        /**
         * 先判断正式期活动，如果有的话，就直接返回
         */
        if (presale != null) {
            // 预售
            return ActivityType.PRESALE
        } else if (itemBase?.state == 3 && extra?.onSaleTime > nowTime) {
            // 定时开售
            return ActivityType.SALEONTIME
        } else if (activity != null && activity.activityState == 2) {
            // 大促正式
            return ActivityType.DACU_IN
        } else if (groupbuying != null && groupbuying.status == TuanStatus.IN) {
            // 团购
            return ActivityType.TUANGOU_IN
        } else if (Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system && nowTime > pinTuan?.startTime && !pinTuan?.isExpire()) {
            // 非渠道招商拼团，只有走招商才会显示
            return ActivityType.PINTUAN_IN
        } else {

            /**
             * 开始处理预热活动
             *
             * 1. 取商品报名的所有活动，
             * 2. 如果有处于正式期间的，则不展示预热价，展示正式氛围
             * 3. 如果没有正式期活动，则取出所有活动的开始时间，距离现在最近的一个活动，展示该活动的预热价，和预热氛围
             */

            /**
             * 大促预热且能取到预热价
             */
            if (activity != null && activity.activityState == 1 && activity?.warmUpPrice?.price) {
                startTimeList.push(new StartTimeData(
                        startTime: activity?.startTime,
                        activityType: ActivityType.DACU_PRE,
                ))
            }

            /**
             * 团购
             */
            if (groupbuying != null && groupbuying.status == TuanStatus.PRE) {
                startTimeList.push(new StartTimeData(
                        startTime: groupbuying?.startTime,
                        activityType: ActivityType.TUANGOU_PRE,
                ))
            }
            /**
             * 招商拼团
             */
            if (pinTuan != null && pinTuan?.system && nowTime < pinTuan?.startTime) {
                startTimeList.push(new StartTimeData(
                        startTime: pinTuan?.startTime,
                        activityType: ActivityType.PINTUAN_PRE,
                ))
            }

            return startTimeList.min { it?.startTime - nowTime }?.activityType ?: ActivityType.NORMAL
        }

    }

    static class StartTimeData {
        Long startTime
        ActivityType activityType
    }
}