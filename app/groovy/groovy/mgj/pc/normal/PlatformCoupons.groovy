package groovy.mgj.pc.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.coupon.domain.CouponDO


/**
 * Created by ch<PERSON><PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-部分商品优惠券
 */

@Translator(id = "platformCoupons")
class PlatformCoupons implements IOneDependTranslator<CouponDO, CouponDO> {

    @Override
    CouponDO translate(CouponDO coupon) {
        return coupon;
    }
}