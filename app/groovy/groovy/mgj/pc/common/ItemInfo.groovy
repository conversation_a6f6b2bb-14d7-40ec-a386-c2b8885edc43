package groovy.mgj.pc.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.itemBase.domain.VideoInfo
import com.mogujie.detail.module.seo.domain.SeoDO
import com.mogujie.detail.module.brandinfo.domain.BrandInfoDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.pc.base.Util

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-商品信息
 */

@Translator(id = "itemInfo")
class ItemInfo implements IFourDependTranslator<ItemBaseDO, ExtraDO, SeoDO, BrandInfoDO, ItemInfoVO> {

    static class ItemInfoVO {
        String desc;
        String title;
        String itemId;
        Boolean isFaved;
        // 是否显示分享赚积分
        Boolean isShareIntegral;
        Integer cFav;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer state;
        /**
         * 开售时间，待开售商品
         */
        Long saleStartTime;
        /**
         * 商品类型，0为普通商品，1为预售商品
         */
        Integer saleType;

        Map<String, String> seo;

        Integer type

        VideoInfo video

        Integer priceChannel

        Integer virtualItemType

        // 是否为医美虚拟商品
        Boolean isMedicalBeautyVirtualItem

        // 是否为虚拟商品
        Boolean isVirtualItem

        // 是否为虚拟优惠券商品
        Boolean isVirtualCouponItem

        // 是否为京东商品
        Boolean isJdItem

        // 是否为主播特供（直播供应链）
        Boolean isSpecialSupplyItem

        // 是否为跨境
        Boolean isCrossBorderItem

        // 是否显示开通"白付美"引导条
        Boolean canApplyInstallment

        // 蘑豆折扣
        String modouDiscount

        ExtraDO extra

        OfficialRecommend officialRecommend

        // 与类目相关的大促中通Banner
        def categoryBanner

        String lowPrice;
        String highPrice;
        String lowNowPrice;
        String highNowPrice;

        String brandName; // 品牌名称

        Long nowTime = System.currentTimeSeconds();

        // 标题tag
        List<String> titleTags = [];

        // 图片展示的右上角tag
        List<String> picTags = [];

        ItemInfoVO(ItemBaseDO item, ExtraDO extra, SeoDO seo, BrandInfoDO brand) {
            this.desc = item?.desc;
            this.title = item?.title;
            this.isFaved = item?.isFaved;
            this.state = item?.state;
            this.saleType = item?.saleType;
            this.type = item?.type;
            this.video = item?.video
            this.canApplyInstallment = item?.canApplyInstallment

            this.lowNowPrice = item?.lowNowPrice;
            this.highNowPrice = item?.highNowPrice;

            // 判断是否能展示原价
            this.lowPrice = item.canShowStrikethroughPrice? item?.lowPrice : this.lowNowPrice;
            this.highPrice = item.canShowStrikethroughPrice? item?.highPrice : this.highNowPrice;

            this.brandName = brand?.showName;
            this.officialRecommend = item?.officialRecommend?: new OfficialRecommend();


            this.seo = seo;
            this.itemId = item?.iid;
            this.isShareIntegral = Util.getSwitchConfig("shareIntegralSwitch");
            this.priceChannel = item?.priceChannel ?: 0;
            this.virtualItemType = item?.virtualItemType?.getCode();
            this.cFav = item?.getCFav();

            this.extra = extra?: new ExtraDO();
            this.saleStartTime = extra?.onSaleTime;
            this.modouDiscount = extra?.modouDiscount;

            this.isJdItem = Tools.isJdItem();
            this.isCrossBorderItem = Tools.isCrossBorderItem();
            this.isSpecialSupplyItem = Tools.isSpecialSupplyItem();
            this.isMedicalBeautyVirtualItem = Tools.isMedicalBeautyItem();
            this.isVirtualCouponItem = Tools.isVirtualCouponItem();
            this.isVirtualItem = this.virtualItemType != 0 || this.isMedicalBeautyVirtualItem || this.isVirtualCouponItem; // 虚拟商品

            List<Map<String, Object>> mdl1 = MaitUtil.getTargetedMaitData(117725)
            this.categoryBanner = mdl1?.get(0)

            List<Map<String, Object>> mdl2 =  MaitUtil.getMaitData(127253L)
            Map<String, Object> md2 = mdl2?.get(0)

            // 自营标
            if (Tools.isSelfEmployedItem() && md2?.get("topSelfIcon")) {
                this.picTags = [md2?.get("topSelfIcon")?.toString()]
            }

        }
    }

    @Override
    ItemInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, SeoDO seo, BrandInfoDO brand) {
        if (!itemBase) {
            return null;
        }
        ItemInfoVO itemInfo = new ItemInfoVO(itemBase, extra, seo, brand);
        return itemInfo;
    }
}