package groovy.mgj.pc.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.itemParams.domain.ProductInfo
import com.mogujie.detail.module.itemParams.domain.Rule
import groovy.xcx.h5.base.Util

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-商品参数
 */

@Translator(id = "itemParams")
class ItemParams implements ITwoDependTranslator<ItemParamsDO, ItemBaseDO, ItemParamsVO> {

    static class ItemParamsVO {
        ProductInfo info;
        Rule rule;
    }

    @Override
    ItemParamsVO translate(ItemParamsDO input1, ItemBaseDO itemBase) {
        if (!input1) {
            return null;
        }

        Boolean isNewSizeHelper = Util.isNewSizeHelper(itemBase)

        return new ItemParamsVO(
                info: input1?.info,
                rule: isNewSizeHelper ? null : input1?.rule
        );
    }
}