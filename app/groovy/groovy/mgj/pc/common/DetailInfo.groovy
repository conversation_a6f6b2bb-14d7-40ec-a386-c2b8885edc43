package groovy.mgj.pc.common

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-图文详情
 */

@Translator(id = "detailInfo")
class DetailInfo implements IOneDependTranslator<DetailDO, DetailDO> {

    @Override
    DetailDO translate(DetailDO input1) {
        if (!input1) {
            return null;
        }

        DetailDO detail = JSON.parseObject(JSON.toJSONString(input1), DetailDO.class);
        String link = detail?.shopDecorate?.link ?: '';

        // 不是h5类型的链接，都过滤掉（比如app短链）
        // 这个判断可以写在前端吧
        if (link && link.indexOf('http') != 0) {
            detail.shopDecorate = null
        }

        return detail;
    }
}
