package groovy.mgj.pc.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.buyuser.domain.BuyuserInfoDO


/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-登录信息
 */

@Translator(id = "userInfo")
class UserInfo implements IThreeDependTranslator<ItemBaseDO, ShopDO, BuyuserInfoDO, UserInfoVO> {

    static class UserInfoVO {
        String userId;
        Boolean isLogin;
        String loginUserId;
        Boolean isSelf;
        Boolean admin;
        Boolean isNewComer; // 是否为新人
        String shopId;
        String sellerId;
    }

    @Override
    UserInfoVO translate(ItemBaseDO itemBase, ShopDO shop, BuyuserInfoDO buyuser) {
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.with {
            isLogin = itemBase?.loginUserId ? true : false;
            loginUserId = itemBase?.loginUserId;
            isSelf = itemBase?.isSelf;
            userId = itemBase?.userId;
            shopId = itemBase?.shopId;
            admin = buyuser?.admin;
            isNewComer = buyuser?.isNewComer()
            sellerId = shop?.userId;
        }
        return userInfo;
    }
}