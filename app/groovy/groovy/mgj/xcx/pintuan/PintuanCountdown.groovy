package groovy.mgj.xcx.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import groovy.mgj.xcx.base.TopCountdownVO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 01/07/2017.
 * H5私有模块-拼团详情页-拼团倒计时
 */

@Translator(id = "pintuanCountdown")
class PintuanCountdown implements IOneDependTranslator<PinTuanDO, TopCountdownVO>{



    @Override
    TopCountdownVO translate(PinTuanDO pinTuan) {

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776)
        Map<String, Object> imgData = maitData?.get(0)

        def backgroundImg = imgData?.get("pintuanImage")
        def titleColor = imgData?.get("pintuanTitleColor")

        Boolean isExpire = pinTuan?.isExpire
        Long now = System.currentTimeSeconds()
        Long startTime = pinTuan?.startTime
        Long remainTime = pinTuan?.remainTime

        if (!pinTuan) {
            return null
        } else if (now < startTime) {
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - now,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else if (now > startTime && !isExpire) {
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: remainTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }
}
