package groovy.mgj.xcx.share

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import groovy.mgj.h5.base.ItemTagVO

/**
 * Created by chang<PERSON><PERSON> on 21/03/2017.
 * H5公共模块-商品销量、商品包邮、商品Tag、店铺服务体系
 */

@Translator(id = "shareItemServices")
class ShareItemServices implements IOneDependTranslator<ExtraDO, ItemServicesVO> {

    static class ItemServicesVO {
        List<ItemTagVO> columns
    }

    @Override
    ItemServicesVO translate(ExtraDO extra) {
        if (extra?.sales == null) {
            return null
        }
        // 销量和包邮
        String express = "默认快递"
        if (extra?.isFreePost()) {
            express = "免邮费"
        } else if (extra?.postPrice != null) {
            def price = String.format("%.2f", extra?.postPrice / 100.0f)
            express = "快递 ${price}元"
        }
        List<ItemTagVO> columns = [
                // 销量放在评价里
                new ItemTagVO(
                        desc: "销量 " + extra?.sales?.toString(),
                        icon: ImageUtil.img("/p1/161117/idid_ifrdkytcgbstoobummzdambqmeyde_512x512.png"),
                        name: "sales"
                ),
                new ItemTagVO(
                        desc: express,
                        icon: ImageUtil.img("/p1/161117/idid_ifqwgnbsmjsdoobummzdambqgyyde_512x512.png"),
                        name: "express"
                )
        ]

        if (extra?.address) {
            columns.add(new ItemTagVO(
                    desc: extra?.address,
                    icon: ImageUtil.img("/p1/161117/idid_ie4tgmbsgjsdoobummzdambqgqyde_512x512.png"),
                    name: "address"
            ))
        }

        return new ItemServicesVO(
                columns: columns
        )
    }
}