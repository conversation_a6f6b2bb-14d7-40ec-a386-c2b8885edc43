package groovy.mgj.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/03/2017.
 * H5私有模块-普通详情页-业务中间大Banner
 */

@Translator(id = "goodsBanners")
class GoodsBanners implements IOneDependTranslator<ItemBaseDO, GoodsBannerVO[]> {

    static class GoodsBannerVO {
        String img;
        String link;
    }

    @Override
    GoodsBannerVO[] translate(ItemBaseDO itemBase) {
        if (!itemBase) {
            return null;
        }
        return itemBase?.goodsBanners?.collect {
            new GoodsBannerVO(
                    img: it.img,
                    link: it.link
            )
        }
    }
}