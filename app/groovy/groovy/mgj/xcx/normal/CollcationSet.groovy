package groovy.mgj.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.collcationset.domain.CollcationSetDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.service.hummer.domain.dto.collocationset.CollocationSetCampaignForDetailDTO

/**
 * Created by changsheng on 21/03/2017.
 * H5私有模块-普通详情页-搭配购
 */

@Translator(id = "collcationSet")
class CollocationSet implements IThreeDependTranslator<CollcationSetDO, ItemBaseDO, ShopDO, CollocationSetVO> {

    static class CollocationSetVO {
        String itemId;
        String sellerId;
        List<CollocationSetCampaignForDetailDTO> collcationSetList;
    }

    @Override
    CollocationSetVO translate(CollcationSetDO collcationSet, ItemBaseDO itemBase, ShopDO shop) {
        if (!collcationSet) {
            return null;
        }
        return new CollocationSetVO(
                itemId: itemBase?.iid,
                sellerId: shop?.userId,
                collcationSetList: collcationSet
        );
    }
}