package groovy.mgj.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import groovy.mgj.xcx.base.TopCountdownVO

/**
 * Created by chang<PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-活动氛围&倒计时
 */

@Translator(id = "normalCountdown")
class NormalCountdown implements ITwoDependTranslator<ActivityDO, GroupbuyingDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ActivityDO activity, GroupbuyingDO groupbuying) {

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(43182);
        Map<String, Object> imgData = maitData?.get(0);

        // 优先级：大促 > 团购
        if (activity?.countdown) {
            if (activity?.activityState == 1 && activity?.warmUpTitle) {
                // 纯文案，取warmUpTitle
                return new TopCountdownVO(
                    text: activity?.warmUpTitle,
                    countdown: activity?.countdown,
                    image: activity?.activityPreImage,
                    isCountdomShow: false
                )
            } else {
                return new TopCountdownVO (
                    text: "距结束仅剩",
                    countdown: activity?.countdown,
                    image: activity?.activityInImage,
                    titleColor: activity?.activityInTitleColor
                )
            }
        } else if (groupbuying && groupbuying?.status == TuanStatus.IN && groupbuying?.endTime) {
            //读取团购倒计时背景麦田资源
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(groupbuying, imgData);

            return new TopCountdownVO (
                text: "距结束仅剩",
                countdown: groupbuying.endTime - System.currentTimeSeconds(),
                image: ImageUtil.img(info?.image),
                titleColor: info?.titleColor
            )
        } else if (groupbuying && groupbuying?.status == TuanStatus.PRE && groupbuying?.startTime) {
            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(groupbuying, imgData);

            return new TopCountdownVO (
                text: "距开始仅剩",
                countdown: groupbuying.startTime - System.currentTimeSeconds(),
                image: ImageUtil.img(info?.image),
                titleColor: info?.titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }

    static enum GroupBuyType {
        NORMAL,
        UZHI,
        STORE,
        PINPAI

        static public GroupBuyType getGroupBuyType(GroupbuyingDO groupbuyingDO) {
            if (groupbuyingDO.bizType == TuanBizType.UZHI) {
                return UZHI
            }
            else if (groupbuyingDO.type == TuanType.STORE) {
                return STORE
            }
            else if (groupbuyingDO.bizType == TuanBizType.PINPAI) {
                return PINPAI
            }
            else if (groupbuyingDO.bizType == TuanBizType.NORMAL){
                return NORMAL
            }
        }
    }

    static class GroupBuyImageAndTitleColorInfo {
        String image
        String titleColor

        public GroupBuyImageAndTitleColorInfo(GroupbuyingDO groupbuyingDO, Map<String, Object> imgData) {
            if (!imgData) {
                return
            }
            GroupBuyType groupBuyType = GroupBuyType.getGroupBuyType(groupbuyingDO)
            if (groupBuyType == GroupBuyType.UZHI) {
                image= imgData?.get("tuanUZhiImage")
                titleColor= imgData?.get("tuanUZhiTitleColor")
            }
            else if (groupBuyType == GroupBuyType.STORE) {

                image = imgData?.get("tuanInStoreImage")
                titleColor = imgData?.get("tuanInStoreTitleColor")
            }
            else if (groupBuyType == GroupBuyType.PINPAI) {
                image= imgData?.get("tuanPinpaiImage")
                titleColor= imgData?.get("tuanPinpaiTitleColor")
            }
            else if (groupBuyType == GroupBuyType.NORMAL) {
                image= imgData?.get("tuanNormalImage")
                titleColor= imgData?.get("tuanNormalTitleColor")
            }
        }
    }
}