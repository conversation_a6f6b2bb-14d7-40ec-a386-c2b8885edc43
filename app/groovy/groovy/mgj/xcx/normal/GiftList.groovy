package groovy.mgj.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.activity.domain.Alert
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by chang<PERSON><PERSON> on 23/03/2017.
 * H5私有模块-普通详情页-各种促销信息，包括分期购、魔豆、红包等
 */

@Translator(id = "normalGiftList")
class GiftList implements ITwoDependTranslator<ActivityDO, ItemBaseDO, GiftVO[]> {

    static class GiftVO {
        String icon;
        String title;
        String highlightText;
        String highlightColor;
        String link;
        Boolean showArrow;
        String arrowDesc;
        String type;
        Alert alert;
    }

    @Override
    GiftVO[] translate(ActivityDO activity, ItemBaseDO itemBase) {
        if (!activity?.giftList) {
            return null;
        }
        List<GiftVO> giftList = [];

        activity?.giftList?.each {
            giftList.add(new GiftVO(
                    icon: it.icon,
                    title: it.title,
                    highlightText: it.highlightText,
                    highlightColor: it.highlightColor,
                    link: it.link,
                    showArrow: it.showArrow,
                    arrowDesc: it.arrowDesc,
                    type: it.type,
                    alert: it.alert
            ));
        }

        def res = MaitUtil.getMaitData(29802);

        if (res && itemBase?.canApplyInstallment && ItemTag.INSTALMENT in itemBase?.itemTags) {
            String icon = res.get(0)?.get('icon')
            String link = res.get(0)?.get('link')
            String text = res.get(0)?.get('text')
            String accessoryText = res.get(0)?.get('accessoryText')
            def installmentTag = new GiftVO(
                    icon: ImageUtil.img(icon),
                    title: text,
                    link: link,
                    showArrow: link ? true : false,
                    arrowDesc: link ? accessoryText : null
            )
            giftList.add(0, installmentTag)
        }

        return giftList;
    }
}
