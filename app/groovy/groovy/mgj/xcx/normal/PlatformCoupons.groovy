package groovy.mgj.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.coupon.domain.PlatformCoupon

/**
 * Created by chang<PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-部分商品优惠券
 */

@Translator(id = "platformCoupons")
class PlatformCoupons implements IOneDependTranslator<CouponDO, PlatformCouponsVO> {

    static class PlatformCouponsVO {
        List<PlatformCouponVO> coupons;
    }

    static class PlatformCouponVO {
        String iconTitle;
        String title;
        String useTimeDesc;
        String img;
        String accessoryTitle;
        String linkUrl;
        String tagBgColor;
        String tagTextColor;

        PlatformCouponVO(PlatformCoupon platformCoupon) {
            iconTitle = platformCoupon?.iconTitle;
            title = platformCoupon?.title;
            useTimeDesc = platformCoupon?.useTimeDesc;
            img = platformCoupon?.img;
            accessoryTitle = platformCoupon?.accessoryTitle;
            linkUrl = platformCoupon?.linkUrl;
        }
    }

    @Override
    PlatformCouponsVO translate(CouponDO coupon) {
        if (!coupon) {
            return null;
        }

        def promotionTagsMap = [:]
        def promotionTags = MaitUtil.getMaitData(18353)
        promotionTags?.each {
            def activityName = it.get("activityName");
            if (activityName) {
                promotionTagsMap[activityName] = [
                        tagBgColor:  it.get("tagBgColor"),
                        tagTextColor: it.get("tagTextColor")
                ];
            }
        }

        List<PlatformCouponVO> coupons = coupon?.platformCoupon?.collect {
            PlatformCouponVO platformCoupon = new PlatformCouponVO(it);
            platformCoupon.with {
                tagBgColor = promotionTagsMap[iconTitle] ? promotionTagsMap[iconTitle]?.tagBgColor : "#FF5577";
                tagTextColor = promotionTagsMap[iconTitle] ? promotionTagsMap[iconTitle]?.tagTextColor : "#FFFFFF";
            }
            return platformCoupon;
        }
        return new PlatformCouponsVO(
                coupons: coupons
        );
    }
}