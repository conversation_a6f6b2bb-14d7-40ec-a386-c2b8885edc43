package groovy.mgj.xcx.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO


@Translator(id = "pintuanInfo", defaultValue = DefaultType.NULL)
class PintuanInfo implements IOneDependTranslator<ItemBaseDO, Object> {

    @Override
    Object translate(ItemBaseDO input1) {
        return null
    }
}
// import com.mogujie.detail.core.adt.DetailContextHolder
// import com.mogujie.detail.core.adt.RouteInfo
// import com.mogujie.detail.core.annotation.Translator
// import com.mogujie.detail.core.constant.BizType
// import com.mogujie.detail.core.translator.ITwoDependTranslator
// import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
// import com.mogujie.detail.module.pintuan.domain.PinTuanDO
// import com.mogujie.detail.module.sku.domain.SkuDO
// import com.mogujie.enzo.api.dto.UserInfo
// import groovy.mgj.xcx.base.ItemPriceVO

// /**
//  * Created by changsheng on 22/03/2017.
//  * H5私有模块-拼团详情页-拼团信息
//  */

// @Translator(id = "pintuanInfo")
// class PintuanInfo implements ITwoDependTranslator<PinTuanDO, ItemBaseDO, PintuanInfoVO> {

//     static class PintuanInfoVO {
//         Integer tuanType
//         Integer lotteryProcess
//         Integer awardNum
//         Integer tuanNum
//         Boolean isNew
//         Long countdown
//         Long startTime
//         Long endTime
//         Integer successTuanNum
//         Boolean isExpire
//         List<UserInfo> successTuan
//         List<UserInfo> joinTuanList
//         SkuDO skuInfo
//         String tips
//         String normalPrice
//         String pintuanPrice
//         Integer maxNumber
//         /**
//          * 拼团状态
//          * 1：已下架，2：普通+未开始，3：普通+已结束，4：普通+已抢光，5：正常拼团
//          */
//         int state
//         String currency = "¥"
//         Integer outType

//     }

//     @Override
//     PintuanInfoVO translate(PinTuanDO pinTuan, ItemBaseDO itemBase) {
//         if (!itemBase) {
//             return null;
//         }

//         ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

//         itemPrice.setNowPriceByRange(itemBase);
//         itemPrice.setOldPriceByRange(itemBase);

//         String tips = null;
//         Long countdown = null;
//         Integer state = 0;

//         /**
//          * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
//          */
//         Integer itemState = itemBase?.state;
//         SkuDO pintuanSkuInfo = pinTuan?.skuInfo;
//         Integer pintuanStock = pintuanSkuInfo?.totalStock;
//         Integer tuanNum = pinTuan?.tuanNum;
//         Integer tuanType = pinTuan?.tuanType;


//         Boolean isExpire = pinTuan?.isExpire;
//         Long now = System.currentTimeSeconds();
//         Long startTime = pinTuan?.startTime;
//         Long remainTime = pinTuan?.remainTime;

//         if (itemState == 3 || itemState == 1) {
//             state = 1
//         } else if (!pinTuan) {
//             state = 3
//         } else if (now < startTime) {
//             tips = "距开始仅剩：";
//             countdown = startTime - now;
//             // 1：均置灰，2：普通+未开始
//             // 活动未开始，只有普通商品下架，才展示下架，否则正常购买
//             state = 2
//         } else if (now > startTime && !isExpire) {
//             tips = "距结束仅剩："
//             countdown = remainTime

//             RouteInfo routeInfo =  DetailContextHolder.get().getRouteInfo()
//             if ((tuanType == 3 || routeInfo.bizType == BizType.NORMAL) && pintuanStock > 0) {
//                 // 抽奖团或者非渠道拼团不用判断成团人数
//                 state = 5
//             } else if (tuanType != 3 && routeInfo.bizType == BizType.PINTUAN && pintuanStock >= tuanNum) {
//                 // 5：正常，渠道拼团
//                 state = 5
//             } else {
//                 // 1：均置灰，4：普通+已抢光
//                 state = 4
//                 if (pintuanSkuInfo != null) {
//                     pintuanSkuInfo?.totalStock = 0
//                     pintuanSkuInfo?.skus?.each {
//                         it?.stock = 0
//                     }
//                 }
//             }
//         } else if (isExpire) {
//             // 1：均置灰，3：普通+已结束
//             state = 3
//         }

//         def pintuanInfoVO = new PintuanInfoVO(
//                 tuanType: pinTuan?.tuanType,
//                 lotteryProcess: pinTuan?.lotteryProcess,
//                 awardNum: pinTuan?.awardNum,
//                 tuanNum: pinTuan?.tuanNum ?: 0,
//                 isNew: pinTuan?.isNew,
//                 countdown: countdown,
//                 startTime: pinTuan?.startTime,
//                 endTime: pinTuan?.endTime,
//                 successTuanNum: pinTuan?.successTuanNum,
//                 joinTuanList: pinTuan?.joinTuanList?: [],
//                 isExpire: pinTuan?.isExpire,
//                 successTuan: pinTuan?.successTuan?: [],
//                 skuInfo: pintuanSkuInfo,
//                 tips: tips,
//                 normalPrice: itemPrice.nowPrice,
//                 pintuanPrice: pintuanSkuInfo?.lowNowPrice ?: itemPrice.nowPrice,
//                 state: state,
//                 maxNumber: pinTuan?.isLimit ? pinTuan?.limitNum : null,
//                 currency: itemBase?.currency ?: "¥",
//                 outType: pinTuan?.outType
//         );
//         return pintuanInfoVO
//     }
// }