package groovy.mgj.xcx.base

import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by chang<PERSON><PERSON> on 14/03/2017.
 * 公共VO-商品价格
 */

class ItemPriceVO {

    /**
     * 默认价格颜色
     */
    static final String defaultPriceColor = "#333333";
    static final String defaultEventPriceColor = "#FF2255";

    /**
     * 现价(区间价的最小值)
     */
    String nowPrice;

    /**
     * 原价(暂无区间价)
     */
    String oldPrice;

    /**
     * 现价(区间价的最大值)
     */
    String highNowPrice;

    /**
     * 区间价分隔符
     */
    String priceSplit;

    /**
     * 价格单位，默认为：¥
     */
    String currency;

    /**
     * 价格颜色，默认：#333333
     */
    String priceColor;

    /**
     * 价格后面的Tag列表
     */
    List<PriceTagVO> priceTags;

    /**
     * 价格前面的Tag(暂时只有一项)
     */
    PriceTagVO prePriceTag;

    /**
     * 第二行的活动价
     */
    String eventPrice;

    /**
     * 活动家后面的描述
     */
    PriceTagVO eventPriceDesc;

    /**
     * 活动价的颜色，默认：#FF2255
     */
    String eventPriceColor;

    /**
     * 第二行活动价后面的Tag列表
     */
    List<PriceTagVO> eventTags;

    /**
     * 手机专享价
     */
    String mobilePrice;

    /**
     * 手机专享价后面的引导下载URL
     */
    String mobileDownloadLink;

    ItemPriceVO(ItemBaseDO itemBase) {
        this.currency = itemBase?.currency ?: "¥";
        this.priceColor = defaultPriceColor;
        this.eventPriceColor = defaultEventPriceColor;
        this.priceSplit = "~"
    }

    /**
     * 普通取价格逻辑-计算原价
     * @param item
     * @return
     */
    public void setOldPriceByRange(ItemBaseDO item) {
        String lowPrice = item?.lowPrice;
        String highPrice = item?.highPrice;
        String lowNowPrice = item?.lowNowPrice;
        String highNowPrice = item?.highNowPrice;

        if (lowPrice == lowNowPrice && highPrice == highNowPrice){
            this.oldPrice = null
        } else {
            this.oldPrice = lowPrice
        }
    }

    /**
     * 无视现价获取原价，一般用于非普通详情页
     * @param item
     */
    public static String getOldPriceForce (ItemBaseDO item) {
        return item?.lowPrice;
    }

    /**
     * 普通取价格逻辑-计算现价
     * @param item
     * @return
     */
    public void setNowPriceByRange(ItemBaseDO item) {
        String lowNowPrice = item?.lowNowPrice;
        String highNowPrice = item?.highNowPrice;

        if (lowNowPrice != highNowPrice){
            this.nowPrice = lowNowPrice;
            this.highNowPrice = highNowPrice;
        } else {
            this.nowPrice = lowNowPrice
        }
    }
}