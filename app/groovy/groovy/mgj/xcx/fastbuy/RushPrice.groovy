package groovy.mgj.xcx.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.xcx.base.ItemPriceVO
import groovy.mgj.xcx.base.PriceTagVO
import groovy.mgj.xcx.base.Util

/**
 * Created by changsheng on 30/06/2017.
 * H5私有模块-快抢详情页-价格
 */
@Translator(id = "rushPrice")
class RushPrice implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, ItemPriceVO> {


    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, FastbuyDO fastbuy) {
        ItemPriceVO rushPrice = new ItemPriceVO(itemBase)

        rushPrice.with {
            if (itemBase) {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: "快抢价"
                        )
                ]
                if (fastbuy?.isNewComerItem()) {
                    priceTags.add(
                            new PriceTagVO(
                                    text: "新人专享"
                            )
                    )
                }
            } else {
                return null
            }
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(rushPrice)

        return rushPrice
    }
}
