package groovy.mgj.h5.xinrenxcx.xinren

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Create by jinger on 2020/03/17 14:59
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.NULL)
class ActivityInfo implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, Object> {

    static class ActivityInfoVO {
        String buttonText
        Boolean isDisabled
    }

    @Override
    ActivityInfoVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {
        Integer itemState = itemBase?.state

        Long startTime = channelInfo?.startTime
        Long endTime = channelInfo?.endTime
        Long nowTime = System.currentTimeSeconds()


        if (itemState == 1 || itemState == 2) {
            return new ActivityInfoVO(
                    buttonText: getBuyText(itemState),
                    isDisabled: true
            )
        }

        // 预售或者活动未开始
        if (nowTime < startTime || itemBase?.saleType == 1) {
            return new ActivityInfoVO(
                    buttonText: "未开始",
                    isDisabled: true
            )
        }

        if (nowTime < endTime && itemState == 0) {
            return new ActivityInfoVO(
                    buttonText: "立即购买",
                    isDisabled: false
            )
        }

        return new ActivityInfoVO(
                buttonText: "活动结束",
                isDisabled: true
        )
    }

    static private String getBuyText(Integer itemState) {
        switch (itemState) {
            case 0: return "立即购买"
            case 1: return "已下架"
            case 2: return "卖光啦"
            default: return "立即购买"
        }
    }
}