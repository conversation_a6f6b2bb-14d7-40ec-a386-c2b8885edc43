package groovy.mgj.h5.xinrenxcx.xinren

import com.mogujie.detail.core.adt.ChannelMeta
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by jinger on 17/03/2020.
 */

@Translator(id = "priceInfo")
class PriceInfo implements ITwoDependTranslator<ItemBaseDO, ChannelInfoDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ChannelInfoDO channelInfo) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {

            ChannelMeta channelMetaInfo = channelInfo?.channelMetaInfo

            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: channelMetaInfo?.priceDesc
                        )
                ]
            }

            Util.setEsiDataForPrice(itemPrice)

            // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
            itemPrice.isHideOldPrice(itemBase)

            return itemPrice
        }
    }
}