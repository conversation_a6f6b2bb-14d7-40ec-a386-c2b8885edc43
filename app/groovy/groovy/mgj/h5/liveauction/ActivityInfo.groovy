package groovy.mgj.h5.liveauction

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.auction.domain.AuctionDO

/**
 * Created by xiaoque on 2019-08-12.
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.NULL)
class ActivityInfo implements IOneDependTranslator<AuctionDO, ActivityInfoVO> {

    static class ActivityInfoVO {
        String biddingPrice // 竞价
        Boolean isWinner // 是否是竞拍者
        Integer status // 拍卖状态
        Integer quantity // 商品库存
        Boolean haveOrderQual // 资格是否被消耗
    }

    @Override
    ActivityInfoVO translate(AuctionDO auction) {
        if (!auction) {
            return null
        }

        return new ActivityInfoVO (
                biddingPrice: auction?.biddingPrice,
                isWinner: auction?.isWinner,
                status: auction?.status,
                quantity: auction?.quantity,
                haveOrderQual: auction?.haveOrderQual
        )
    }
}