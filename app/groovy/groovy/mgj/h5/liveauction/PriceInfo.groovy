package groovy.mgj.h5.liveauction

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by xiaoque on 2019-08-14.
 */

@Translator(id = "priceInfo")
class PriceInfo implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {

                String lowPrice = itemBase?.lowPrice
                String highPrice = itemBase?.highPrice

                if (lowPrice == highPrice) {
                    oldPrice = lowPrice
                } else {
                    oldPrice = "${lowPrice}起"
                }

                priceTags = [
                        new PriceTagVO(
                                text: itemBase?.discountDesc
                        )
                ]
            }
            Util.setEsiDataForPrice(itemPrice)
            return itemPrice
        }

    }
}