package groovy.mgj.h5.liveauction

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveType

/**
 * Created by xiaoque on 2019-08-19.
 */
@Translator(id = "liveInfo", defaultValue = DefaultType.NULL)
class LiveInfo implements IOneDependTranslator<LiveDO, LiveDO> {


    @Override
    LiveDO translate(LiveDO live) {
        if (!live) {
            return null
        }

        live.liveType = LiveType.LIVE_AUCTION

        return live
    }
}