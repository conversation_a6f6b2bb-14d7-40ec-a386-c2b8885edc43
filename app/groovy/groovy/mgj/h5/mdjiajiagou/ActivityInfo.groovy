package groovy.mgj.h5.mdjiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.modou.domain.ModouDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-蘑豆详情页(加价购)-蘑豆信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
public class ActivityInfo implements IThreeDependTranslator<ModouDO, PinTuanDO, ChannelInfoDO, ActivityInfoVO> {

    static class ActivityInfoVO {
        Long startTime
        Long endTime
        Long totalStock
        Long allStock
        Integer modouAmount
        Integer state
        String nowSaleLowPrice
        Boolean isPintuanLowPrice
    }

    @Override
    ActivityInfoVO translate(ModouDO modou, PinTuanDO pinTuan, ChannelInfoDO channelInfo) {
        if (!modou) {
            return null;
        }

        String nowSaleLowPrice = '';
        Boolean isPintuanLowPrice = false;

        if (channelInfo?.lowNormalPrice != null) {
            nowSaleLowPrice = NumUtil.formatNum(channelInfo.lowNormalPrice / 100D);
        }

        if (pinTuan?.tuanType == 7) {
            isPintuanLowPrice = true;
            nowSaleLowPrice = pinTuan.skuInfo?.lowNowPrice;
        }

        return new ActivityInfoVO(
            startTime: modou.startTime,
            endTime: modou.endTime,
            totalStock: modou.totalStock,
            allStock: channelInfo?.originTotalStock,
            modouAmount: modou.modouAmount,
            state: modou.state,
            nowSaleLowPrice: nowSaleLowPrice,
            isPintuanLowPrice: isPintuanLowPrice,
        )
    }
}