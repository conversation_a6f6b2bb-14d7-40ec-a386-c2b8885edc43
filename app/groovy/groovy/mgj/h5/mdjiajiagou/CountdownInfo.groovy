package groovy.mgj.h5.mdjiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.modou.domain.ModouDO
import groovy.mgj.h5.base.TopCountdownVO

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢倒计时
 */

@Translator(id = "countdownInfo", defaultValue = DefaultType.EMPTY_MAP)
class CountdownInfo implements IOneDependTranslator<ModouDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ModouDO modou) {
        if (!modou) {
            return null
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(111816)
        Map<String, Object> imgData = maitData?.get(0)

        Long startTime = modou?.startTime
        Long endTime = modou?.endTime

        def backgroundImg = imgData?.get("mdJiajiagouImage")
        def titleColor = imgData?.get("mdJiajiagouTitleColor")

        def nowTime = System.currentTimeSeconds()
        if (nowTime < startTime) {
            // 活动未开始
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            // 活动进行中
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: endTime - nowTime,
                    image: backgroundImg,
                    titleColor: titleColor
            )
        } else {
            return new TopCountdownVO()
        }
    }
}
