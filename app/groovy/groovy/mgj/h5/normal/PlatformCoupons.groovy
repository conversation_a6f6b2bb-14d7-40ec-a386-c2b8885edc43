package groovy.mgj.h5.normal

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.coupon.domain.CouponDO
import com.mogujie.detail.module.mycoupon.domain.MyCouponDO
import com.mogujie.detail.spi.dslutils.Tools


/**
 * Created by changshe<PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-部分商品优惠券
 */

@Translator(id = "platformCoupons")
class PlatformCoupons implements ITwoDependTranslator<CouponDO, MyCouponDO, CouponVO> {

    static class CouponVO extends CouponDO {
        Integer bonusCount = 0;
        Boolean isBonusItem = false;
    }

    @Override
    CouponVO translate(CouponDO input1, MyCouponDO mycoupon) {

        // 过滤掉新人券
        if (input1?.platformCouponV2?.size() > 0 && input1?.platformCouponV2?.first().forNewUser) {
            input1 = null
        }

        CouponVO coupon = JSON.parseObject(JSON.toJSONString(input1?: {}), CouponVO.class);

        coupon.isBonusItem = Tools.isBonusItem();

        if (coupon.isBonusItem) {
            coupon.bonusCount = mycoupon?.bonusCount?:0;
        }

        return coupon;
    }
}
