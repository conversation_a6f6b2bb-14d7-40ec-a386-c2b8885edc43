package groovy.mgj.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.shop.domain.ShopDO

/**
 * Created by enmeen on 2020/7/2.
 */
@Translator(id = "officialSelect", defaultValue = DefaultType.NULL)
class OfficialSelect implements IOneDependTranslator<ShopDO, Object> {

    @Override
    Object translate(ShopDO shopDO) {
        groovy.mgj.app.common.OfficialSelect appTranslator = new groovy.mgj.app.common.OfficialSelect()
        return appTranslator.translate(shopDO)
    }
}