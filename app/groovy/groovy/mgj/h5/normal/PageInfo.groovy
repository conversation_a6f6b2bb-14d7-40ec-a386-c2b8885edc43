package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import groovy.xcx.h5.base.PageInfoVO
import groovy.xcx.h5.base.Util
import groovy.xcx.h5.base.constant.SourceType

@Translator(id = "pageInfo")
class PageInfo implements ITwoDependTranslator<ItemBaseDO, LiveSimpleDO, PageInfoVO> {

    @Override
    PageInfoVO translate(ItemBaseDO itemBase,LiveSimpleDO liveSimple) {

        SourceType sourceType = Util?.getSourceType(DetailContextHolder.get())

        if (!liveSimple?.pickedExplainInfo && sourceType == SourceType.LIVE_WALL) {
            return new PageInfoVO(
                    redirectUrl: "//h5.mogu.com/detail-normal/index.html?itemId=${itemBase?.iid}",
                    redirectTips: "活动已经结束，正在为您跳转至新购买地址~"
            )
        } else {
            return new PageInfoVO()
        }
    }
}