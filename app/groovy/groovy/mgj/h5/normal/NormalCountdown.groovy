package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.constants.TuanType
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import groovy.mgj.h5.base.TopCountdownVO
import groovy.mgj.h5.base.Util


/**
 * 废弃了，请维护countdownInfo!!!!!!
 * 废弃了，请维护countdownInfo!!!!!!
 * 废弃了，请维护countdownInfo!!!!!!
 * 废弃了，请维护countdownInfo!!!!!!
 * 废弃了，请维护countdownInfo!!!!!!
 * 废弃了，请维护countdownInfo!!!!!!
 */

@Translator(id = "normalCountdown")
class NormalCountdown implements ISixDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, PresaleDO, NormalCountdownDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ActivityDO activity, GroupbuyingDO groupbuying, ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale, NormalCountdownDO normalCountDown) {

        def countdownInfo = normalCountDown?.countdownInfoMap?.get("xsbk");

        //倒计时优先级：大促>限时爆款>团购>拼团
        // 不管该商品有没有参加大促，只要在大促期间，倒计时氛围就会展示为大促样式
        if (activity?.countdown) {
            /**
             * 大促预热状态
             */
            if (activity?.activityState == 1) {
                // 纯文案，取warmUpTitle
                return new TopCountdownVO(
                        text: activity?.warmUpTitle,
                        countdown: activity?.countdown,
                        image: activity?.activityPreImage,
                        isCountdomShow: false
                )
            } else {
                /**
                 * 大促正式状态
                 */

                // 大促正式，预售商品取的旧字段
                if (presale) {
                    return new TopCountdownVO(
                            text: "距结束仅剩",
                            countdown: activity?.countdown,
                            image: activity?.activityInImage,
                            titleColor: activity?.activityInTitleColor
                    )
                } else {
                    return new TopCountdownVO(
                            text: "距结束仅剩",
                            countdown: activity?.countdown,
                            image: activity?.activityInImage1110,
                            titleColor: activity?.endTimeHintColor,
                            priceColor: activity?.endTimeHintColor,
                            isPriceShow: true,
                            titleIcon: activity?.activityTitleImage,
                            activityBanner: activity?.activitySphereImage,
                    )
                }
            }
        } else if (countdownInfo) {
            // 限时爆款
            def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(countdownInfo.maitId1110);
            Map<String, Object> md = maitData?.get(0);

            if (isWarmUp) {
                return new TopCountdownVO(
                        text: "距开始仅剩",
                        countdown: countdownInfo.countdown,
                        image: md?.get("coverBg"),
                        titleColor: md?.get("countdownColor"),
                )
            } else {
                return new TopCountdownVO(
                        text: "距结束仅剩",
                        countdown: countdownInfo.countdown,
                        image: md?.get("coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isPriceShow: true
                )
            }
        } else if (groupbuying && groupbuying?.status == TuanStatus.IN && groupbuying?.endTime) {
            //读取团购倒计时背景麦田资源

            /**
             * 团购正式状态
             */

            List<Map<String, Object>> maitData = MaitUtil.getMaitData(123204);
            Map<String, Object> md = maitData?.get(0);

            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: groupbuying.endTime - System.currentTimeSeconds(),
                    image: md?.get("coverBg"),
                    titleColor: md?.get("titleColor"),
                    isPriceShow : true,
                    priceColor: md?.get("priceColor"),
                    titleIcon: md?.get("titleIcon"),
                    activityBanner: md?.get("activityBanner"),
            )
        } else if (groupbuying && groupbuying?.status == TuanStatus.PRE && groupbuying?.startTime) {

            /**
             * 团购预热状态
             */
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(43182);
            Map<String, Object> md = maitData?.get(0);

            GroupBuyImageAndTitleColorInfo info = new GroupBuyImageAndTitleColorInfo(groupbuying, md);

            // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
            if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                return getPintuanCountdown(pinTuan)
            } else {
                return new TopCountdownVO(
                        text: "距开始仅剩",
                        countdown: groupbuying.startTime - System.currentTimeSeconds(),
                        image: ImageUtil.img(info?.image),
                        titleColor: info?.titleColor
                )
            }
        } else if (Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
            /**
             * 非渠道拼团-招商拼团(非招商的没有氛围)
             */
            return getPintuanCountdown(pinTuan)
        } else {
            return new TopCountdownVO()
        }
    }

    static TopCountdownVO getPintuanCountdown(PinTuanDO pinTuan) {
        Boolean isExpire = pinTuan?.isExpire
        Long now = System.currentTimeSeconds()
        Long startTime = pinTuan?.startTime
        Long remainTime = pinTuan?.remainTime

        if (!pinTuan) {
            return new TopCountdownVO()
        } else if (now < startTime) {
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(56776);
            Map<String, Object> md = maitData?.get(0);
            /**
             * 非渠道拼团预热状态（不存在这种情况，不过代码还没删）
             */
            return new TopCountdownVO(
                    text: "距开始仅剩",
                    countdown: startTime - now,
                    image: md?.get("normalPintuanImage"),
                    titleColor: md?.get("normalPintuanTitleColor"),
            )
        } else if (now > startTime && !isExpire) {
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(123206);
            Map<String, Object> md = maitData?.get(0);
            /**
             * 拼团正式状态
             */
            return new TopCountdownVO(
                    text: "距结束仅剩",
                    countdown: remainTime,
                    image: md?.get("coverBg"),
                    titleColor: md?.get("countdownColor"),
                    isPriceShow : true,
                    priceColor:md?.get("priceColor"),
                    titleIcon: md?.get("titleIcon"),
                    activityBanner: md?.get("activityBanner"),
            )
        } else {
            return new TopCountdownVO()
        }
    }

    static enum GroupBuyType {
        NORMAL,
        UZHI,
        STORE,
        PINPAI

        static public GroupBuyType getGroupBuyType(GroupbuyingDO groupbuyingDO) {
            if (groupbuyingDO.bizType == TuanBizType.UZHI) {
                return UZHI
            } else if (groupbuyingDO.type == TuanType.STORE) {
                return STORE
            } else if (groupbuyingDO.bizType == TuanBizType.PINPAI) {
                return PINPAI
            } else if (groupbuyingDO.bizType == TuanBizType.NORMAL) {
                return NORMAL
            }
        }
    }


    /**
     * 团购预热状态
     */
    static class GroupBuyImageAndTitleColorInfo {
        String image
        String titleColor

        /**
         * 一般团购业务  NORMAL 优质团  UZHI 品牌团  PINPAI
         *
         */

        public GroupBuyImageAndTitleColorInfo(GroupbuyingDO groupbuyingDO, Map<String, Object> imgData) {
            if (!imgData) {
                return
            }
            GroupBuyType groupBuyType = GroupBuyType.getGroupBuyType(groupbuyingDO)
            if (groupBuyType == GroupBuyType.UZHI) {
                image = imgData?.get("tuanUZhiImage")
                titleColor = imgData?.get("tuanUZhiTitleColor")
            } else if (groupBuyType == GroupBuyType.STORE) {

                image = imgData?.get("tuanInStoreImage")
                titleColor = imgData?.get("tuanInStoreTitleColor")
            } else if (groupBuyType == GroupBuyType.PINPAI) {
                image = imgData?.get("tuanPinpaiImage")
                titleColor = imgData?.get("tuanPinpaiTitleColor")
            } else if (groupBuyType == GroupBuyType.NORMAL) {
                image = imgData?.get("tuanNormalImage")
                titleColor = imgData?.get("tuanNormalTitleColor")
            }
        }
    }
}