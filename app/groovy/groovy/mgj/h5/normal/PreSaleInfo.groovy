package groovy.mgj.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.presale.domain.PresaleDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 22/03/2017.
 * H5私有模块-普通详情页-预售信息
 */

@Translator(id = "preSaleInfo", defaultValue = DefaultType.EMPTY_STRING)
class PreSaleInfo implements IOneDependTranslator<PresaleDO, PresaleDO> {

    @Override
    PresaleDO translate(PresaleDO input1) {
        if (!input1) {
            return null
        }
        return input1;
    }
}