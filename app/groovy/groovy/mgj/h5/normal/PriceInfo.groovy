package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util
import groovy.mgj.h5.normal.utils.ActivityManager
import groovy.mgj.h5.normal.utils.ActivityType

import java.lang.reflect.Array

/**
 * Created by changsheng on 14/03/2017.
 * H5私有模块-普通详情页-价格
 */

@Translator(id = "priceInfo")
class PriceInfo implements INineDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, NormalCountdownDO, ExtraDO, SkuDO, LiveSimpleDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, NormalCountdownDO normalCountDown, ExtraDO extra, SkuDO sku, LiveSimpleDO liveSimpleDO) {
        if (!itemBase) {
            return null;
        }


        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountDown, liveSimpleDO)

        switch (activityType) {
            case ActivityType.LIVE_WALL_ITEM:
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(144975);
                Map<String, Object> data = maitData?.get(0);

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    priceTags = [
                            new PriceTagVO(
                                    text: data?.get("priceDesc")
                            )
                    ]
                }
                break

            case ActivityType.PRESALE:
                itemPrice.with {
                    nowPrice = presale?.totalPrice?.replace("¥", "");
                    setOldPriceByRange(itemBase);
                    priceTags = [
                            new PriceTagVO(
                                    text: "预售价"
                            )
                    ];
                    eventPrice = presale?.deposit;
                    eventPriceDesc = new PriceTagVO(
                            text:  "定金"
                    )
                    if (presale?.expandMoney) {
                        eventTags = [
                                new PriceTagVO(
                                        text: "抵" + presale?.expandMoney
                                )
                        ]
                    }

                }
                break


            case ActivityType.DACU_IN:
            case ActivityType.DACU_PRE:
                itemPrice.with {
                    setNowPriceByRange(itemBase);
                    setOldPriceByRange(itemBase);
                    priceTags = activity?.priceDesc ? [
                            new PriceTagVO(
                                    text: activity?.priceDesc
                            )
                    ] : []
                    eventPrice = activity?.warmUpPrice?.price?.replace("¥", "")
                    eventPriceColor = activity?.warmUpPrice?.color
                    if (eventPrice) {
                        eventPriceDesc = new PriceTagVO(
                                text: activity?.warmUpPrice?.priceDesc,
                                bgColor: "#FFFFFF" //无背景色
                        )
                    }
                }
                break


            case ActivityType.XSBK_IN:
            case ActivityType.XSBK_PRE:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("xsbk");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(countdownInfo.maitId);
                Map<String, Object> data = maitData?.get(0);

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)
                    priceTags = [
                            new PriceTagVO(
                                    text: data?.get(isWarmUp ? "prePriceDesc": "priceDesc")
                            )
                    ]
                }
                break

            case ActivityType.XINPIN_IN:
            case ActivityType.XINPIN_PRE:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("xinp");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(127452);
                Map<String, Object> data = maitData?.get(0);

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    // 预热期要取预热价
                    if (countdownInfo?.state == CountdownState.WARM_UP) {
                        eventPrice = NumUtil.formatNum(countdownInfo?.price / 100D)
                        eventPriceDesc = new PriceTagVO(
                                text: data?.get("prePriceDesc")
                        )
                    } else if (countdownInfo?.state == CountdownState.IN_ACTIVITY) {
                        priceTags = [
                                new PriceTagVO(
                                        text: data?.get("priceDesc")
                                )
                        ]
                    }
                }
                break

            case ActivityType.SHANGOU_IN:
            case ActivityType.SHANGOU_PRE:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("shango");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                def shangoMaitId = countdownInfo?.maitId1110
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(shangoMaitId);
                Map<String, Object> data = maitData?.get(0);

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    // 预热期要取预热价
                    if (countdownInfo?.state == CountdownState.WARM_UP) {
                        eventPrice = NumUtil.formatNum(countdownInfo?.price / 100D)
                        eventPriceDesc = new PriceTagVO(
                                text: data?.get("prePriceDesc")
                        )
                    } else if (countdownInfo?.state == CountdownState.IN_ACTIVITY) {
                        priceTags = [
                                new PriceTagVO(
                                        text: data?.get("priceDesc")
                                )
                        ]
                    }
                }
                break

            case ActivityType.LIVESECKILL_IN:
            case ActivityType.LIVESECKILL_PRE:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("ltg");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(140436);
                Map<String, Object> data = maitData?.get(0);

                itemPrice.with {
                    setNowPriceByRange(itemBase)
                    setOldPriceByRange(itemBase)

                    // 预热期要取预热价
                    if (countdownInfo?.state == CountdownState.WARM_UP) {
                        eventPrice = NumUtil.formatNum(countdownInfo?.price / 100D)
                        eventPriceDesc = new PriceTagVO(
                                text: data?.get("prePriceDesc")
                        )
                    } else if (countdownInfo?.state == CountdownState.IN_ACTIVITY) {
                        priceTags = [
                                new PriceTagVO(
                                        text: data?.get("priceDesc")
                                )
                        ]
                    }
                }
                break


            case ActivityType.TUANGOU_PRE:
            case ActivityType.TUANGOU_IN:
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(123204);
                String groupbuyingText = maitData?.get(0)?.get("priceTag");
                itemPrice.with {
                    setNowPriceByRange(itemBase);
                    setOldPriceByRange(itemBase);

                    if (groupbuying.status == TuanStatus.IN) {
                        priceTags = [
                                new PriceTagVO(
                                        text: groupbuyingText
                                )
                        ]
                    } else if (groupbuying.status == TuanStatus.PRE) {
                        eventPrice = groupbuying?.price
                        eventPriceDesc = new PriceTagVO(
                                text: groupbuyingText
                        )

                        // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                        if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                            eventPriceDesc.text = ""
                        }
                    }
                }
                break


            case ActivityType.PINTUAN_IN:
            case ActivityType.PINTUAN_PRE:
            case ActivityType.SALEONTIME:
            default:
                itemPrice.with {
                    setNowPriceByRange(itemBase);
                    setOldPriceByRange(itemBase);
                    priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                }
                break
        }



        // 有拼团就要多一个展示拼团价（前端对应会有两个按钮，一个是拼团的按钮，一个是普通按钮［团购／限时爆款］），但氛围还是以团购、限时爆款优先
        // 比如：如果一个商品既是拼团又是限时爆款，那会展示拼团价和单独购买价（限时爆款价），然后氛围展示的是限时爆款的
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            itemPrice.with {
                nowPrice = pinTuan?.skuInfo?.lowNowPrice
                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                oldPrice = getOldPriceForce(itemBase)
            }
        }

        List<PriceTagVO> items = []

        if (activity?.eventTags) {
            items = activity.eventTags.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }
        }

        if (itemPrice.eventTags) {
            itemPrice.eventTags.addAll(items)
        } else {
            // 统一的活动Tag
            itemPrice.eventTags = items
        }

        itemPrice.with {
            // 手机专享价
            mobilePrice = itemBase?.extra?.mobilePrice
            mobileDownloadLink = itemBase?.extra?.mobileDownloadLink
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        itemPrice.isHideOldPrice(itemBase)


        return itemPrice;
    }

    static List<PriceTagVO> getDefaultPriceTags(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            return [
                    new PriceTagVO(
                            text: "拼团价"
                    )
            ]
        } else if (itemBase?.discountDesc) {
            return [
                    new PriceTagVO(
                            text: itemBase?.discountDesc
                    )
            ]
        } else {
            return []
        }

    }

}