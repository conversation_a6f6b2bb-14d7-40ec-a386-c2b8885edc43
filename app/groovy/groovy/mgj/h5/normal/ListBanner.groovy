package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IZeroDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.spi.mgj.iteminfo.domain.BaseBanner
import com.mogujie.metabase.spring.client.MetabaseClient
import com.mogujie.metabase.utils.CollectionUtils
import com.mogujie.tesla.common.util.StringUtils

import javax.annotation.Resource
/**
 * Created by x<PERSON>oya<PERSON> on 17/3/24.
 */
@Translator(id = "listBanner")
class ListBanner implements IZeroDependTranslator<ListBannerVO> {

    private static final long MID_BANNER_INFO = 2182L;

    private static final long RED_BANNER_CODE = 2099L;

    public static final long H5_DOWNLOAD_BANNER_CODE = 5694L; // H5 详情资源为下载banner位


    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient;

    static class EventBanner {
        private String icon;
        private String info;
        private String link;
    }

    static class ListBannerVO {
        private String bgImg;
        private String link;
        private EventBanner eventBanner;
        private BaseBanner downloadBanner;

        boolean isEmpty() {
            return StringUtils.isEmpty(bgImg) && org.springframework.util.StringUtils.isEmpty(link) && eventBanner == null && CollectionUtils.isEmpty(list) && (null == downloadBanner || downloadBanner.isEmpty());
        }
    }

    @Override
    ListBannerVO translate() {
        ListBannerVO bannerInfo = new ListBannerVO();
        bannerInfo.bgImg = getBigImage();
        Map<Long, List<Map<String, Object>>> resources = MaitUtil.batchGetMatiData(Arrays.asList(MID_BANNER_INFO, RED_BANNER_CODE));
        EventBanner eventBanner = getBanner(resources);
        // 活动banner
        bannerInfo.eventBanner = eventBanner;

        if (!CollectionUtils.isEmpty(resources)) {
            // 设置中部大banner
            this.setMidBannerInfo(bannerInfo, resources.get(MID_BANNER_INFO));
        }
        bannerInfo.downloadBanner = getDownloadBanner();
        if (bannerInfo.isEmpty()) {
            return null;
        }
        return bannerInfo


    }

    protected String getBigImage() {
        try {
            RouteInfo route = DetailContextHolder.get().getRouteInfo();
            String key = "listbanner_" + route.getApp() + "_" + route.getPlatform() + "_" + route.getBizType();
            String image = metabaseClient.get(key);
            if (!StringUtils.isEmpty(image)) {
                return ImageUtil.img(image);
            }
        } catch (Throwable e) {
        }
        return null;
    }

    private BaseBanner getDownloadBanner() {
        List<Map<String, Object>> resources = MaitUtil.getMaitData(H5_DOWNLOAD_BANNER_CODE);
        BaseBanner downloadBanner = getH5DownloadBanner(resources);
        return downloadBanner;
    }

    /**
     * 获取h5下载bannber资源位
     * @param resources
     * @return
     */
    BaseBanner getH5DownloadBanner(List<Map<String, Object>> resources) {
        if (com.mogujie.tesla.common.CollectionUtils.isEmpty(resources)) {
            return null;
        }

        BaseBanner baseBanner = new BaseBanner();
        Map<String, Object> res = resources.get(0);
        Object bgImg = res.get("midBgImg");
        Object link = res.get("midLink");

        baseBanner.setBgImg(bgImg == null ? null : bgImg.toString());
        baseBanner.setLink(link == null ? null : link.toString());
        baseBanner.setAcm((String)res.get("acm"));
        return baseBanner;
    }


    protected static EventBanner getBanner(Map<Long, List<Map<String, Object>>> resources) {
        if (com.mogujie.tesla.common.CollectionUtils.isEmpty(resources)) {
            return null;
        }

        List<Map<String, Object>> redBannerRes = resources.get(RED_BANNER_CODE);
        if (com.mogujie.tesla.common.CollectionUtils.isEmpty(redBannerRes)) {
            return null;
        }

        EventBanner banner = new EventBanner();
        Map<String, Object> res = redBannerRes.get(0);
        Object icon = res.get("icon");
        Object info = res.get("info");
        Object link = res.get("link");

        banner.icon = (icon == null ? null : icon.toString());
        banner.info = (info == null ? null : info.toString());
        banner.link = (link == null ? null : link.toString());
        return banner;
    }

    /**
     * 非海淘商品设置中部大banner
     * @return
     */
    protected boolean setMidBannerInfo(ListBannerVO bannerInfo, List<Map<String, Object>> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return false;
        }

        Map<String, Object> res = resources.get(0);
        Object bgImg = res.get("bannerImg");
        Object link = res.get("bannerLink");


        if (bgImg == null) {
            return false;
        }

        bannerInfo.bgImg = bgImg.toString();
        bannerInfo.link(link == null ? null : link.toString());
        return true;
    }


}
