package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ISixDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.Util


/**
 * 废弃了，请维护priceInfo!!!!!!
 * 废弃了，请维护priceInfo!!!!!!
 * 废弃了，请维护priceInfo!!!!!!
 * 废弃了，请维护priceInfo!!!!!!
 * 废弃了，请维护priceInfo!!!!!!
 * 废弃了，请维护priceInfo!!!!!!
 */

@Translator(id = "normalPrice")
class NormalPrice implements ISixDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, PinTuanDO, NormalCountdownDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying, PinTuanDO pinTuan, NormalCountdownDO normalCountDown) {
        if (!itemBase) {
            return null;
        }
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);
        def countdownInfo = normalCountDown?.countdownInfoMap?.get("xsbk");

        /**
         * 氛围优先级：预售>大促>限时爆款>团购>拼团
         * 价格优先级：拼团>预售>大促>限时爆款>团购
         */
        if (presale != null) {
            // 预售
            itemPrice.with {
                nowPrice = presale?.totalPrice?.replace("¥", "");
                setOldPriceByRange(itemBase);
                priceTags = [
                        new PriceTagVO(
                                text: "预售价"
                        )
                ];
                eventPrice = presale?.deposit;
                eventPriceDesc = new PriceTagVO(
                        text:  "定金"
                )
            }
        } else if (activity != null && (activity.warmUpPrice || activity.inActivityItem)) {
            // 大促
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                priceTags = activity?.priceDesc ? [
                        new PriceTagVO(
                                text: activity?.priceDesc
                        )
                ] : []
                eventPrice = activity?.warmUpPrice?.price
                eventPriceColor = activity?.warmUpPrice?.color
                if (eventPrice) {
                    eventPriceDesc = new PriceTagVO(
                            text: activity?.warmUpPrice?.priceDesc,
                            bgColor: "#FFFFFF" //无背景色
                    )
                }
            }
        } else if (countdownInfo) {
            // 限时爆款价格标签
            def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
            List<Map<String, Object>> maitData = MaitUtil.getMaitData(countdownInfo.maitId);
            Map<String, Object> data = maitData?.get(0);

            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: data?.get(isWarmUp ? "prePriceDesc": "priceDesc")
                        )
                ]
            }

        } else if (groupbuying != null && (groupbuying.status == TuanStatus.IN || groupbuying.status == TuanStatus.PRE)) {
            // 团购
            // 都展示为团购价
            String groupbuyingText = "团购价"
//            if (groupbuying.bizType == TuanBizType.UZHI) {
//                 groupbuyingText = "U质团"
//            }
//            else if (groupbuying.bizType == TuanBizType.PINPAI) {
//                 groupbuyingText = "品牌团"
//            }

            if (groupbuying.status == TuanStatus.IN) {
                // 团购正式
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    } else {
                        nowPrice = groupbuying?.price;
                    }

                    priceTags = [
                            new PriceTagVO(
                                    text: groupbuyingText
                            )
                    ]
                }

            } else if (groupbuying.status == TuanStatus.PRE) {
                // 团购预热
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    }
                    // priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
                    eventPrice = groupbuying?.price;
                    eventPriceDesc = new PriceTagVO(
                            text:  groupbuyingText
                    )

                    // U质团的预告期间，如果商品正在非渠道招商拼团正式期，则优先展示非渠道拼团活动氛围，且不展示U质团价格预告；否则展示U质团氛围与价格预告
                    if (groupbuying.bizType == TuanBizType.UZHI && Util.isPintuanItem(itemBase, pinTuan, presale) && pinTuan?.system) {
                        eventPriceDesc.text = ""
                    }
                }
            }
        } else {
            // 普通
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                priceTags = getDefaultPriceTags(itemBase, pinTuan, presale)
            }
        }

        // 有拼团就要多一个展示拼团价（前端对应会有两个按钮，一个是拼团的按钮，一个是普通按钮［团购／限时爆款］），但氛围还是以团购、限时爆款优先
        // 比如：如果一个商品既是拼团又是限时爆款，那会展示拼团价和单独购买价（限时爆款价），然后氛围展示的是限时爆款的
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            itemPrice.with {
                nowPrice = pinTuan?.skuInfo?.lowNowPrice
                highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                oldPrice = getOldPriceForce(itemBase)
            }
        }

        itemPrice.with {
            // 统一的活动Tag
            eventTags = activity?.eventTags?.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }

            // 手机专享价
            mobilePrice = itemBase?.extra?.mobilePrice
            mobileDownloadLink = itemBase?.extra?.mobileDownloadLink
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)


        return itemPrice;
    }

    static List<PriceTagVO> getDefaultPriceTags(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        if (Util.isPintuanItem(itemBase, pinTuan, presale)) {
            return [
                    new PriceTagVO(
                            text: "拼团价"
                    )
            ]
        } else if (itemBase?.discountDesc) {
            return [
                    new PriceTagVO(
                            text: itemBase?.discountDesc
                    )
            ]
        } else {
            return []
        }

    }

}