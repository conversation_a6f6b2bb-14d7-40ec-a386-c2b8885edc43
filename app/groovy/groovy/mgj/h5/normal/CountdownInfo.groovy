package groovy.mgj.h5.normal

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IEightDependTranslator
import com.mogujie.detail.core.translator.INineDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.normalCountdown.domain.CountdownState
import com.mogujie.detail.module.normalCountdown.domain.NormalCountdownDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.h5.base.CommonBannerVO
import groovy.mgj.h5.base.TopCountdownVO
import groovy.mgj.h5.normal.utils.ActivityManager
import groovy.mgj.h5.normal.utils.ActivityType

/**
 * Created by changsheng on 22/03/2017.
 * H5私有模块-普通详情页-活动氛围&倒计时
 */

@Translator(id = "countdownInfo")
class CountdownInfo implements INineDependTranslator<ActivityDO, GroupbuyingDO, ItemBaseDO, PinTuanDO, PresaleDO, NormalCountdownDO, ExtraDO, SkuDO, LiveSimpleDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(ActivityDO activity, GroupbuyingDO groupbuying, ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale, NormalCountdownDO normalCountDown, ExtraDO extra, SkuDO sku, LiveSimpleDO liveSimpleDO) {

        ActivityType activityType = ActivityManager.getActivityType(itemBase, activity, presale, groupbuying, pinTuan, extra, sku, normalCountDown, liveSimpleDO)

        switch (activityType) {
            case ActivityType.LIVE_WALL_ITEM:
                def isWarmUp = false;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(144975);
                Map<String, Object> md = maitData?.get(0);
                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        image: md?.get(isWarmUp ? "preCoverBg" : "coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        activityBannerLink: md?.get("h5ActivityBannerLink"),
                        isWarmUp: isWarmUp,
                        isHideRightCountdown: true
                )
                break

            case ActivityType.DACU_PRE:
            case ActivityType.DACU_IN:
                Boolean isWarmUp = activityType == ActivityType.DACU_PRE;
                String image = isWarmUp ? activity?.preActivityInImage1110 : activity?.activityInImage1110;
                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: activity?.countdown,
                        image: image,
                        titleColor: activity?.endTimeHintColor,
                        priceColor: activity?.endTimeHintColor,
                        titleIcon: activity?.activityTitleImage,
                        activityBanner: activity?.activitySphereImage,
                        isWarmUp: isWarmUp
                )


            case ActivityType.XSBK_PRE:
            case ActivityType.XSBK_IN:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("xsbk");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(countdownInfo.maitId1110);
                Map<String, Object> md = maitData?.get(0);


                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: countdownInfo.countdown,
                        image: md?.get("coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isWarmUp: isWarmUp
                )


            case ActivityType.XINPIN_PRE:
            case ActivityType.XINPIN_IN:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("xinp");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(127452);
                Map<String, Object> md = maitData?.get(0);

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: countdownInfo.countdown,
                        image: md?.get(isWarmUp ? "preCoverBg" : "coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isWarmUp: isWarmUp
                )

            case ActivityType.SHANGOU_PRE:
            case ActivityType.SHANGOU_IN:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("shango");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                def shangoMaitiId = countdownInfo?.nbt == '117' ? Long.valueOf('152342') : countdownInfo?.maitId1110
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(shangoMaitiId);
                Map<String, Object> md = maitData?.get(0);

                String needPreCountDown = md?.get("needPreCountDown") != null ? String.valueOf(md?.get("needPreCountDown")) : "1"
                String needCountDown = md?.get("needCountDown") != null ? String.valueOf(md?.get("needCountDown")) : "1"

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: countdownInfo.countdown,
                        image: md?.get(isWarmUp ? "preCoverBg" : "coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        activityBannerLink: md?.get("h5ActivityBannerLink"),
                        isWarmUp: isWarmUp,
                        priceHistoryInfo: getPriceHistoryInfo(countdownInfo),
                        isHideRightCountdown: isWarmUp ? needPreCountDown != "1" : needCountDown != "1"
                )

            case ActivityType.LIVESECKILL_PRE:
            case ActivityType.LIVESECKILL_IN:
                def countdownInfo = normalCountDown?.countdownInfoMap?.get("ltg");
                def isWarmUp = countdownInfo.state == CountdownState.WARM_UP;
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(140436);
                Map<String, Object> md = maitData?.get(0);

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: countdownInfo.countdown,
                        image: md?.get(isWarmUp ? "preCoverBg" : "coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isWarmUp: isWarmUp
                )


            case ActivityType.TUANGOU_PRE:
            case ActivityType.TUANGOU_IN:
                Boolean isWarmUp = activityType == ActivityType.TUANGOU_PRE
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(123204);
                Map<String, Object> md = maitData?.get(0);

                Long nowTime = System.currentTimeSeconds();

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: (isWarmUp ? groupbuying.startTime : groupbuying.endTime) - nowTime,
                        image: md?.get(isWarmUp ? "preCoverBg" : "coverBg"),
                        titleColor: md?.get("titleColor"),
                        priceColor: md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isWarmUp: isWarmUp
                )


            case ActivityType.PINTUAN_PRE:
            case ActivityType.PINTUAN_IN:
                /**
                 * 非渠道拼团-招商拼团(非招商的没有氛围)
                 * 不存在非渠道拼团预热这种情况，不过代码先放着
                 */
                List<Map<String, Object>> maitData = MaitUtil.getMaitData(123206);
                Map<String, Object> md = maitData?.get(0);

                Long nowTime = System.currentTimeSeconds();
                Long startTime = pinTuan?.startTime;
                Boolean isWarmUp = nowTime < startTime;
                Long countdown = isWarmUp ? startTime - nowTime : pinTuan?.remainTime

                return new TopCountdownVO(
                        businessType: activityType.toString(),
                        countdown: countdown,
                        image: md?.get("coverBg"),
                        titleColor: md?.get("countdownColor"),
                        priceColor:md?.get("priceColor"),
                        titleIcon: md?.get("titleIcon"),
                        activityBanner: md?.get("activityBanner"),
                        isWarmUp: isWarmUp
                )


            default:
                return new TopCountdownVO(
                        businessType: activityType.toString()
                )
        }

    }

    // 获取价格趋势图，后端会根据kv标来控制是否透出价格趋势图
    static CommonBannerVO getPriceHistoryInfo(def countdownInfo) {
        if (countdownInfo?.priceHistoryImg) {
            return new CommonBannerVO(
                    image: countdownInfo?.priceHistoryImg
            )
        }
        return new CommonBannerVO()
    }
}