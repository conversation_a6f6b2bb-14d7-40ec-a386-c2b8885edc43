package groovy.mgj.h5.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.coupon.domain.CouponDO
import groovy.mgj.app.vo.CrossShopDiscountVO

/**
 * Created by pananping on 2020/9/22.
 */
@Translator(id = "crossShopDiscount", defaultValue = DefaultType.NULL)
class CrossShopDiscount implements IOneDependTranslator<CouponDO, CrossShopDiscountVO> {

    @Override
    CrossShopDiscountVO translate(CouponDO couponDO) {
        CrossShopDiscountVO ret = new groovy.mgj.app.common.CrossShopDiscount().translate(couponDO)
        if (ret?.link) {
            ret.link = ""
        }
        return ret
    }

}