package groovy.mgj.h5.livelottery

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<ChannelInfoDO, ChannelInfoDO>{

    @Override
    ChannelInfoDO translate(ChannelInfoDO input1) {
        return input1;
    }

}
