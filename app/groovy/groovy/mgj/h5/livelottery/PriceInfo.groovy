package groovy.mgj.h5.livelottery

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by changsheng on 19/09/2017.
 */

@Translator(id = "priceInfo")
class PriceInfo implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                priceTags = [
                        new PriceTagVO(
                                text: '福利价，可兑换1件'
                        )
                ]
            }

            Util.setEsiDataForPrice(itemPrice)

            return itemPrice
        }
    }
}