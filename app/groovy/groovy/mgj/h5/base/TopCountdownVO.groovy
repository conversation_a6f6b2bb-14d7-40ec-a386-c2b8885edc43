package groovy.mgj.h5.base

/**
 * Created by ch<PERSON><PERSON><PERSON> on 30/06/2017.
 * 公共VO-顶部倒计时
 */
class TopCountdownVO {

    // 业务类型，方便前端调试和区分目前的详情页活动类型
    String businessType = ''
    // 是否预热
    Boolean isWarmUp = false


    // 服务器目前时间
    Long nowTime = System.currentTimeSeconds()
    // 开始时间
    Long startTime
    // 结束时间
    Long endTime
    Long countdown = 0


    /* 参见麦田字段http://mait.mogujie.org/recordEntry?definitionId=123209&tab=prepare */
    // 背景图片
    String image = ""
    // 文案颜色
    String titleColor = "#FFFFFF"
    // 价格相关颜色（包括价格标签）
    String priceColor = '#FFFFFF'
    // 商品活动banner
    String activityBanner = ''
    // 商品活动banner跳转链接
    String activityBannerLink = ''
    // 标题前icon
    String titleIcon = ''

    // 价格趋势图
    CommonBannerVO priceHistoryInfo

    Boolean isHideRightCountdown = false    // 是否隐藏右侧倒计时（搭配购正式氛围）



    /////////// 后续计划：activityBanner是否展示的判断放在前端，尽量减少DSL层对isWarmUp的判断

    // 目前只有大促预热可能会用到，准备废弃
    @Deprecated String text = ""
    // 是否展示倒计时，false只显示背景图片，不显示文案和倒计时
    @Deprecated Boolean isCountdomShow = true
    // 是否显示是价格&标签
    @Deprecated Boolean isPriceShow = false




}
