package groovy.mgj.h5.base

import com.mogujie.detail.core.util.MetabaseTool
import com.alibaba.fastjson.JSONObject
import com.mogujie.commons.utils.EnvUtil
import com.mogujie.detail.core.adt.DetailContext
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.service.waitress.platform.common.ServiceDetailEnum
import com.mogujie.service.waitress.platform.domain.entity.ItemServiceDetail
import groovy.xcx.h5.base.constant.SourceType
import groovy.xcx.h5.base.constant.TagKey
import groovy.xcx.h5.base.constant.XCXType
import org.apache.commons.lang3.StringUtils
import org.apache.commons.collections4.CollectionUtils
import com.mogujie.detail.module.shop.util.WaitressUtil

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.mogujie.detail.core.adt.DetailContext
import org.apache.http.util.TextUtils

import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * Created by changsheng on 13/09/2017.
 */

class Util  {

    static void setEsiDataForPrice (ItemPriceVO itemPrice) {
        itemPrice.with {
            if (nowPrice == null) {
                nowPrice = ""
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (highNowPrice == null) {
                highNowPrice = ""
            }
            if (priceTags == null) {
                priceTags = []
            }
            if (prePriceTag == null) {
                prePriceTag = new PriceTagVO()
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (eventPriceDesc == null) {
                eventPriceDesc = new PriceTagVO()
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (mobilePrice == null) {
                mobilePrice = ""
            }
            if (mobileDownloadLink == null) {
                mobileDownloadLink = ""
            }
            if (extraDesc == null) {
                extraDesc = ""
            }
            if (eventDesc == null) {
                eventDesc = ""
            }

            if (highNowPrice && nowPrice?.endsWith(".00") && highNowPrice?.endsWith(".00")) {
                nowPrice = nowPrice?.split(/\./)[0]
                highNowPrice = highNowPrice?.split(/\./)[0]
            }
        }
    }

    static Boolean getSwitchConfig(String configName) {
        return "true".equalsIgnoreCase(MetabaseTool.getValue(configName))
    }
    static Boolean isPintuanItem(ItemBaseDO itemBase, PinTuanDO pinTuan, PresaleDO presale) {
        // pintuanDO不为null，并且拼团库存不为0，并且商品不是下架、待开售、预售
        return pinTuan && (!Tools.isJdItem() && pinTuan?.skuInfo?.totalStock > 0 || Tools.isJdItem()) && itemBase?.state != 1 && itemBase?.state != 3 && presale == null && !Boolean.valueOf(DetailContextHolder.get().getParam("noPintuan"))
    }

    /**
     * 直播商品进图墙，sourceParams {"actorId":"16u922u","type":1}
     * @return
     */
    static SourceType getSourceType(DetailContext context) {
        if (context == null) return null
        try {
            String sourceParams
            if (!TextUtils.isEmpty(sourceParams = context.getParam("sourceParams"))) {
                Gson gson = new Gson()
                JsonObject json = gson.fromJson(sourceParams, JsonObject)
                Integer type = json.get("type").getAsInt()
                switch (type) {
                    case 1:
                        return SourceType.LIVE_WALL;

                    default:
                        return SourceType.NULL;
                }
            }
        } catch (Exception ignore) {
        }
        return SourceType.NULL;
    }
}