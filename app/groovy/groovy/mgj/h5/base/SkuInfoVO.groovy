package groovy.mgj.h5.base

import com.mogujie.detail.module.sku.domain.AddressInfo
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import com.mogujie.detail.spi.dslutils.Tools

class SkuInfoVO {

    String title
    List<SkuData> skus
    List<PropInfo> props
    String styleKey
    String sizeKey
    String priceRange
    String defaultPrice
    Integer totalStock
    String mainPriceStr
    String subPriceStr
    Boolean canInstallment
    Integer limitTotalStock
    Integer limitNum
    String limitDesc
    String highNowPrice
    String lowNowPrice
    Integer freePhases
    AddressInfo addressInfo

    /**
     * 额外添加的属性
     */
    Boolean isJdItem


    SkuInfoVO(SkuDO sku) {
        this.title = sku?.title
        this.skus = sku?.skus
        this.props = sku?.props
        this.styleKey = sku?.styleKey
        this.sizeKey = sku?.sizeKey
        this.priceRange = sku?.priceRange
        this.defaultPrice = sku?.defaultPrice
        this.totalStock = sku?.totalStock
        this.mainPriceStr = sku?.mainPriceStr
        this.subPriceStr = sku?.subPriceStr
        this.canInstallment = sku?.canInstallment
        this.limitTotalStock = sku?.limitTotalStock
        this.limitNum = sku?.limitNum
        this.limitDesc = sku?.limitDesc
        this.highNowPrice = sku?.highNowPrice
        this.lowNowPrice= sku?.lowNowPrice
        this.freePhases = sku?.freePhases
        this.addressInfo = sku?.addressInfo?:new AddressInfo()
        this.isJdItem = Tools.isJdItem()
    }
}