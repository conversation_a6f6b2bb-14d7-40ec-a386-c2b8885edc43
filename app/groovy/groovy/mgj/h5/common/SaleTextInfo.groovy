package groovy.mgj.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.xcx.h5.base.SaleTextVO
import groovy.xcx.h5.common.SaleTextInfoV2

/**
 * Create by jinger on 2020/01/09 18:15
 */
@Translator(id = "saleTextInfo", defaultValue = DefaultType.NULL)
class SaleTextInfo implements IThreeDependTranslator<ItemBaseDO, SkuDO, ExtraDO, SaleTextVO> {

    @Override
    SaleTextVO translate(ItemBaseDO itemBase, SkuDO sku, ExtraDO extraDO) {

        if (!itemBase || !sku) {
            return  null
        }

        SaleTextVO saleTextInfo = SaleTextInfoV2.getSaleText(sku)
        if (saleTextInfo?.text) {
            return saleTextInfo
        }

        // 营销文案兜底
        return new SaleTextVO(
                text: itemBase?.slogan,
                name: "slogan"
        )
    }
}