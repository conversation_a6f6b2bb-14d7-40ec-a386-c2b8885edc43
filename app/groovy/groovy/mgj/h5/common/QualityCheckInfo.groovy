package groovy.mgj.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.qualitycheck.domain.CheckDetail
import com.mogujie.detail.module.qualitycheck.domain.CheckInfo
import com.mogujie.detail.module.qualitycheck.domain.QualityCheckDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 21/03/2017.
 * H5公共模块-质检信息
 */

@Translator(id = "qualityCheckInfo")
class QualityCheckInfo implements IOneDependTranslator<QualityCheckDO, QualityCheckInfoVO> {

    static class QualityCheckInfoVO {
        List<CheckDetail> detailList;
        CheckInfo checkInfo;
    }

    @Override
    QualityCheckInfoVO translate(QualityCheckDO qualityCheck) {
        if (!qualityCheck) {
            return null;
        }
        return new QualityCheckInfoVO(
                detailList: qualityCheck?.detailList,
                checkInfo: qualityCheck?.checkInfo
        );
    }
}