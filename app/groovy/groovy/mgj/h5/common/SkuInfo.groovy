package groovy.mgj.h5.common

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.AddressInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.spi.dslutils.Tools

/**
 * Created by changsheng on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "skuInfo")
public class SkuInfo implements ITwoDependTranslator<ItemBaseDO, SkuDO, SkuVO> {

    static class SkuVO extends SkuDO {
        String itemId;
        String img;
        Boolean isJdItem;
    }

    @Override
    SkuVO translate(ItemBaseDO itemBase, SkuDO input1) {
        SkuVO sku = JSON.parseObject(JSON.toJSONString(input1?: {}), SkuVO.class);

        sku.itemId = itemBase?.iid;
        sku.img = itemBase?.topImages?.size() > 0 ? itemBase.topImages.get(0) : '';
        sku.isJdItem = Tools.isJdItem()
        sku.addressInfo = sku?.addressInfo?:new AddressInfo()

        return sku
    }
}