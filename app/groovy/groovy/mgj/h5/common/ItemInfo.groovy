package groovy.mgj.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.BizType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.LimitDiscountInfo
import com.mogujie.detail.module.itemBase.domain.OfficialRecommend
import com.mogujie.detail.module.itemBase.domain.VideoInfo
import com.mogujie.detail.module.brandinfo.domain.BrandInfoDO
import com.mogujie.detail.module.seo.domain.SeoDO
import com.mogujie.detail.spi.dslutils.Tools
import groovy.mgj.h5.base.Util
import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import groovy.xcx.h5.base.constant.SourceType

/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-商品信息
 */

@Translator(id = "itemInfo")
class ItemInfo implements IFourDependTranslator<ItemBaseDO, ExtraDO, SeoDO, BrandInfoDO, ItemInfoVO> {

    static class PickformeVO {
        String name;
        String icon;
        String link;
        String acm;
    }

    static class ItemInfoVO {
        String desc;
        String title;
        String itemId;
        Boolean isFaved;
        // 是否显示分享赚积分
        Boolean isShareIntegral;
        Integer cFav;

        String slogan;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer state;
        /**
         * 开售时间，待开售商品
         */
        Long saleStartTime;
        /**
         * 商品类型，0为普通商品，1为预售商品
         */
        Integer saleType;

        Map<String, String> seo;

        Integer type

        VideoInfo video

        Integer priceChannel

        Integer virtualItemType

        // 是否为医美虚拟商品
        Boolean isMedicalBeautyVirtualItem

        // 是否为虚拟优惠券商品
        Boolean isVirtualCouponItem

        // 是否为虚拟商品
        Boolean isVirtualItem

        // 是否为京东商品
        Boolean isJdItem

        // 是否在春节期间
        Boolean inSpringFestival
        // 是否在春节期间休息不发货
        Boolean isSpringFestivalShutdownItem
        // 是否订阅了30天发货服务体系
        Boolean contain30DayDeliveryService

        // 是否显示开通"白付美"引导条
        Boolean canApplyInstallment

        // 蘑豆折扣
        String modouDiscount

        ExtraDO extra

        OfficialRecommend officialRecommend

        // 限量立减
        LimitDiscountInfo limitDiscountInfo

        // 与类目相关的大促中通Banner
        def categoryBanner

        String lowPrice;
        String highPrice;
        String lowNowPrice;
        String highNowPrice;

        String brandName; // 品牌名称

        Long nowTime = System.currentTimeSeconds();

        // 标题tag
        List<String> titleTags = [];
        List<String> titleTagsV2 = [];

        // 图片展示的右上角tag
        List<String> picTags = [];

        /**
         * title 右侧的帮我选
         */
        PickformeVO pickforme;

        ItemInfoVO(ItemBaseDO item, ExtraDO extra, SeoDO seo, BrandInfoDO brand) {
            this.desc = item?.desc;
            this.title = item?.title;
            this.isFaved = item?.isFaved;
            this.state = item?.state;
            this.saleType = item?.saleType;
            this.type = item?.type;
            this.video = item?.video
            this.canApplyInstallment = item?.canApplyInstallment
            this.slogan = item?.slogan
            this.limitDiscountInfo = item?.limitDiscountInfo?: new LimitDiscountInfo();

            this.lowNowPrice = item?.lowNowPrice;
            this.highNowPrice = item?.highNowPrice;

            // 判断是否能展示原价
            this.lowPrice = item.canShowStrikethroughPrice? item?.lowPrice : this.lowNowPrice;
            this.highPrice = item.canShowStrikethroughPrice? item?.highPrice : this.highNowPrice;

            this.brandName = brand?.showName;

            this.officialRecommend = item?.officialRecommend?: new OfficialRecommend();

            this.titleTags = []
            this.titleTagsV2 = []
            this.picTags = []

            this.seo = seo;
            this.itemId = item?.iid;
            this.isShareIntegral = Util.getSwitchConfig("shareIntegralSwitch");
            this.priceChannel = item?.priceChannel ?: 0;
            this.virtualItemType = item?.virtualItemType?.getCode();
            this.cFav = item?.getCFav();

            this.extra = extra?: new ExtraDO();
            this.saleStartTime = extra?.onSaleTime;
            this.modouDiscount = extra?.modouDiscount;

            this.isJdItem = Tools.isJdItem();
            this.isMedicalBeautyVirtualItem = Tools.isMedicalBeautyItem();
            this.isVirtualCouponItem = Tools.isVirtualCouponItem();
            this.isVirtualItem = this.virtualItemType != 0 || this.isMedicalBeautyVirtualItem || this.isVirtualCouponItem; // 虚拟商品
            this.inSpringFestival = Tools.inSpringFestival();
            this.isSpringFestivalShutdownItem = Tools.isSpringFestivalShutdownItem();
            this.contain30DayDeliveryService = Tools.contain30DayDeliveryService();

            List<Map<String, Object>> mdl1 = MaitUtil.getTargetedMaitData(117725)
            this.categoryBanner = mdl1?.get(0)

            List<Map<String, Object>> mdl2 =  MaitUtil.getMaitData(127253L)
            Map<String, Object> md2 = mdl2?.get(0)

            // 自营标
            if (Tools.isSelfEmployedItem() && md2?.get("topSelfIcon")) {
                this.picTags = [md2?.get("topSelfIcon")?.toString()]
            }

            // 过渡一下先，mgj-detail, mgj-detail-pintuan还会用到
            if (Tools.isSelfEmployedItem() && md2?.get("selfIcon")) {
                this.titleTags.add(md2?.get("selfIcon")?.toString())
            }

            // 直播秒杀中标
            SourceType sourceType = Util?.getSourceType(DetailContextHolder.get())
            if (extra?.isLiveSeckill() && md2?.get("liveSeckillIcon") && sourceType != SourceType.LIVE_WALL) {
                this.titleTagsV2.add(0, md2?.get("liveSeckillIcon")?.toString())
            }

            // 帮我选（默认只有女装类目透出），渠道不透出
            RouteInfo routeInfo = DetailContextHolder.get().getRouteInfo()
            if (routeInfo.bizType == BizType.NORMAL) {
                List<Map<String, Object>> maitList = MaitUtil.getMaitData(143035L)
                Map<String, Object> maitData = maitList?.get(0)
                String pickformeCids = maitData?.get("cids") ?: "683"

                if (groovy.xcx.h5.base.Util.isInCids(pickformeCids, item)) {
                    String h5Link = "https://h5.mogu.com/pickforme/launch/index.html?itemId=${item.iid}"
                    h5Link = groovy.xcx.h5.base.Util.addAcmToUrl(h5Link, maitData?.get("acm"))
                    this.pickforme = new PickformeVO(
                            icon: 'https://s10.mogucdn.com/mlcdn/c45406/191218_762ibk3def84blec68cbe4le770df_54x54.png',
                            name: '帮我选',
                            link: h5Link,
                            acm: maitData?.get("acm")
                    )
                }
            }

        }
    }

    @Override
    ItemInfoVO translate(ItemBaseDO itemBase, ExtraDO extra, SeoDO seo, BrandInfoDO brand) {
        if (!itemBase) {
            return null;
        }
        ItemInfoVO itemInfo = new ItemInfoVO(itemBase, extra, seo, brand);
        return itemInfo;
    }
}