package groovy.mgj.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.live.domain.LiveDO
import com.mogujie.detail.module.live.domain.LiveSimpleDO
import com.mogujie.detail.module.live.domain.LiveType
import com.mogujie.detail.module.shop.domain.ShopDO
import groovy.xcx.h5.base.Util

/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "liveInfo")
public class LiveInfo implements IThreeDependTranslator<LiveDO, ShopDO, LiveSimpleDO, LiveInfoVO> {

    static class LiveInfoVO {
        LiveType liveType
        String licenseImage
        String providerName
        Long providerId
    }

    @Override
    LiveInfoVO translate(LiveDO live, ShopDO shop, LiveSimpleDO liveSimpleDO) {

        if (liveSimpleDO?.pickedExplainInfo) {
            return new LiveInfoVO(
                    providerName: shop?.name
            )
        }

        if (live) {
            return new LiveInfoVO(
                    liveType: live?.liveType,
                    licenseImage: live?.licenseImage,
                    providerId: live?.providerId,
                    providerName: live?.providerName
            )
        }

        if (Util?.isHideEntryShop(shop)) {
            return new LiveInfoVO(
                    providerName: shop?.name
            )
        }

        return  new LiveInfoVO()

    }
}