package groovy.mgj.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.App
import com.mogujie.detail.core.constant.ItemTag
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.spi.dslutils.Tools
import com.mogujie.metabase.spring.client.MetabaseClient
import groovy.mgj.h5.base.ItemTagVO
//import groovy.mgj.h5.base.PintuanInfoVO
import org.apache.commons.lang3.StringUtils

import javax.annotation.Resource

/**
 * Created by changsheng on 21/03/2017.
 * H5公共模块-商品销量、商品包邮、商品Tag、店铺服务体系
 */

@Translator(id = "itemServices")
class ItemServices implements IFourDependTranslator<ItemBaseDO, ShopDO, ExtraDO, PinTuanDO, ItemServicesVO> {

    static class ItemServicesVO {
        List<ItemTagVO> services
        List<ItemTagVO> columns
    }

    @Resource(name = "confMetabaseClient")
    private MetabaseClient metabaseClient

    @Override
    ItemServicesVO translate(ItemBaseDO itemBase, ShopDO shop, ExtraDO extra, PinTuanDO pinTuan) {
        if (extra?.sales == null) {
            return null
        }
        // 销量和包邮
        String express = "默认快递"
        if (extra?.isFreePost()) {
            express = "免邮费"
        } else if (extra?.postPrice != null) {
            def price = String.format("%.2f", extra?.postPrice / 100.0f)
            express = "快递 ${price}元"
        }
        List<ItemTagVO> columns = [
                // 销量放在评价里
                new ItemTagVO(
                        desc: "销量 " + extra?.sales?.toString(),
                        icon: ImageUtil.img("/p1/161117/idid_ifrdkytcgbstoobummzdambqmeyde_512x512.png"),
                        name: "sales"
                ),
                new ItemTagVO(
                        desc: express,
                        icon: ImageUtil.img("/p1/161117/idid_ifqwgnbsmjsdoobummzdambqgyyde_512x512.png"),
                        name: "express"
                )
        ]

        if (extra?.address) {
            columns.add(new ItemTagVO(
                    desc: extra?.address,
                    icon: ImageUtil.img("/p1/161117/idid_ie4tgmbsgjsdoobummzdambqgqyde_512x512.png"),
                    name: "address"
            ))
        }

        // ItemTags商品标
        List<ItemTagVO> services = []

        // 店铺服务体系
        services.addAll(
                shop?.services?.collect {
                    new ItemTagVO(
                            name: it?.name,
                            icon: it?.icon,
                            link: it?.link,
                            content: it?.desc
                    )
                } ?: []
        )

//        PintuanInfoVO pintuanInfo = new PintuanInfoVO(pinTuan, itemBase)

        services = filterServices(services/*,pintuanInfo*/)

        services = solrtServices(services)

        // 全部都变成红色勾，暂时由后端修改一下，后续在前端升级就不需要了
        columns.forEach {o ->
            o.icon = 'https://s10.mogucdn.com/mlcdn/c45406/180417_25kbfg1c3hdbd120394ji4b11bk2k_36x36.png';
        }
        services.forEach {o ->
            o.icon = 'https://s10.mogucdn.com/mlcdn/c45406/180417_25kbfg1c3hdbd120394ji4b11bk2k_36x36.png';
        }

        /**
         * services 包含了商品服务（可缓存）和店铺服务
         * 在动态请求的时候，商品服务为null，店铺服务有值，会覆盖缓存，导致商品服务为空
         * 所以在动态请求时，需要返回null，去取缓存的数据
         */
        Boolean isDyn = DetailContextHolder.get().isDyn()

        return new ItemServicesVO(
                columns: columns,
                services: isDyn ? null : services
        )
    }

    List<ItemTagVO> solrtServices(List<ItemTagVO> services) {
        String noRefoundText = metabaseClient.get("noRefoundText")
        noRefoundText = StringUtils.isBlank(noRefoundText) ? "不支持无理由退货" : noRefoundText
        List<String> standardList = ["进口商品", "商家包税", "假一赔三", "健康服务", "医美意外险", "劣一赔三服务","源头好货","品牌认证","入仓质检","小时发货","天无理由退货",noRefoundText,"退货补运费","实拍认证","白付美分期购"]
        String itemServicePriority = metabaseClient.get("app_item_service_priority")
        if (itemServicePriority) {
            standardList = itemServicePriority.split(",")
        }

        return services.sort {a, b ->
            Integer atIndex = findIndexOfService(standardList, a?.name)
            Integer btIndex = findIndexOfService(standardList, b?.name)
            if (atIndex > -1 && btIndex > -1) {
                return atIndex - btIndex
            } else if (atIndex > -1) {
                return -1
            } else if (btIndex > -1) {
                return 1
            } else {
                return  0
            }
        }
    }

    static Integer findIndexOfService(List<String> standardList, String service) {
        return standardList.findIndexOf {
            service.endsWith(it)
        }
    }

    static List<ItemTagVO> filterServices(List<ItemTagVO> services /*, PintuanInfoVO pintuanInfo*/) {
        List<String> disableServices = []

        // 保税商品不展示"发货后不支持退货"
        if (Tools.isBondedItem()) {
            disableServices.add("发货后不支持退货")
        }

        // 0元团不展示"延误必赔"
//        if (pintuanInfo?.pintuanPrice?.equals("0.00")) {
//            disableServices.add("延误必赔")
//        }

        disableServices.each { serviceNameSuffix ->
            services = services.findAll { service ->
                !service?.name?.endsWith(serviceNameSuffix)
            }
        }

        return services
    }
}