package groovy.mgj.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.qzonevip.domain.QZoneDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.h5.base.ItemPriceVO

/**
 * Created by changsheng on 26/07/2017.
 * H5私有模块-黄钻详情页-黄钻信息
 */

@Translator(id = "diamondInfo")
class DiamondInfo implements ITwoDependTranslator<QZoneDO, ItemBaseDO, DiamondInfoVO> {

    static class DiamondInfoVO {
        /**
         * 黄钻价
         */
        String diamondPrice
        String normalPrice
        /**
         * 普通详情页可以通过判断是否有activityId来判断是否有黄钻专享
         */
        String activityId
        SkuDO skuInfo
        /**
         * 黄钻商品状态
         * 1：已下架，2：普通+未开始，3：普通+已结束，4：普通+已抢光，5：正常拼团
         */
        Integer state
        String currency = "¥"
        /**
         * 普通详情页使用的背景图
         * 价格说明文案
         * 右侧跳转说明文案
         */
        String backgroundImage
        String priceTitle
        String arrowTitle
    }

    @Override
    DiamondInfoVO translate(QZoneDO qZone, ItemBaseDO itemBase) {
        if (!itemBase) {
            return null
        }

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        itemPrice.setNowPriceByRange(itemBase)
        itemPrice.setOldPriceByRange(itemBase)

        Integer state = 0
        Integer itemState = itemBase?.state
        SkuDO diamondSkuInfo = qZone?.skuInfo
        Integer diamondStock = diamondSkuInfo?.totalStock

        Integer now = System.currentTimeSeconds()
        Integer startTime = qZone?.startTime
        Integer endTime = qZone?.endTime

        if (itemState == 3 || itemState == 1) {
            state = 1
        } else if (!qZone) {
            state = 3
        } else if (now < startTime) {
            state = 2
        } else if (now > startTime && now < endTime) {
            if (diamondStock <=0) {
                state = 4
            } else {
                state = 5
            }
        } else if (now > endTime) {
            state = 3
        }

        /**
         * 背景图和文案从麦田取
         */
        List<Map<String, Object>> maitDataList = MaitUtil.getMaitData(61288)
        Map<String, Object> maitData = maitDataList?.get(0)

        String backgroundImage = maitData?.get("image")
        String arrowTitle = maitData?.get("arrowTitle")
        String priceTitle = maitData?.get("priceTitle")

        return new DiamondInfoVO(
                diamondPrice: qZone?.vipPrice ? NumUtil.formatNum(qZone?.vipPrice / 100D) : itemPrice?.nowPrice,
                state: state,
                activityId: qZone?.activityId,
                normalPrice: itemPrice?.nowPrice,
                skuInfo: qZone?.skuInfo,
                backgroundImage: backgroundImage,
                arrowTitle: arrowTitle,
                priceTitle: priceTitle
        )
    }
}