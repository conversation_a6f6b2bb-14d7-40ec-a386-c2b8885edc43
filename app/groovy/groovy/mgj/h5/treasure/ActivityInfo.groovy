package groovy.mgj.h5.treasure

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.treasure.domain.TreasureDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<TreasureDO, TreasureDO>{

    @Override
    TreasureDO translate(TreasureDO treasure) {
        if (!treasure) {
            return null
        }

        TreasureDO obj = JSON.parseObject(JSON.toJSONString(treasure), TreasureDO.class);
        // 防止返回为null 走了缓存
        obj.rankList = treasure.rankList?:[];
        obj.awardList = treasure.awardList?:[];

        return obj;
    }

}
