package groovy.mgj.h5.treasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.treasure.domain.TreasureDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.marketing.duobao.enums.BizStatus

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "treasureInfo", defaultValue = DefaultType.EMPTY_MAP)
class TreasureInfo implements ITwoDependTranslator<TreasureDO, ItemBaseDO, TreasureInfoVO>{

    static class TreasureInfoVO {
        String state
        Long startTime
        Long endTime
        def rankList
        def awardList
        Integer treasureCodeNums
    }

    @Override
    TreasureInfoVO translate(TreasureDO treasure, ItemBaseDO itemBase) {
        if (!treasure) {
            return null
        }

        return new TreasureInfoVO(
                state: treasure.status?.name(),
                startTime: treasure.startTime,
                endTime: treasure.endTime,
                rankList: treasure.rankList?:[],
                awardList: treasure.awardList?:[],
                treasureCodeNums: treasure.treasureCodeNums
        )
    }

}
