package groovy.mgj.h5.diamond

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.qzonevip.domain.QZoneDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by changsheng on 27/07/2017.
 */

@Translator(id = "diamondPrice")
class DiamondPrice implements ITwoDependTranslator<ItemBaseDO, QZoneDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, QZoneDO qZone) {

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return  null
        } else if (qZone && qZone?.vipPrice) {
            itemPrice.with {
                nowPrice = NumUtil.formatNum(qZone?.vipPrice / 100D)
                oldPrice = getOldPriceForce(itemBase)

                priceTags = [
                        new PriceTagVO(
                                text: "豪华黄钻专享"
                        )
                ]
            }
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
            }
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)

        return itemPrice
    }
}