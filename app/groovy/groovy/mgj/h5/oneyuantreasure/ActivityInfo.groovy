package groovy.mgj.h5.oneyuantreasure

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.oneYuanTreasure.domain.OneYuanTreasureDO;

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-一元夺宝
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
public class ActivityInfo implements IOneDependTranslator<OneYuanTreasureDO, Object> {

    @Override
    Object translate(OneYuanTreasureDO treasure) {
        if (!treasure) {
            return null;
        }

        return treasure;
    }
}