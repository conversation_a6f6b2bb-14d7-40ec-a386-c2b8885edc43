package groovy.mgj.h5.jiajiagou

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by changsheng on 19/09/2017.
 */

@Translator(id = "priceInfo")
class PriceInfo implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase)

        if (!itemBase) {
            return null
        } else {
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
                if (itemBase?.discountDesc) {
                    priceTags = [
                            new PriceTagVO(
                                    text: itemBase?.discountDesc
                            )
                    ]
                }
            }

            Util.setEsiDataForPrice(itemPrice)

            return itemPrice
        }
    }
}