package groovy.mgj.h5.mdovervalue

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.channelInfo.domain.ChannelInfoDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.modou.domain.ModouDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-蘑豆详情页(加价购)-蘑豆信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
public class ActivityInfo implements IFourDependTranslator<ModouDO, ItemBaseDO, PinTuanDO, ChannelInfoDO, ActivityInfoVO> {

    static class ActivityInfoVO {
        Long startTime
        Long endTime
        Long totalStock
        Long allStock
//        Integer modouAmount
        Integer state
        String nowSaleLowPrice
        Boolean isPintuanLowPrice

        String prefix
        String discountPrice
        String offsetPrice
    }

    @Override
    ActivityInfoVO translate(ModouDO modou, ItemBaseDO itemBase, PinTuanDO pinTuan, ChannelInfoDO channelInfo) {
        if (!modou) {
            return null;
        }

        String nowSaleLowPrice = '';
        Boolean isPintuanLowPrice = false;

        if (channelInfo?.lowNormalPrice != null) {
            nowSaleLowPrice = NumUtil.formatNum(channelInfo.lowNormalPrice / 100D);
        }

        if (pinTuan?.tuanType == 7) {
            isPintuanLowPrice = true;
            nowSaleLowPrice = pinTuan.skuInfo?.lowNowPrice;
        }

        double discountPrice = 0D;

        if (itemBase?.lowNowPrice != null && modou?.offsetPrice != null) {
            discountPrice = Double.parseDouble(itemBase?.lowNowPrice) - Double.parseDouble(modou?.offsetPrice);
        }


        def res = MaitUtil.getMaitData(40806L/*40369L*/);
        def start = res?.get(0)?.get('vipStartTime') as Integer ?: 0;
        def end = res?.get(0)?.get('vipEndTime') as Integer ?: 0;
        int now = (int) (System.currentTimeMillis() / 1000);
        def prefix = "";
        //处在会员日,需要透出额外的前缀文案
        if (start && end && start < now && now < end) {
            prefix = res?.get(0)?.get("details") as String ?: '';
            prefix = prefix + ",";
        }

        return new ActivityInfoVO(
            startTime: modou.startTime,
            endTime: modou.endTime,
            totalStock: modou.totalStock,
            allStock: channelInfo?.originTotalStock,
//            modouAmount: modou.modouAmount,
            state: modou.state,
            nowSaleLowPrice: nowSaleLowPrice,
            isPintuanLowPrice: isPintuanLowPrice,
            offsetPrice: modou?.offsetPrice,
            prefix: prefix,
            discountPrice: String.format("%.2f", discountPrice)
        )
    }
}