package groovy.mgj.h5.mdovervalue

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.modou.domain.ModouDO
import com.mogujie.detail.module.sku.domain.SkuDO

/**
 * Created by anshi on 17/3/9.
 * H5私有模块-蘑豆详情页(超值购)-蘑豆信息
 */
@Translator(id = "mdOverValueInfo")
public class MdOverValueInfo implements IThreeDependTranslator<ModouDO, SkuDO, ItemBaseDO, MdOverValueInfoVO> {

    static class MdOverValueInfoVO {
        Integer type
        String offsetPrice
        String prefixDesc
        String suffixDesc
        Integer state
        String prefix
        String discountPrice
    }

    @Override
    MdOverValueInfoVO translate(<PERSON>douD<PERSON> modou, Sku<PERSON><PERSON> sku, ItemBaseDO itemBase) {
        // 默认活动结束
        Integer state = 3;
        /**
         * 0: 正常，1：虚拟店铺&已下架，2：库存不足，3：待开售&已下架
         */
        Integer itemState = itemBase?.state
        /**
         * 0. 未开始
         * 1. 活动中
         * 2. 活动结束
         * 3. 非蘑豆商品
         */
        Integer modouState = modou?.state
        if (!modou || modouState == 2) {
            // 不在活动有效期范围，活动结束
            state = 3
        } else if (itemState == 3 || itemState == 1) {
            // 商品下架
            state = 4
        } else if (itemState == 2) {
            // 商品库存不足
            state = 2
        } else if (modouState == 0) {
            // 活动未开始
            state = 0
        } else if (modouState == 1 && itemState == 0 && sku?.totalStock > 0) {
            // 正常状态
            state = 1
        } else if (modouState == 3) {
            // 活动结束
            state = 3
        }

        double discountPrice = 0D;

        if (itemBase?.lowNowPrice != null && modou?.offsetPrice != null) {
            discountPrice = Double.parseDouble(itemBase?.lowNowPrice) - Double.parseDouble(modou?.offsetPrice);
        }


        def res = MaitUtil.getMaitData(40806L/*40369L*/);
        def start = res?.get(0)?.get('vipStartTime') as Integer ?: 0;
        def end = res?.get(0)?.get('vipEndTime') as Integer ?: 0;
        int now = (int) (System.currentTimeMillis() / 1000);
        def prefix = "";
        //处在会员日,需要透出额外的前缀文案
        if (start && end && start < now && now < end) {
            prefix = res?.get(0)?.get("details") as String ?: '';
            prefix = prefix + ",";
        }
        return new MdOverValueInfoVO(
                state: state,
                type: modou?.type,
                offsetPrice: modou?.offsetPrice,
                prefix: prefix,
                discountPrice: String.format("%.2f", discountPrice)
        )
    }
}