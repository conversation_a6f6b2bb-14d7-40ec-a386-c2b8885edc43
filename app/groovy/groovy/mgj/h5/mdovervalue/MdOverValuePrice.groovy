package groovy.mgj.h5.mdovervalue

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util

/**
 * Created by chang<PERSON>ng on 21/03/2017.
 * H5私有模块-蘑豆详情页(超值购)-蘑豆价格
 */

@Translator(id = "mdOverValuePrice")
public class MdOverValuePrice implements IOneDependTranslator<ItemBaseDO, ItemPriceVO> {
    @Override
    ItemPriceVO translate(ItemBaseDO itemBase) {
        if (!itemBase) {
            return null;
        }
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);
        itemPrice.setNowPriceByRange(itemBase);
        itemPrice.setOldPriceByRange(itemBase);
        itemPrice.prePriceTag = new PriceTagVO(
                text: "蘑豆价",
                textColor: "#9D3FEF"
        );

        Util.setEsiDataForPrice(itemPrice)

        return itemPrice;
    }
}