package groovy.mgj.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.sku.domain.SkuDO
import groovy.mgj.app.fastbuy.FastbuyPricebannerV3

/**
 * Created by pananping on 2020/10/28.
 */
@Translator(id = "priceBannerV2", defaultValue = DefaultType.NULL)
class PriceBannerV2 implements IFourDependTranslator<SkuDO, FastbuyDO, ItemBaseDO, ExtraDO, Object> {

    @Override
    Object translate(SkuDO skuDO, FastbuyDO fastbuyDO, ItemBaseDO itemBaseDO, ExtraDO extraDO) {
        return new FastbuyPricebannerV3().translate(skuDO, fastbuyDO, itemBaseDO, extraDO)
    }
}