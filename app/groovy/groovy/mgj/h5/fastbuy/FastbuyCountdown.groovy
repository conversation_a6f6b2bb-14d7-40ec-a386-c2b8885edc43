package groovy.mgj.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import groovy.mgj.h5.base.TopCountdownVO

/**
 * Created by changsheng on 30/06/2017.
 * H5私有模块-快抢详情页-快抢倒计时
 */

@Translator(id = "countdownInfo", defaultValue = DefaultType.EMPTY_MAP)
class FastbuyCountdown implements IOneDependTranslator<FastbuyDO, TopCountdownVO> {

    @Override
    TopCountdownVO translate(FastbuyDO fastbuy) {
        if (!fastbuy) {
            return null
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(fastbuy?.maitId?: 123208);
        Map<String, Object> md = maitData?.get(0);

        Long startTime = fastbuy?.startTime
        Long endTime = fastbuy?.endTime

        def image = md?.get("coverBg")
        def titleColor = md?.get("titleColor")
        def priceColor = md?.get("priceColor")
        def titleIcon = md?.get("titleIcon")
        def activityBanner = md?.get("activityBanner")

        def nowTime = System.currentTimeSeconds()
        if (nowTime < startTime) {
            Long countdown = startTime - nowTime
            // 活动未开始
            return new TopCountdownVO(
                    text: countdown < 25 * 60 * 60 ? "距开始" : "开始时间",
                    countdown: countdown,
                    image: image,
                    titleColor: titleColor,
                    priceColor: priceColor,
                    titleIcon: titleIcon,
                    activityBanner: activityBanner,
                    isPriceShow: true,
                    isWarmUp: true
            )
        } else if (nowTime > startTime && nowTime < endTime) {
            // 活动进行中
            return new TopCountdownVO(
                    text: "距结束",
                    countdown: endTime - nowTime,
                    image: image,
                    titleColor: titleColor,
                    priceColor: priceColor,
                    titleIcon: titleIcon,
                    activityBanner: activityBanner,
                    isPriceShow: true
            )
        } else {
            return new TopCountdownVO()
        }
    }
}
