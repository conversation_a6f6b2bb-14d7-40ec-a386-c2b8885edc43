package groovy.mgj.h5.fastbuy

import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢状态
 */


/**
 * fastbuyState
 * 0. 未开始,距离开始时间大于5分钟
 * 1. 活动中
 * 2. 活动中, 但是库存为0，并且没有未付款人数
 * 3. 活动结束 （这个状态不会使用）
 * 4. 活动中, 但是库存为0，并且有未付款人数
 * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
 */

class FastbuyState {
    public static final int ACTIVITY_NOT_STARTED = 0
    public static final int ACTIVITY_STARTED = 1
    public static final int OUT_OF_STOCK = 2
    public static final int ACTIVITY_ENDED = 3
    public static final int WAIT_FOR_STOCK = 4
    public static final int ACTIVITY_WARM_UP = 5

    Integer state

    FastbuyState(FastbuyDO fastbuy, ItemBaseDO itemBase) {
        Integer itemState = itemBase?.state
        Integer state = fastbuy?.state
        Long startTime = fastbuy?.startTime
        Long nowTime = System.currentTimeSeconds()

        if (itemState == 1 || itemState == 3) {
            state = 3
        } else if (nowTime < startTime && state == 0 && (startTime - nowTime) <= 300) {
            state = 5
        }

        this.state = state
    }

}
