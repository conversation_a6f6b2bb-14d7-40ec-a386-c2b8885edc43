package groovy.mgj.h5.fastbuy

import com.alibaba.fastjson.JSON
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO

/**
 * Created by chang<PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<FastbuyDO, FastbuyDO>{

    static class FastbuyVO extends FastbuyDO {
        String isHiddenProgress = '';
        String progressValueColor = '';
        String progressBgColor = '';
    }

    @Override
    FastbuyVO translate(FastbuyDO input1) {
        if (!input1) {
            return null
        }

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(input1?.maitId?: 123208);
        Map<String, Object> md = maitData?.get(0);

        FastbuyVO a = JSON.parseObject(JSON.toJSONString(input1), FastbuyVO.class);
        a.isHiddenProgress = md?.get('isHiddenProgress');
        a.progressValueColor = md?.get('progressValueColor');
        a.progressBgColor = md?.get('progressBgColor');

        return a;
    }

}
