package groovy.mgj.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.itemBase.domain.LimitDiscountInfo
import groovy.mgj.h5.base.ItemPriceVO
import groovy.mgj.h5.base.PriceTagVO
import groovy.mgj.h5.base.Util
import com.mogujie.detail.core.util.NumUtil


/**
 * Created by changsheng on 30/06/2017.
 * H5私有模块-快抢详情页-价格
 */
@Translator(id = "priceInfo")
class FastbuyPrice implements ITwoDependTranslator<ItemBaseDO, FastbuyDO, ItemPriceVO> {


    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, FastbuyDO fastbuy) {
        ItemPriceVO rushPrice = new ItemPriceVO(itemBase)

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(fastbuy?.maitId?: 123208);
        Map<String, Object> md = maitData?.get(0);


        rushPrice.with {
            if (itemBase) {
                // 业务上保证没有范围价了 2018-08-28
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)

                // 限量立减
                LimitDiscountInfo limitDiscountInfo = itemBase?.limitDiscountInfo
                if (limitDiscountInfo) {
                    // 限量立减的原价为大促价
                    if (itemBase?.activityPrice) {
                        oldPrice = NumUtil.formatNum(itemBase?.activityPrice / 100D)
                    }
                    Integer stock = limitDiscountInfo?.limitCount
                    priceTags = [
                            new PriceTagVO(
                                    text: md?.get("priceDesc")?.replace('${stock}', String.valueOf(stock))?: "前${stock}件限量抢"
                            )
                    ]
                } else {
                    priceTags = [
                            new PriceTagVO(
                                    text: md?.get("priceDesc")?: "快抢价"
                            )
                    ]
                }
                if (fastbuy?.isNewComerItem()) {
                    priceTags.add(
                            new PriceTagVO(
                                    text: "新人专享"
                            )
                    )
                }
            } else {
                return null
            }
        }

        Util.setEsiDataForPrice(rushPrice)

        // 根据 ItemBaseDO#canShowStrikethroughPrice 控制划线价展示
        rushPrice.isHideOldPrice(itemBase)

        return rushPrice
    }
}
