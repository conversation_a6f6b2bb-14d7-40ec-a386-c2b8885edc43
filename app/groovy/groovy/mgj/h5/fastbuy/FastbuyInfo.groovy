package groovy.mgj.h5.fastbuy

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.module.fastbuy.domain.FastbuyDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/06/2017.
 * H5私有模块-快抢详情页-快抢信息
 */
@Translator(id = "fastbuyInfo", defaultValue = DefaultType.EMPTY_MAP)
class FastbuyInfo implements ITwoDependTranslator<FastbuyDO, ItemBaseDO, RushInfoVO>{

    static class RushInfoVO {
        /**
         * 0. 未开始
         * 1. 活动中
         * 2. 活动中, 但是库存为0，并且没有未付款人数
         * 3. 活动结束 （这个状态不会使用）
         * 4. 活动中, 但是库存为0，并且有未付款人数，此时点击按钮也是可以刷新的
         * 5. 活动未开始, 距离开始时间小于5分钟,此时点击按钮是可以刷新页面的
         */
        Integer state
        Long startTime
        Long endTime
        Long totalStock
        Long allStock
        Integer leftUser
        String activityId
        Boolean isNewComerItem
        Boolean isNewComerUser
        Boolean isFollowed
        // 历史价格图片
        String priceHistoryImg
    }

    @Override
    RushInfoVO translate(FastbuyDO fastbuy, ItemBaseDO itemBase) {
        if (!fastbuy) {
            return null
        }
        Integer itemState = itemBase?.state
        Integer state = fastbuy?.state
        Long startTime = fastbuy?.startTime
        Long nowTime = System.currentTimeSeconds()

        if (itemState == 1 || itemState == 3) {
            state = 3
        } else if (nowTime < startTime && state == 0 && (startTime - nowTime) <= 300) {
            state = 5
        }


        // 新品快时尚隐藏趋势图
        String priceHistoryImg = fastbuy?.extra?.get("kimg")?:""
        if (("newFashionBuy" == fastbuy?.getExtra()?.get("bizType")) || !itemBase.canShowStrikethroughPrice) {
            priceHistoryImg = ""
        }

        return new RushInfoVO(
            state: state,
            startTime: fastbuy?.startTime,
            endTime: fastbuy?.endTime,
            totalStock: fastbuy?.totalStock,
            allStock: fastbuy?.allStock,
            leftUser: fastbuy?.leftUser,
            activityId: fastbuy?.activityId,
            isNewComerItem: fastbuy?.isNewComerItem,
            isNewComerUser: fastbuy?.isNewComerUser,
            isFollowed: fastbuy?.followed,
            priceHistoryImg: priceHistoryImg
        )
    }

}
