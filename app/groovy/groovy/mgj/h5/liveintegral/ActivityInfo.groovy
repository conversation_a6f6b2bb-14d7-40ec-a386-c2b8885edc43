package groovy.mgj.h5.liveintegral

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.DefaultType
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.live.domain.LiveRewardPointDO

/**
 * Created by xiaoque on 2019-04-18.
 */
@Translator(id = "activityInfo", defaultValue = DefaultType.EMPTY_MAP)
class ActivityInfo implements IOneDependTranslator<LiveRewardPointDO, ActivityInfoVO> {

    static class ActivityInfoVO {
        Long startTime
        Long endTime
        Long remindTotalStock
        Long usedTotalStock
        Integer state
        Integer score
    }

    @Override
    ActivityInfoVO translate(LiveRewardPointDO liveintegral) {
        if (!liveintegral) {
            return null
        }
        return new ActivityInfoVO(
            startTime: liveintegral?.startTime,
            endTime: liveintegral?.endTime,
            remindTotalStock: liveintegral?.remindTotalStock,
            usedTotalStock: liveintegral?.usedTotalStock,
            state: liveintegral?.status,
            score: liveintegral?.score
        )
    }
}