package groovy.mgj.h5.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import groovy.mgj.h5.base.TopCountdownVO

/**
 * Created by chang<PERSON><PERSON> on 01/07/2017.
 * H5私有模块-拼团详情页-拼团倒计时
 */

@Translator(id = "countdownInfo")
class CountdownInfo implements IOneDependTranslator<PinTuanDO, TopCountdownVO>{

    @Override
    TopCountdownVO translate(PinTuanDO pinTuan) {

        List<Map<String, Object>> maitData = MaitUtil.getMaitData(123206);
        Map<String, Object> md = maitData?.get(0);

        Boolean isExpire = pinTuan?.isExpire
        Long now = System.currentTimeSeconds()
        Long startTime = pinTuan?.startTime
        Long remainTime = pinTuan?.remainTime

        if (!pinTuan) {
            return new TopCountdownVO()
        } else if (now < startTime) {
            Long countdown = startTime - now
            return new TopCountdownVO(
                    text: countdown < 25 * 60 * 60 ? "距开始" : "开始时间",
                    countdown: countdown,
                    image: md?.get("coverBg"),
                    titleColor: md?.get("countdownColor"),
                    priceColor: md?.get("priceColor"),
                    titleIcon: md?.get("titleIcon"),
                    activityBanner: md?.get("activityBanner"),
                    isWarmUp: true
            )
        } else if (now > startTime) {
            return new TopCountdownVO(
                    text: "距结束",
                    countdown: remainTime,
                    image: md?.get("coverBg"),
                    titleColor: md?.get("countdownColor"),
                    priceColor:md?.get("priceColor"),
                    titleIcon: md?.get("titleIcon"),
                    activityBanner: md?.get("activityBanner"),
            )
        } else {
            return new TopCountdownVO()
        }
    }
}
