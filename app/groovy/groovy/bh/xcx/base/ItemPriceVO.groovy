package groovy.bh.xcx.base

import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO

/**
 * Created by ch<PERSON><PERSON><PERSON> on 14/03/2017.
 * 公共VO-商品价格
 */

class ItemPriceVO {

    /**
     * 默认价格颜色
     */
    static final String defaultPriceColor = "#ffffff"
    static final String defaultEventPriceColor = "#FF2255"
    static final String defaultCountdownColor = "#333333"
    static final String defaultCountdownBackgroundColor = "linear-gradient(to left, #ffd200, #ffe730)"
    static final String defaultBackgroundImage = ImageUtil.img("/mlcdn/c45406/170724_12adc6l0clc2i225783527kd4h74a_750x84.png")

    /**
     * 现价(区间价的最小值)
     */
    String nowPrice = ""

    /**
     * 原价(暂无区间价)
     */
    String oldPrice = ""

    /**
     * 现价(区间价的最大值)
     */
    String highNowPrice = ""

    /**
     * 区间价分隔符
     */
    String priceSplit = ""

    /**
     * 价格描述
     */
    PriceTagVO priceDesc

    /**
     * 价格单位，默认为：¥
     */
    String currency = "¥"

    /**
     * 价格颜色，默认：#333333
     */
    String priceColor = ""

    /**
     * 价格后面的Tag列表
     */
    List<PriceTagVO> priceTags = []

    /**
     * 价格前面的Tag(暂时只有一项)，目前没有实时性要求，所以暂时不用默认值
     */
    PriceTagVO prePriceTag

    /**
     * 第二行的活动价
     */
    String eventPrice = ""

    /**
     * 活动家后面的描述
     */
    PriceTagVO eventPriceDesc

    /**
     * 活动价的颜色，默认：#FF2255
     */
    String eventPriceColor = ""

    /**
     * 第二行活动价后面的Tag列表
     */
    List<PriceTagVO> eventTags = []

    /**
     * 拼团氛围背景图
     */
    String backgroundImage = ""

    /**
     * 活动标识
     */
    String eventTagImage

    /**
     * 拼团氛围倒计时标题
     */
    String countdownTitle = ""

    /**
     * 拼团氛围倒计时标题颜色
     */
    String countdownTitleColor

    /**
     * 拼团氛围倒计时时间（秒）
     */
    Long countdown = 0

    /**
     * 拼团氛围倒计时文字颜色
     */
    String countdownColor = ""

    /**
     * 拼团氛围倒计时背景色（渐变）
     */
    String countdownBackgroundColor = ""

    /**
     * 价格类型，1代表日常，2代表双十一
     */
    Integer priceType

    ItemPriceVO(ItemBaseDO itemBase) {
        this.currency = itemBase?.currency ?: "¥"
        this.priceColor = defaultPriceColor
        this.eventPriceColor = defaultEventPriceColor
        this.backgroundImage = defaultBackgroundImage
        this.countdownBackgroundColor = defaultCountdownBackgroundColor
        this.countdownColor = defaultCountdownColor
        this.priceSplit = "~"
    }

    /**
     * 普通取价格逻辑-计算原价
     * @param item
     * @return
     */
    public void setOldPriceByRange(ItemBaseDO item) {
        String lowPrice = item?.lowPrice;
        String highPrice = item?.highPrice;
        String lowNowPrice = item?.lowNowPrice;
        String highNowPrice = item?.highNowPrice;

        if (lowPrice == lowNowPrice && highPrice == highNowPrice){
            this.oldPrice = null
        } else {
            if (lowPrice == highPrice) {
                this.oldPrice = lowPrice
            } else {
                this.oldPrice = "${lowPrice}起"
            }
        }
    }

    /**
     * 无视现价获取原价，一般用于非普通详情页
     * @param item
     */
    public static String getOldPriceForce (ItemBaseDO item) {
        String lowPrice = item?.lowPrice
        String highPrice = item?.highPrice

        if (lowPrice == highPrice) {
            return lowPrice
        } else {
            return "${lowPrice}起"
        }
    }

    /**
     * 普通取价格逻辑-计算现价
     * @param item
     * @return
     */
    public void setNowPriceByRange(ItemBaseDO item) {
        String lowNowPrice = item?.lowNowPrice;
        String highNowPrice = item?.highNowPrice;

        if (lowNowPrice != highNowPrice){
            this.nowPrice = lowNowPrice;
            this.highNowPrice = highNowPrice;
        } else {
            this.nowPrice = lowNowPrice
        }
    }
}