package groovy.bh.xcx.base

/**
 * Created by changsheng on 13/09/2017.
 */

class Util  {

    static void setEsiDataForPrice (ItemPriceVO itemPrice) {
        itemPrice.with {
            if (nowPrice == null) {
                nowPrice = ""
            }
            if (oldPrice == null) {
                oldPrice = ""
            }
            if (highNowPrice == null) {
                highNowPrice = ""
            }
            if (priceTags == null) {
                priceTags = []
            }
            if (priceDesc == null) {
                priceDesc = new PriceTagVO()
            }
            if (prePriceTag == null) {
                prePriceTag = new PriceTagVO()
            }
            if (eventPrice == null) {
                eventPrice = ""
            }
            if (eventPriceDesc == null) {
                eventPriceDesc = new PriceTagVO()
            }
            if (eventTags == null) {
                eventTags = []
            }
            if (backgroundImage == null) {
                backgroundImage = ""
            }
            if (countdownTitle == null) {
                countdownTitle = ""
            }
            if (countdownTitleColor == null) {
                countdownTitleColor = ""
            }
            if (countdown == null) {
                countdown = 0
            }
            if (countdownBackgroundColor == null) {
                countdownBackgroundColor = ""
            }
            if (countdownColor == null) {
                countdownColor = ""
            }
            if (priceType == null) {
                priceType = 0
            }
        }
    }

}