package groovy.bh.xcx.normal

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IFourDependTranslator
import com.mogujie.detail.module.activity.domain.ActivityDO
import com.mogujie.detail.module.groupbuying.constants.TuanBizType
import com.mogujie.detail.module.groupbuying.constants.TuanStatus
import com.mogujie.detail.module.groupbuying.domain.GroupbuyingDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.presale.domain.PresaleDO
import groovy.bh.xcx.base.ItemPriceVO
import groovy.bh.xcx.base.PriceTagVO
import groovy.bh.xcx.base.Util

/**
 * Created by changsheng on 14/03/2017.
 * H5私有模块-普通详情页-价格
 */

@Translator(id = "normalPrice")
class NormalPrice implements IFourDependTranslator<ItemBaseDO, ActivityDO, PresaleDO, GroupbuyingDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, ActivityDO activity, PresaleDO presale, GroupbuyingDO groupbuying) {
        if (!itemBase) {
            return null;
        }
        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

        /**
         * 预售>大促>团购>普通
         */
        if (presale != null) {
            // 预售
            itemPrice.with {
                nowPrice = presale?.totalPrice?.replace("¥", "");
                setOldPriceByRange(itemBase);
                priceTags = [
                        new PriceTagVO(
                                text: "预售价"
                        )
                ];
                eventPrice = presale?.deposit;
                eventPriceDesc = new PriceTagVO(
                        text:  "定金"
                )
            }
        } else if (activity != null && (activity.warmUpPrice || activity.inActivityItem)) {
            // 大促
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                priceTags = activity?.priceDesc ? [
                        new PriceTagVO(
                                text: activity?.priceDesc
                        )
                ] : [
                        new PriceTagVO(
                                text: itemBase?.discountDesc
                        )
                ]
                eventPrice = activity?.warmUpPrice?.price
                eventPriceColor = activity?.warmUpPrice?.color
                if (eventPrice) {
                    eventPriceDesc = new PriceTagVO(
                            text: activity?.warmUpPrice?.priceDesc,
                            bgColor: "#FFFFFF" //无背景色
                    )
                }
            }
        } else if (groupbuying != null && (groupbuying.status == TuanStatus.IN || groupbuying.status == TuanStatus.PRE)) {
            // 团购

            String groupbuyingText = "团购价"
            if (groupbuying.bizType == TuanBizType.UZHI) {
                groupbuyingText = "U质团"
            }
            else if (groupbuying.bizType == TuanBizType.PINPAI) {
                groupbuyingText = "品牌团"
            }

            if (groupbuying.status == TuanStatus.IN) {
                // 团购正式
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    } else {
                        nowPrice = groupbuying?.price;
                    }

                    priceTags = [
                            new PriceTagVO(
                                    text: groupbuyingText
                            )
                    ]


                }

            } else if (groupbuying.status == TuanStatus.PRE) {
                // 团购预热
                itemPrice.with {
                    if (itemBase) {
                        setNowPriceByRange(itemBase);
                        setOldPriceByRange(itemBase);
                    }
                    eventPrice = groupbuying?.price;
                    eventPriceDesc = new PriceTagVO(
                            text:  groupbuyingText
                    )
                }
            }
        } else {
            // 普通
            itemPrice.with {
                setNowPriceByRange(itemBase);
                setOldPriceByRange(itemBase);
                if (!activity?.hideDiscount && itemBase?.discountDesc) {
                    priceTags = [
                            new PriceTagVO(
                                    text: itemBase?.discountDesc
                            )
                    ]
                }
            }
        }


        itemPrice.with {
            // 统一的活动Tag
            eventTags = activity?.eventTags?.collect {
                new PriceTagVO(
                        text: it?.tagText,
                        bgColor: it?.tagBgColor,
                        link: it?.link
                )
            }
        }

        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)


        return itemPrice;
    }
}