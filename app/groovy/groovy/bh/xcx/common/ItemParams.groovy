package groovy.bh.xcx.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.itemParams.domain.ItemParamsDO
import com.mogujie.detail.module.itemParams.domain.ProductInfo
import com.mogujie.detail.module.itemParams.domain.Rule

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-商品参数
 */

@Translator(id = "itemParams")
class ItemParams implements IOneDependTranslator<ItemParamsDO, ItemParamsVO> {

    static class ItemParamsVO {
        ProductInfo info;
        Rule rule;
    }

    @Override
    ItemParamsVO translate(ItemParamsDO input1) {
        if (!input1) {
            return null;
        }
        return new ItemParamsVO(
                info: input1?.info,
                rule: input1?.rule
        );
    }
}