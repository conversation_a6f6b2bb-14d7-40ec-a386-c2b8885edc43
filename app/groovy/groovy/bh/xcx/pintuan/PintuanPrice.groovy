package groovy.bh.xcx.pintuan

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IThreeDependTranslator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.core.util.MaitUtil
import com.mogujie.detail.core.util.NumUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.itemBase.domain.ItemBaseDO
import com.mogujie.detail.module.pintuan.domain.PinTuanDO
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData
import groovy.bh.xcx.base.ItemPriceVO
import groovy.bh.xcx.base.PriceTagVO
import groovy.bh.xcx.base.Util

/**
 * Created by changsheng on 23/03/2017.
 */

@Translator(id = "pintuanPrice")
class PintuanPrice implements IThreeDependTranslator<ItemBaseDO, PinTuanDO, ExtraDO, ItemPriceVO> {

    @Override
    ItemPriceVO translate(ItemBaseDO itemBase, PinTuanDO pinTuan, ExtraDO extra) {

        ItemPriceVO itemPrice = new ItemPriceVO(itemBase);

        if (!itemBase) {
            return null;
        } else if (pinTuan && pinTuan?.tuanType != null) {
            /**
             * 团类型,新人团:1, 普通团:2, 抽奖团:3
             */
            Integer tuanType = pinTuan?.tuanType

            /**
             * 开团数量
             */
            Integer tuanNum = pinTuan?.tuanNum

            Boolean inC1Activity = pinTuan?.inC1Activity

            Integer activityPrice = pinTuan?.activityPrice

            if(!tuanNum) {
                tuanNum = 0
            }

            /**
             * 成功拼团数量
             */
//            Integer successTuanNum = pinTuan?.successTuanNum
            Long successTuanNum = extra?.sales

            String maitPriceColor // 日常价格颜色
            String maitCountdownColor // 日常倒计时颜色
            String maitCountdownBackgroundColor // 日常倒计时背景色
            String maitBackgroundImage // 日常背景图

            String maitPreEventNowPriceColor // 大促预热现价颜色 颜色
            String maitPreEventNowPriceDesc // 大促预热现价说明 文案
            String maitPreEventPriceColor // 大促预热价格颜色 颜色
            String maitPreEventPriceDesc // 大促预热价格说明 文案
            String maitPreEventCountdownTitle // 大促预热倒计时标题 文案
            String maitPreEventCountdownTitleColor // 大促预热倒计时标题颜色 颜色
            String maitPreEventCountdownColor // 大促预热倒计时数字颜色 颜色
            String maitPreEventBgImage // 大促预热氛围背景图 图片

            String maitEventBgImage // 大促正式背景图 图片
            String maitEventTagImage // 大促正式活动标签 图片
            String maitEventPriceColor // 大促正式价格颜色 颜色
            String maitEventCountdownTitle // 大促正式倒计时标题 文案
            String maitEventCountdownTitleColor // 大促正式倒计时标题颜色 颜色
            String maitEventCountdownColor // 大促正式倒计时数字颜色 颜色

            List<Map<String, Object>> maitData = MaitUtil.getMaitData(60130)
            for (Map<String, Object> imgData : maitData) {
                Integer imgTuanType = imgData?.get("tuanType")
                if (imgTuanType == tuanType) {
                    // 日常环境
                    maitPriceColor = imgData?.get("priceColor")
                    maitCountdownColor = imgData?.get("countdownColor")
                    maitCountdownBackgroundColor = imgData?.get("countdownBackgroundColor")
                    maitBackgroundImage = imgData?.get("backgroundImage")

                    // 预热阶段
                    maitPreEventNowPriceColor = imgData?.get("preEventNowPriceColor")
                    maitPreEventNowPriceDesc = imgData?.get("preEventNowPriceDesc")
                    maitPreEventPriceColor = imgData?.get("preEventPriceColor")
                    maitPreEventPriceDesc = imgData?.get("preEventPriceDesc")
                    maitPreEventCountdownTitle = imgData?.get("preEventCountdownTitle")
                    maitPreEventCountdownTitleColor  = imgData?.get("preEventCountdownTitleColor")
                    maitPreEventCountdownColor = imgData?.get("preEventCountdownColor")
                    maitPreEventBgImage  = imgData?.get("preEventBgImage")


                    // 正式阶段
                    maitEventBgImage = imgData?.get("eventBgImage")
                    maitEventTagImage = imgData?.get("eventTagImage")
                    maitEventPriceColor = imgData?.get("eventPriceColor")
                    maitEventCountdownTitle  = imgData?.get("eventCountdownTitle")
                    maitEventCountdownTitleColor  = imgData?.get("eventCountdownTitleColor")
                    maitEventCountdownColor  = imgData?.get("eventCountdownColor")

                    break
                }
            }

            itemPrice.with {

                Boolean isExpire = pinTuan?.isExpire
                Long now = System.currentTimeSeconds()
                Long startTime = pinTuan?.startTime
                Long remainTime = pinTuan?.remainTime

                // 拼团价格
                if (pinTuan?.skuInfo?.lowNowPrice) {
                    nowPrice = pinTuan?.skuInfo?.lowNowPrice
                    highNowPrice = pinTuan?.skuInfo?.highNowPrice?.equals(pinTuan?.skuInfo?.lowNowPrice) ? null : pinTuan?.skuInfo?.highNowPrice
                } else {
                    setNowPriceByRange(itemBase)
                }
                oldPrice = getOldPriceForce(itemBase)

                if (inC1Activity) {
                    priceType = 2
                    // 大促阶段
                    if (now < startTime) {
                        // 预热
                        countdownTitle = maitPreEventCountdownTitle
                        countdown = startTime - now
                        countdownColor = maitPreEventCountdownColor
                        countdownTitleColor = maitPreEventCountdownTitleColor
                        backgroundImage = maitPreEventBgImage
                        eventTagImage = ""

                        // 价格
                        oldPrice = ""
                        priceDesc = new PriceTagVO(
                                text: maitPreEventNowPriceDesc
                        )
                        priceColor = maitPreEventNowPriceColor

                        eventPrice = (null != activityPrice) ? NumUtil.formatNum(activityPrice / 100D) : null
                        eventPriceColor = maitPreEventPriceColor
                        eventPriceDesc = new PriceTagVO(
                                text: maitPreEventPriceDesc
                        )


                    } else if (now > startTime && !isExpire) {
                        // 正式
                        countdownTitle = maitEventCountdownTitle
                        countdown = remainTime
                        countdownColor = maitEventCountdownColor
                        countdownTitleColor = maitEventCountdownTitleColor
                        backgroundImage = maitEventBgImage
                        eventTagImage = maitEventTagImage

                        priceColor = maitEventPriceColor

                    } else {
                        // 结束
                        // 麦田取的价格氛围
                        priceColor = maitPriceColor ?: defaultPriceColor
                        countdownColor = maitCountdownColor ?: defaultCountdownColor
                        countdownBackgroundColor = maitCountdownBackgroundColor ?: defaultCountdownBackgroundColor
                        backgroundImage = maitBackgroundImage ?: defaultBackgroundImage
                    }

                } else {
                    // 日常
                    priceType = 1

                    // 麦田取的价格氛围
                    priceColor = maitPriceColor ?: defaultPriceColor
                    countdownColor = maitCountdownColor ?: defaultCountdownColor
                    countdownBackgroundColor = maitCountdownBackgroundColor ?: defaultCountdownBackgroundColor
                    backgroundImage = maitBackgroundImage ?: defaultBackgroundImage


                    // 拼团倒计时，如果countdown为null则不展示倒计时
                    if (now < startTime) {
                        countdownTitle = "距开始"
                        countdown = startTime - now

                    } else if (now > startTime && !isExpire) {
                        countdownTitle = "距结束"
                        countdown = remainTime
                    }
                }


                // priceTag
                if (inC1Activity) {
                    if (tuanType == 1) {
                        priceTags = [
                                new PriceTagVO(
                                        text: "老带新"
                                ),
                                new PriceTagVO(
                                        text: "${tuanNum}人团"
                                )
                        ]
                    } else if (tuanType == 3) {
                        priceTags = [
                                new PriceTagVO(
                                        text: "抽奖团"
                                ),
                                new PriceTagVO(
                                        text: "${tuanNum}人团"
                                )
                        ]
                    } else {
                        priceTags = [
                                new PriceTagVO(
                                        text: "${tuanNum}人团"
                                )
                        ]
                        if (successTuanNum > 0) {
                            priceTags.add(0, new PriceTagVO(
                                    text: "已团${successTuanNum}件"
                            ))
                        }
                    }
                } else {
                    if (tuanType == 1) {
                        priceTags = [
                                new PriceTagVO(
                                        text: "老带新·${tuanNum}人团"
                                )
                        ]
                    } else if (tuanType == 3) {
                        priceTags = [
                                new PriceTagVO(
                                        text: "抽奖团·${tuanNum}人团"
                                )
                        ]
                    } else {
                        String text = "${tuanNum}人团"
                        if (successTuanNum > 0) {
                            text = "已团${successTuanNum}件·" + text
                        }
                        priceTags = [
                                new PriceTagVO(
                                        text: text
                                )
                        ]
                    }
                }

            }
        } else {
            // 没有拼团信息
            itemPrice.with {
                setNowPriceByRange(itemBase)
                setOldPriceByRange(itemBase)
            }
        }


        /**
         * 覆盖ESI
         */
        Util.setEsiDataForPrice(itemPrice)

        return itemPrice
    }
}