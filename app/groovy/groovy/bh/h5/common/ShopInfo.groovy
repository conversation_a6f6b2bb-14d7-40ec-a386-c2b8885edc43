package groovy.bh.h5.common

import com.mogujie.detail.core.adt.DetailContextHolder
import com.mogujie.detail.core.adt.RouteInfo
import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.constant.Platform
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.shop.domain.DetailShopCategory
import com.mogujie.detail.module.shop.domain.ShopDO
import com.mogujie.detail.module.shop.domain.ShopDsr
import com.mogujie.detail.module.shop.domain.ShopService

/**
 * Created by chang<PERSON><PERSON> on 13/03/2017.
 * H5公共模块-店铺信息
 */

@Translator(id = "shopInfo")
class ShopInfo implements IOneDependTranslator<ShopDO, ShopInfoVO> {

    static class ShopInfoVO {
        List<ShopDsr> score;
        List<ShopService> services;
        List<DetailShopCategory> categories;
        Integer cFans;
        Integer cSells;
        Boolean isMarked;
        Integer cGoods;
        String userId;
        String shopLogo;
        String name;
        String shopId;
        String tag;
        Integer type;
        Map<String, Object> shopHeader;
        Integer level;
        String shopUrl;

        ShopInfoVO(ShopDO shop) {
            RouteInfo routeInfo =  DetailContextHolder.get().getRouteInfo();
            Platform platform = routeInfo.platform;
            if (platform == Platform.WX) {
                this.shopUrl = shop?.shopId ? ("//weixin.meilishuo.com/wx/shop/" + shop?.shopId) : null;
            } else {
                this.shopUrl = shop?.shopId ? ("//m.mogujie.com/v8/meili/shop?shopid=" + shop?.shopId) : null;
            }
            this.score = shop?.score?.grep {
                it?.name != "价格合理"
            };
            this.services = shop?.services;
            this.categories = shop?.categories;
            this.cFans = shop?.cFans;
            this.cSells = shop?.cSells;
            this.isMarked = shop?.isMarked;
            this.cGoods = shop?.cGoods;
            this.userId = shop?.userId;
            this.shopLogo = shop?.shopLogo;
            this.name = shop?.name;
            this.shopId = shop?.shopId;
            this.tag = shop?.tag;
            this.type = shop?.type;
            this.shopHeader = shop?.shopHeader;
            this.level = shop?.level;
        }
    }

    @Override
    ShopInfoVO translate(ShopDO input1) {
        if (!input1) {
            return null
        }
        return new ShopInfoVO(input1);
    }
}