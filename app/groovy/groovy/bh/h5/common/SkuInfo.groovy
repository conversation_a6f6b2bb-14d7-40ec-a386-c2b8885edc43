package groovy.bh.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.sku.domain.PropInfo
import com.mogujie.detail.module.sku.domain.SkuDO
import com.mogujie.detail.module.sku.domain.SkuData

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-SKU信息
 */


@Translator(id = "skuInfo")
public class SkuInfo implements IOneDependTranslator<SkuDO, SkuDO> {

    @Override
    SkuDO translate(SkuDO sku) {
        return sku
    }
}