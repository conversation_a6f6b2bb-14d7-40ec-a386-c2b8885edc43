package groovy.bh.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.IOneDependTranslator
import com.mogujie.detail.module.detail.domain.DetailDO
import com.mogujie.service.tangram.domain.entity.DetailModule

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 13/03/2017.
 * H5公共模块-图文详情
 */

@Translator(id = "detailInfo")
class DetailInfo implements IOneDependTranslator<DetailDO, DetailInfoVO> {

    static class DetailInfoVO {
        String desc;
        List<DetailModule> detailImage;
    }

    @Override
    DetailInfoVO translate(DetailDO input1) {
        if (!input1) {
            return null;
        }
        return new DetailInfoVO(
                desc: input1?.desc,
                detailImage: input1?.detailImage
        );
    }
}