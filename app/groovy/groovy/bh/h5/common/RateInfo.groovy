package groovy.bh.h5.common

import com.mogujie.detail.core.annotation.Translator
import com.mogujie.detail.core.translator.ITwoDependTranslator
import com.mogujie.detail.core.util.ImageUtil
import com.mogujie.detail.module.extra.domain.ExtraDO
import com.mogujie.detail.module.rate.domain.DetailRate
import com.mogujie.detail.module.rate.domain.RateDO
import com.mogujie.detail.module.rate.util.Constants
import com.mogujie.service.rate.domain.tag.RateTag

/**
 * Created by changsheng on 21/03/2017.
 * H5公共模块-商品评价
 */

@Translator(id = "rateInfo")
class RateInfo implements ITwoDependTranslator<RateDO, ExtraDO, RateInfoVO> {

    static class RateInfoVO {
        List<DetailRateVO> list;
        Integer cRate;
        Integer imgTotal;
        List<RateTag> rateTags;
        Long sales;
    }

    static class DetailRateVO {
        RateUserInfoVO user;
        List<String> images;
        String rateId;
        String content;
        Long created;
        Integer isAnonymous;
        String style;
        Boolean isProbation;
        String probation;
        Integer isEmpty;
        DetailRate append;
        String explain;
        String level;
        Boolean canExplain;
        List<String> extraInfo;
        boolean isBuyerShow;
        Map<String, String> sizeInfo;
        Long contentId;
    }

    static class RateUserInfoVO {
        String uid;
        String uname;
        String avatar;
        String profileUrl;
        String tagIndex;
    }

    public static String hideUname(String uname) {
        if ((uname == null || uname.trim().isEmpty())) {
            return "***";
        } else {
            return uname.substring(0, 1) + "***" + uname.substring(uname.size() - 1);
        }
    }

    @Override
    RateInfoVO translate(RateDO rate, ExtraDO extra) {
        if (!rate) {
            return null;
        }
        List<DetailRateVO> rateList = rate?.list?.collect {
            DetailRateVO detailRate = new DetailRateVO(
                    user: it?.getIsAnonymous() != null && it?.getIsAnonymous() == 1 ? new RateUserInfoVO(
                            uid: null,
                            uname: hideUname(it?.user?.uname),
                            avatar: ImageUtil.img(Constants.ANONYMOUS_ICON[(int) (Integer.parseInt(it?.user?.uid) % 6)]),
                            profileUrl: it?.user?.profileUrl,
                            tagIndex: it?.user?.tagIndex
                    ) : new RateUserInfoVO(
                            uid: null,
                            uname: it?.user?.uname,
                            avatar: it?.user?.avatar,
                            profileUrl: it?.user?.profileUrl,
                            tagIndex: it?.user?.tagIndex
                    ),
                    images: it?.images,
                    rateId: it?.rateId,
                    content: it?.content,
                    created: it?.created,
                    style: it?.style,
                    isAnonymous: it?.isAnonymous,
                    probation: it?.probation,
                    isEmpty: it?.isEmpty,
                    append: it?.append,
                    isProbation: it?.isProbation,
                    explain: it?.explain,
                    level: it?.level,
                    canExplain: it?.canExplain,
                    extraInfo: it?.extraInfo,
                    isBuyerShow: it?.isBuyerShow,
                    sizeInfo: it?.sizeInfo,
                    contentId: it?.contentId
            );
            return detailRate;
        }

        if (rate?.rateTags && rate.rateTags.size() > 0 && rate.imgTotal && rate.imgTotal > 0) {
            RateTag imgTag = new RateTag();
            imgTag.setNum(rate.imgTotal);
            imgTag.setEmotion("positive");
            imgTag.setProperty("图片");
            imgTag.setValue("有图片");
            rate.rateTags.add(0, imgTag);
        }

        return new RateInfoVO(
                list: rateList,
                cRate: rate?.cRate,
                imgTotal: rate?.imgTotal,
                rateTags: rate?.rateTags,
                sales: extra?.sales
        )
    }
}