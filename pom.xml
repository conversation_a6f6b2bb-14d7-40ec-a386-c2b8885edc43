<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mogujie.detail</groupId>
    <artifactId>detail-dsl</artifactId>
    <packaging>pom</packaging>
    <version>1.1.0</version>

    <properties>
        <detail.version>1.1.0</detail.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mogujie.detail</groupId>
            <artifactId>spi</artifactId>
            <version>1.1.0.dsl</version>
        </dependency>
        <dependency>
            <groupId>com.mogujie.service</groupId>
            <artifactId>pay-insurance-api</artifactId>
        <version>1.2.43</version>
    </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.16.4</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-biz-bom</artifactId>
                <version>1.0.20</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-middleware-bom</artifactId>
                <version>1.0.20</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mogujie</groupId>
                <artifactId>mogu-third-lib-bom</artifactId>
                <version>1.0.20</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <!--<distributionManagement>-->
    <!--<repository>-->
    <!--<id>snapshots</id>-->
    <!--<name>snapshots</name>-->
    <!--<url>http://maven.mogujie.org/nexus/service/local/repositories/snapshots/content/</url>-->
    <!--</repository>-->
    <!--</distributionManagement>-->

</project>