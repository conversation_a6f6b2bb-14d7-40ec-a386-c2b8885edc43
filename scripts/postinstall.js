#!/usr/bin/env node

var fs = require('fs');
var os = require('os');
var path = require('path');

var moguGlobalPath = path.join(os.homedir(), '.meili');
var renameFile = path.join(__dirname, '../app.json');
var originFile = path.join(__dirname, '../app.default.json');

fs.stat(originFile , function(err, stat){
    if( stat && stat.isFile() ) {
		var appConfig = require(originFile);

		appConfig.moguGlobalPath = moguGlobalPath;

		fs.writeFile(originFile, JSON.stringify(appConfig), function (err) {
		    if (err) return console.log(err);
		    console.log(JSON.stringify(appConfig));
		    console.log('writing to ' + originFile);
		    fs.rename(originFile, renameFile, (err) => {
		        if (err) throw err;
		        console.log('renamed complete');
		    });
		});
    }
});


